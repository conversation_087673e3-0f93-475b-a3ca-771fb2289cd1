import { BaseService } from '@la-pasta/common/base/base.service';
import { Injectable } from '@nestjs/common';
import { ImageBannerRepository } from './repository/images-banner.repository';


@Injectable()
export class ImageBannerService extends BaseService {

  constructor(
    private readonly imageBannerRepository: ImageBannerRepository
  ) {
    super(imageBannerRepository);
  }
  async findBannerByCommercialRegion(query?: QueryOptions) {
    const filter = {
      ...query.filter,
      $or: [
        { commercialRegion: query.filter['commercialRegion']},
        { commercialRegion: [] },
        { commercialRegion: null }
      ]
    };

    try {
      delete filter['commercialRegion'];
      const banners = await this.findAll({ filter });
      return banners;
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des bannières par région commerciale:', error);
      throw new Error('Failed to retrieve banners by commercial region.');
    }
  }
}
