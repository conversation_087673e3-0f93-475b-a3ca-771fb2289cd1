import { forwardRef, Module } from '@nestjs/common';
import { InvitationService } from './invitation.service';
import { InvitationController } from './invitation.controller';
import { NotificationsModule } from '@la-pasta-module/notifications/notifications.module';
import { SnitchsModule } from '@la-pasta-module/snitchs/snitchs.module';
import { UsersModule } from '@la-pasta-module/users';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { InvitationRepository } from './repository/invitation.repository';
@Module({
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => SnitchsModule),
    forwardRef(() => NotificationsModule),
  ],
  providers: [InvitationService, InvitationRepository],
  controllers: [InvitationController],
  exports: [InvitationService, InvitationRepository]
})
export class InvitationModule { }
