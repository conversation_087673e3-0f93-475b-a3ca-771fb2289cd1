import { Injectable } from '@nestjs/common';
import { CreateQrCodeFileManagementDto } from './dto/create-qr-code-file-management.dto';
import { BaseService, t } from '@la-pasta/common';
import { QrCodeFile } from './repository/qr-code-file-management.repository';

@Injectable()
export class QrCodeFileManagementService extends BaseService {

  constructor(private qrCodeFileRepository: QrCodeFile) {
    super(qrCodeFileRepository);
}

   async saveQrCodeFilePdf(createQrCodeFileDto: CreateQrCodeFileManagementDto): Promise<{ message: string }> {
     try {
       await this.qrCodeFileRepository.create(createQrCodeFileDto);
       return {
         message: await t('SAVED'),
       };
     } catch (error) {
       console.error('Error saving PDF:', error);
       return error;
     }
   }
}
