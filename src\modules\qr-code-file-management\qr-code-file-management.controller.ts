import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { QrCodeFileManagementService } from './qr-code-file-management.service';
import { CreateQrCodeFileManagementDto } from './dto/create-qr-code-file-management.dto';
import { GetUser } from '@la-pasta-module/auth';

@Controller('qr-code-file-management')
export class QrCodeFileManagementController {
  constructor(private readonly qrCodeFileManagementService: QrCodeFileManagementService) { }

  @Post()
  async create(@Body() createQrCodeFileManagementDto: CreateQrCodeFileManagementDto) {
    return this.qrCodeFileManagementService.saveQrCodeFilePdf(createQrCodeFileManagementDto);
  }

  @Get()
  async findAllQrCodeFile(@Query() query: QueryFilter, @GetUser() user) {
    return await this.qrCodeFileManagementService?.findAll({ filter: query });
  }

  @Get(':qrCodeFileReference/download')
  async downloadQrCodeFile(@Param('qrCodeFileReference') qrCodeFileReference: string) {
    return await this.qrCodeFileManagementService.findOne({ filter: { qrCodeFileReference } });
  }
}
