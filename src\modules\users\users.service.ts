import { CompaniesService, Company, CompanyCategory } from '@la-pasta-module/companies';
import { OrderAction } from '@la-pasta-module/order';
import {
  CompanyEmployee,
  EmployeeEntity,
  EmployeeType,
} from './entities/user.entity';
import { config } from 'convict-config';
import {
  HttpStatus,
  Injectable,
  NotFoundException,
  Inject,
  ForbiddenException,
  forwardRef,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { BaseService, convertParams, setDateFilter, setResponse, t } from '@la-pasta/common';
import { Profile, ProfileService } from '@la-pasta-module/authorizations';
import {
  AccountCreatedEvent,
  EmployeeCreatedEvent,
  PasswordChangedEvent,
} from './events';
import { BaseUser, User, UserCategory } from './entities';
import { UserRepository } from './repository';
import { CreateUserDto } from './dto';
import { UserRole } from './roles';
import * as bcrypt from 'bcrypt';
import { UserEntity } from '@la-pasta-module/loyalty-program/entities';
import { FirebaseCloud } from '@la-pasta/infrastructures/image';

@Injectable()
export class UsersService extends BaseService {
  constructor(
    private readonly userRepository: UserRepository,
    private profileService: ProfileService,
    @Inject(forwardRef(() => CompaniesService))
    private readonly companiesService: CompaniesService,
    private firebaseCloud: FirebaseCloud,
    @Inject('QUEUE') private queueClient: ClientProxy,
  ) {
    super(userRepository);
  }

  async createUser(user: CreateUserDto | BaseUser) {
    await this.verifyIfUserExiste({
      $or: [{ email: user?.email }, { tel: user?.tel }],
      enable: true
    });

    user.password = await bcrypt.hash(user.password, 10);
    user.roles = this.setRole(user);
    user.authorizations ??= await this.getProfileAuthorizations(user.category);

    this.setUserSpecifications(user);
    if (user?.profilePicture && user.profilePicture.startsWith('data:image/')) {
      user.profilePicture = await this.firebaseCloud.getImageUrl(user?.profilePicture, user?.email);
    }

    const response = await this.create(user);

    this.logger.log(`User created successfully with id: ${response.data}`);

    if (user.category === UserCategory.EmployeeEntity) {
      this.queueClient.emit(
        'employee_created',
        new EmployeeCreatedEvent(
          user.email,
          `${user?.firstName} ${user?.lastName}`,
        ),
      );
    } else {
      this.queueClient.emit(
        'account_created',
        new AccountCreatedEvent(user),
      );
    }

    // 📲 Envoi de SMS après la création de compte
    if (user.tel) {
      this.queueClient.emit('sms_received', {
        receiver: user.tel,
        message: `Bonjour ${user.firstName},\nVotre compte a été créé avec succès. Bienvenue sur Click CADYST ! 🎉\nInstaller via ce lien: ${config.get('appUrl.mobile')}\nMerci de nous faire confiance.`
      });
    }

    return setResponse(HttpStatus.CREATED, t('USER_CREATED'), response?.data);
  }

  async createIndirectUser(user: CreateUserDto | BaseUser) {
    await this.verifyIfUserExiste({ tel: user.tel, enable: true });

    user.roles = this.setRole(user);
    user.authorizations ??= await this.getProfileAuthorizations(user.category);

    this.setUserSpecifications(user);
    if (user?.profilePicture && user.profilePicture.startsWith('data:image/')) {
      user.profilePicture = await this.firebaseCloud.getImageUrl(user?.profilePicture, user?.email);
    }

    const response = await this.create(user);

    this.logger.log(`User created successfully with id: ${response.data}`);

    // 📲 Envoi de SMS après la création de compte
    if (user.tel) {
      this.queueClient.emit('sms_received', {
        receiver: user.tel,
        message: `Bonjour ${user.firstName},\nVotre compte a été créé avec succès. Bienvenue sur Click CADYST ! 🎉\nMerci de nous faire confiance.`
      });
    }

    return setResponse(HttpStatus.CREATED, t('USER_CREATED'), response?.data);
  }


  async getParticularUserByCommercialId(query: QueryOptions) {

    query = convertParams(query);
    if ('startDate' in query?.filter || 'endDate' in query?.filter) {
      query = setDateFilter(query);
    }
    try {
      return await this.userRepository.getParticularUserByCommercialId(query);
    } catch (error) {
      console.log(error);
    }
  }
  // TODO: remove this method after execution of migrate script (surchage)
  async createUserMigration(
    user: CreateUserDto | CompanyEmployee,
  ): Promise<QueryResult> {
    try {
      await this.verifyIfUserExiste({ email: user.email });

      user.roles = this.setRole(user);
      user.authorizations ??= await this.getProfileAuthorizationsForMigration(
        user.category === UserCategory.CompanyUser
          ? user.company.category
          : user.category,
      );

      this.setUserSpecifications(user);

      await this.create(user);

      return setResponse(HttpStatus.CREATED, t('USER_CREATED'));
    } catch (error) {
      return error;
    }
  }

  // TODO: remove this method after execution of migrate script (surchage)
  private async getProfileAuthorizationsForMigration(category: number) {
    const query = {
      label: CompanyCategory[category]
        ? CompanyCategory[category].toLowerCase()
        : UserCategory[category].toLowerCase(),
    };
    const profile = (await this.profileService.findOne({
      filter: query,
    })) as unknown as Profile;

    if (profile instanceof Error)
      throw new Error(t('error.NO_CATEGORY_AUTHORIZATION'));

    return profile.authorizations;
  }

  async updateUser(id: string, user: Partial<User>) {
    if ('password' in user)
      user.password = await bcrypt.hash(user.password, 10);

    // if ((user as EmployeeEntity)?.associatedCompanies) {
    //   const particularId = user['associatedCompanies'].filter(item => compa
    // }

    return await this.update({ _id: id }, user);
  }

  async resetPassword(id: string, password: string) {
    const user = (await this.findOne({
      filter: { _id: id },
    })) as unknown as BaseUser;

    user.password = await bcrypt.hash(password, 10);

    await this.update({ _id: user._id }, user);

    this.queueClient.emit(
      'password_changed',
      new PasswordChangedEvent(
        user.email,
        `${user?.firstName} ${user?.lastName}`,
        password,
      ),
    );

    return setResponse(HttpStatus.OK, t('PASSWORD_RESET'));
  }

  async updateUserPoints(userId: string, points: number) {
    try {
      return await this.userRepository.updateUserPoints(userId, points);
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }
  async updateUserValidePoints(userId: string, validePoints: number) {
    try {
      return await this.userRepository.updateUserValidatePoints(userId, validePoints);
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async getDataLoyaltyProgram(filter: QueryFilter): Promise<UserEntity[]> {
    const aggregate = [
      {
        $match: {
          ...filter
        },
      },
      {
        $lookup:
        {
          from: "rewards",
          localField: "rewardId",
          foreignField: "_id",
          as: "advantages"
        }
      }
    ]
    return await this.findAllAggregate(aggregate) as UserEntity[];
  }

  private setRole(user: User) {
    if (user.category === UserCategory.Administrator) {
      return [UserRole.BACKOFFICE];
    }

    if (user.category === UserCategory.Commercial) {
      return [UserRole.BACKOFFICE, UserRole.CLIENT];
    }

    return [UserRole.CLIENT];
  }

  private async getProfileAuthorizations(category: number) {
    const profile = (await this.profileService.findOne({
      filter: { label: UserCategory[category].toLowerCase() },
    })) as unknown as Profile;

    if (profile instanceof Error)
      throw new NotFoundException(t('error.NO_CATEGORY_AUTHORIZATION'));

    return profile.authorizations;
  }

  private async verifyIfUserExiste(query: QueryFilter) {
    const existVerify = await this.userRepository.findOne({ filter: query });
    if (existVerify) throw new ForbiddenException(`Ce compte existe déjà`);
  }

  private setUserSpecifications(user: User) {
    if ('isRetired' in user) {
      this.EmployeeEntitySpecifications(user);
    }

  }

  EmployeeEntitySpecifications(user: EmployeeEntity) {
    user.isValidated = false;
    user.enable = false;
    user.tonnage = !user.isRetired
      ? {
        capacity: config.get('tonnage.active.capacity'),
        capacityPerYear: config.get('tonnage.active.capacityPerYear'),
        // capacityLeft: config.get('tonnage.active.capacityPerYear')
      }
      : {
        capacity: config.get('tonnage.retired.capacity'),
        capacityPerYear: config.get('tonnage.retired.capacityPerYear'),
        // capacityLeft: config.get('tonnage.retired.capacityPerYear')
      };

    if (
      user.employeeType === EmployeeType.CORDO_RH ||
      user.employeeType === EmployeeType.DRH
    ) {
      user.roles.push(UserRole.BACKOFFICE);
      user.authorizations.push(
        OrderAction.VALIDATE,
        OrderAction.VIEW_EMPLOYEES,
      );
    }
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async migrateUserToOtherCompany(userId: string, companyId: string) {
    const company = await this.getCompanyById(companyId);

    return await this.updateUserCompany(userId, company);
  }

  private async getCompanyById(companyId: string) {
    const company = await this.companiesService.findOne({ filter: { _id: companyId } });
    if (!company) {
      throw new NotFoundException(t('NOT_FOUND'));
    }
    return company;
  }

  private async updateUserCompany(userId: string, company: any) {
    return await this.update(
      { _id: userId },
      {
        company: {
          _id: company._id.toString(),
          name: company.name,
          category: company.category,
        },
      }
    );
  }
}
