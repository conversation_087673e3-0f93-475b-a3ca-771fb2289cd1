import { User } from "@la-pasta-module/users";

export class AuthorizationRemoval {
    _id?: string;
  LoadNumber?: string;
  SoldToDescription?: string;
  DateRequestedShipment?: Date;
  RequestedTimeFrom?: any;
  data?: any;
  ItemNumber?: any;
  BusinessUnit?: string;
  RequestedDate?: string;
  DeliveryNote?: string;
  DeliveryNoteInstruction?: string;
  FreightHandlingCode?: string;
  ItemNumber1?: string;
  LastStatusCode?: string;
  Instruction1?: string;
  LoadStatus?: string;
  StoreLabel?: string;
  ModeofTransport?: string;
  NextStatusCode?: string;
  OrderTypeDescription?: string;
  PrimaryVehicleID?: string;
  PromisedDeliveryDate?: string;
  PromisedDeliveryDate1?: Date;
  PromisedShipmentDate?: string;
  QuantityShipped1?: number;
  SalesOrderNumber?: string;
  ShipTo?: number;
  ShipToDescription?: string;
  ShipmentNumber?: string;
  ShipmentStatus?: number;
  ShortItemNumber?: string;
  UnitofMeasure?: string;
  VehicleType?: string;
  ZoneNumber?: string;
  TransactionQuantity?: number;
  QuantityShipped?: string;
  LineNumber?:string;
  updateDateRequested?: string;
  OrderCreationTime?: number;
  status?: StatusRemovals;
  carrier?: any
  dates?: {
    created: number;
  };
  SoldTo?: string | any;
  enable?: boolean;
  departureTime?: number;
  dockTimeStart?: number;
  dockCode?: number;
  parkTime?: number;
  carrierName?: string;
  carrierPhone?: string;
  carrierIdCard?:string;
  carrierPlateNumber?: string;
  carrierTruckCategory?: string;
  carrierDriverLicense?: string;
  associatedCommercial?: Partial<User>;
  userPhoneNumber?: number;
  userEmailAddress?: string;
  
}

export enum AuthorizationRemovalStatus {
    NOTAPPROVED = 21,
    APPROVED = 29,
    SEIZURE = 35,
    AUTPUT = 50,
    INVOICED = 80,
    REJECTED = 99,
    WAITING = 100,

}

export enum StatusAES {
    NOTAPPROVED = "21",
    APPROVED = "29",
    CHECK_IN = "35",
    OUTPUT = "50",
    LOADSTATUS60 = "60",
    INVOICED = "80",
    REJECTED = "99",
}

export enum RenderType {
    PICKUP = 'C9',
    RENDER = 'A0',
}

export const RenderTypeLibrary: { [key: string]: string } = {
    [RenderType.PICKUP]: "PICK UP",
    [RenderType.RENDER]: "RENDU",
}

export const StatusAESLibrary: { [key: number]: string } = {
    [StatusAES.CHECK_IN]: "En saisie",
    [StatusAES.OUTPUT]: "Sortie livraison",
    [StatusAES.LOADSTATUS60]: `Au chargement partielle`,
    [StatusAES.INVOICED]: "Facturée",
    [StatusAES.REJECTED]: "Rejetée",
    [StatusAES.APPROVED]: "Créer et approuvée",
    [StatusAES.NOTAPPROVED]: "Non approuvée",


}

export enum StatusRemovals {
    NOT_ASSIGNED = 100,
    ASSIGNED = 200,
    WAITING_VALIDATION = 300,
    QUEUED = 400,
    ON_QUAY = 450,
    LOADED = 500,
    BROKEN_DOWN = 600,
    NEW_PARKING = 700,
    REJECTED = 800,
  }