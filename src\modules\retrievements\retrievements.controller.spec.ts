import { Test, TestingModule } from '@nestjs/testing';
import { RetrievementsController } from './retrievements.controller';
import { RetrievementsService } from './retrievements.service';

describe('RetrievementsController', () => {
  let controller: RetrievementsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RetrievementsController],
      providers: [RetrievementsService],
    }).compile();

    controller = module.get<RetrievementsController>(RetrievementsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
