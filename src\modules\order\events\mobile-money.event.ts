import { PaymentReloadBalance } from "@la-pasta-module/reload-balance/entities/reload-balance.entity";
import { Order, Payment } from "../entities"
export class MobileMoneyEventData {
  paymentInfo: Payment;
  amount: number;

  constructor(data: DataMobileMoney) {
    this.paymentInfo = (data.payment as Payment);
    this.amount = data.amount;
  }
}

export interface DataMobileMoney {
  payment: Payment | PaymentReloadBalance;
  amount: number;
}
export class MobileMoneyEvent extends MobileMoneyEventData {
  order: { _id: string; appReference: string };
  paymentInfo: Payment;
  amount: number;

  constructor(order: Partial<Order>) {
    super({ payment: order.payment, amount: order.cart.amount.TTC })
    this.order = {
      _id: order._id,
      appReference: order.appReference,
    };
  }
}
