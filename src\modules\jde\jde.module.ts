import { Module, forwardRef } from '@nestjs/common';
import { JdeService } from './jde.service';
import { Jde<PERSON>ontroller } from './jde.controller';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { CompaniesModule, CompaniesService } from '@la-pasta-module/companies';

@Module({
  imports: [
    ClientsModule.register([
      { name: "JDE", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
    ]),
    forwardRef(() => CompaniesModule),
  ],
  controllers: [JdeController],
  providers: [JdeService],
  exports: [JdeService]
})
export class JdeModule { }
