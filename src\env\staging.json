{"env": "staging", "port": 3001, "host": "api", "db": {"host": "mongodb", "name": "la-pasta", "auth": {"user": "", "password": ""}}, "db_images": {"host": "mongodb", "name": "la-pasta", "auth": {"user": "", "password": ""}}, "jwt": {"secret": "n1l894y7c0br3k7", "expiration": "168h"}, "baseUrl": "https://lapasta.londo-tech.com/", "basePath": "api/v3", "queue": {"port": 3000, "host": "queue-api"}, "payment": {"port": 3000, "host": "payment-api"}, "jde": {"port": 3000, "host": "jde-api"}, "eexTransaction": {"login": "express_exchange_mcm", "password": "NiM{FVCa&aiiK$hZ6Nj!mQZ#any8@"}, "rabbitmq": {"hostname": "dev-backoffice.mycimencam.com", "port": 8147, "protocol": "amqps", "username": "londo", "password": "@pXeClYXziDTdUHZ5t33gJ6dtsDfq64E7FgAHPm3ItUqi74Tlq3MlQWPdJ5hYJYHsu6k8Y5"}, "fluentdHost": "**************", "gotenbergUrl": "http://gotenberg:3000", "isMockOtp": true, "isDisableOrderProcess": false}