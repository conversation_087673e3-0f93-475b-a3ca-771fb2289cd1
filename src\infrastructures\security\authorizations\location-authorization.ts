
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';
import { LocationAction } from '@la-pasta-module/locations/actions';

class LocationAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      LocationAction.CREATE,
      LocationAction.DELETE,
      LocationAction.UPDATE,
      LocationAction.VIEW] as string[]).includes(permission) && user?.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (!user.enable) return false;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const LocationAuthorizationInstance = new LocationAuthorization();