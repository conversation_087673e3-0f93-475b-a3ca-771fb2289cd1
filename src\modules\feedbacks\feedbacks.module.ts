import { Module } from '@nestjs/common';
import { FeedbacksService } from './feedbacks.service';
import { FeedbacksController } from './feedbacks.controller';
import { FeedbackRepository } from './repository';
import { FirebaseCloud } from 'src/infrastructures/image';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { NotificationsModule } from '@la-pasta-module/notifications/notifications.module';

@Module({
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
    NotificationsModule
  ],
  controllers: [FeedbacksController],
  providers: [FeedbacksService, FeedbackRepository, FirebaseCloud],
  exports: [FeedbacksService, FeedbackRepository]

})
export class FeedbacksModule { }
