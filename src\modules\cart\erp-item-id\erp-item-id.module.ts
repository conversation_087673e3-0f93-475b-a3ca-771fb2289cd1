import { Module } from '@nestjs/common';
import { ErpItemIdService } from './erp-item-id.service';
import { ErpItemIdController } from './erp-item-id.controller';
import { ErpItemIdRepository } from './repository';

@Module({
  controllers: [ErpItemIdController],
  providers: [ErpItemIdService, ErpItemIdRepository],
  exports: [ErpItemIdService, ErpItemIdRepository],
})
export class ErpItemIdModule {}
