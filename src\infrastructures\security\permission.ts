import { t } from '@la-pasta/common';
import { ForbiddenException } from '@nestjs/common';
import { User } from 'src/modules/users/entities';
import {
  AuthorizationInterface,
  UserAuthorizationInstance,
  CompnayAuthorizationInstance,
  OrderAuthorizationInstance,
  PaymentAuthorizationInstance,
  PriceAuthorizationInstance,
  PlanificationAuthorizationInstance,
  UnitAuthorizationInstance,
  StoreAuthorizationInstance,
  ProductAuthorizationInstance,
  PackagingAuthorizationInstance,
  ShippingAuthorizationInstance,
  ErpItemIdAuthorizationInstance,
  ReloadBalanceAuthorizationInstance,
  OthersMenuAuthorizationInstance,
  LocationAuthorizationInstance,
  FaqAuthorizationInstance,
  TechnicalAuthorizationInstance,
  LogoAuthorizationInstance,
  PromoCodeAuthorizationInstance,
  FeedbackAuthorizationInstance,
} from './authorizations';
import { CategoryAuthorizationInstance } from './authorizations/category-authorization';
import { OptionAuthorizationInstance } from './authorizations/option-authorization';
import { ItemsAuthorizationInstance } from './authorizations/items-authorization';
import { OrderItemsAuthorizationInstance } from './authorizations/order-items-authorization';
import { ScannerDataAuthorizationInstance } from './authorizations/scanner-data-authorizations';
import { orderSupplierAuthorizationInstance } from './authorizations/order-supplier-authorization';
import { QrCodeAuthorizationInstance } from './authorizations/qr-code-authorization';
import { ReportingAuthorizationInstance } from './authorizations/reporting-authorization';
import { WholeSaleAuthorizationInstance } from './authorizations/whole-sale-authorization';


export class Permission {
  private static authorization: AuthorizationInterface;

  static userAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(UserAuthorizationInstance);

    this.can(user, action, subject);
  }

  static companyAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(CompnayAuthorizationInstance);

    this.can(user, action, subject);
  }

  static orderAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(OrderAuthorizationInstance);

    this.can(user, action, subject);
  }

  static paymentAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(PaymentAuthorizationInstance);

    this.can(user, action, subject);
  }

  static priceAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(PriceAuthorizationInstance);

    this.can(user, action, subject);
  }

  static planificationAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(PlanificationAuthorizationInstance);

    this.can(user, action, subject);
  }

  static unitAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(UnitAuthorizationInstance);

    this.can(user, action, subject);
  }

  static storeAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(StoreAuthorizationInstance);

    this.can(user, action, subject);
  }

  static locationAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(LocationAuthorizationInstance);

    this.can(user, action, subject);
  }


  static faqAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(FaqAuthorizationInstance);

    this.can(user, action, subject);
  }

  static categoryAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(CategoryAuthorizationInstance);

    this.can(user, action, subject);
  }

  static optionAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(OptionAuthorizationInstance);

    this.can(user, action, subject);
  }

  static productAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(ProductAuthorizationInstance);

    this.can(user, action, subject);
  }

  static technicalAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(TechnicalAuthorizationInstance);

    this.can(user, action, subject);
  }

  static feedbackAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(FeedbackAuthorizationInstance);

    this.can(user, action, subject);
  }

  static itemsAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(ItemsAuthorizationInstance);

    this.can(user, action, subject);
  }

  static orderItemsAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(OrderItemsAuthorizationInstance);

    this.can(user, action, subject);
  }

  static scannerDataAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(ScannerDataAuthorizationInstance);

    this.can(user, action, subject);
  }

  static orderSupplierAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(orderSupplierAuthorizationInstance);

    this.can(user, action, subject);
  }

  static qrCodeAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(QrCodeAuthorizationInstance);

    this.can(user, action, subject);
  }

  static logoAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(LogoAuthorizationInstance);

    this.can(user, action, subject);
  }

  static promoCodeAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(PromoCodeAuthorizationInstance);

    this.can(user, action, subject);
  }

  static packagingAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(PackagingAuthorizationInstance);

    this.can(user, action, subject);
  }

  static shippingAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(ShippingAuthorizationInstance);

    this.can(user, action, subject);
  }

  static erpItemIdAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(ErpItemIdAuthorizationInstance);

    this.can(user, action, subject);
  }

  static ReloadBalanceAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(ReloadBalanceAuthorizationInstance);

    this.can(user, action, subject);
  }

  static MenuOthersAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(OthersMenuAuthorizationInstance);

    this.can(user, action, subject);
  }

  static ReportingAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(ReportingAuthorizationInstance);

    this.can(user, action, subject);
  }

  private static can(user: User, action: string, subject: any) {
    const support = this.authorization.support(user, action);
    const authorise = this.authorization.authorise(user, action, subject);
    if (
      !this.authorization.support(user, action) ||
      !this.authorization.authorise(user, action, subject)
    )
      throw new ForbiddenException(t('error.NOT_AUTHORIZE'));
  }

  private static defineAuthorization(authorization: AuthorizationInterface) {
    this.authorization = authorization;
  }

  static wholeSaleAuthorization(user: User, action: string, subject?: any) {
    this.defineAuthorization(WholeSaleAuthorizationInstance);

    this.can(user, action, subject);
  }
}
