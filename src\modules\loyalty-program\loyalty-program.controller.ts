import { Controller, Get, Post, Body, Param, UseGuards, Query } from '@nestjs/common';
import { LoyaltyProgramService } from './loyalty-program.service';
import { CreateLoyaltyProgramDto } from './dto/create-loyalty-program.dto';
import { JwtGuard } from '@la-pasta-module/auth';
import { getUser } from '@la-pasta/common';

@Controller('loyalty-program')
export class LoyaltyProgramController {
  constructor(private readonly loyaltyProgramService: LoyaltyProgramService) { }

  @Post()
  create(@Body() createLoyaltyProgramDto: CreateLoyaltyProgramDto) {
    return this.loyaltyProgramService.create(createLoyaltyProgramDto);
  }

  @Get()
  findAll(@Query() query: QueryFilter) {
    return this.loyaltyProgramService.findAll();
  }

  @Get('points')
  @UseGuards(JwtGuard)
  async getPoints(@Query() query: QueryFilter) {
    return await this.loyaltyProgramService.getPoints(getUser(), query);
  }

  @Get('benefit-rewards/:id')
  @UseGuards(JwtGuard)
  async getBenefitRewards(@Param() id: string) {
    return await this.loyaltyProgramService.getBenefitReward(id);
  }
}
