import { Packaging } from "@la-pasta-module/cart/packagings/entities";
import { Product } from "@la-pasta-module/cart/products/entities";
import { IsArray, IsNotEmpty } from "class-validator";
import { QrCodeData } from "../entities/qr-code.entity";

export class CreateQrCodeDto {
    @IsNotEmpty()
    @IsArray()
    qrCodeData: QrCodeData[];

    @IsNotEmpty()
    product: Partial<Product>;

    @IsNotEmpty()
    packaging: Partial<Packaging>;

    @IsNotEmpty()
    user_id: string;

    @IsNotEmpty()
    qrCodeFileReference: string;

    @IsNotEmpty()
    qrCount: number;

}

export class CreateManyQrCodeDto {
    @IsNotEmpty()
    @IsArray()
    qrCodesData: QrCodeData[];
}
