
import { OrderItemsAction } from '@la-pasta-module/order-items/actions';
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';


class OrderItemsAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      OrderItemsAction.DELETE,
      OrderItemsAction.CREATE,
      OrderItemsAction.UPDATE,
      OrderItemsAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == OrderItemsAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const OrderItemsAuthorizationInstance = new OrderItemsAuthorization();