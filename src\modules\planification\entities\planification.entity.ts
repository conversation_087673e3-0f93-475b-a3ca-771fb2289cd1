import { Packaging, Product, Store } from "@la-pasta-module/cart";
import { CompanyCategory } from "@la-pasta-module/companies";

export class Planification {
  _id?: string;
  id: number;
  title: string;
  start: string;
  end: string;
  data: PlanificationData;
}

export interface PlanificationData {
  store: Store;
  product: Product;
  quarter: string;
  packaging: Partial<Packaging>
  quantity: number;
  quantityLeft: number;
  categorieShare: {
    category: CompanyCategory;
    quantity: number;
  };
  clientShare: {
    company: {
      _id: string;
      name: string;
    };
    quantity: number;
  };
  start: string;
  end: string;
}