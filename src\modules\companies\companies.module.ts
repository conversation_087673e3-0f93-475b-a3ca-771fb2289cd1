import { forwardRef, Module } from '@nestjs/common';
import { CompaniesService } from './companies.service';
import { CompaniesController } from './companies.controller';
import { CompanyRepository } from './repository';
import { UsersModule } from '@la-pasta-module/users';
import { AuthorizationsModule } from '@la-pasta-module/authorizations';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { OrderModule } from '@la-pasta-module/order';
import { FirebaseCloud } from '@la-pasta/infrastructures/image';
import { LogoModule } from '@la-pasta-module/logo/logo.module';
import { BalanceModule } from '@la-pasta-module/balance/balance.module';
import { BalanceService } from '@la-pasta-module/balance/balance.service';
import { BalanceRepository } from '@la-pasta-module/balance/repository';

@Module({
  imports: [
    ClientsModule.register([
      { name: "<PERSON><PERSON>", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => BalanceModule),
    forwardRef(() => OrderModule),
    forwardRef(() => LogoModule),
    AuthorizationsModule],

  controllers: [CompaniesController],
  providers: [CompaniesService, CompanyRepository, FirebaseCloud],
  exports: [CompaniesService, CompanyRepository]
})
export class CompaniesModule { }
