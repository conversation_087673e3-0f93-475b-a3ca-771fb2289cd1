import { Controller, Get, Post, Body, Patch, Param, UseGuards, Query } from '@nestjs/common';
import { ScannerDataService } from './scanner-data.service';
import { CreateScannerDataDto } from './dto/create-scanner-data.dto';
import { UpdateScannerDataDto } from './dto/update-scanner-data.dto';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { BaseUser } from '@la-pasta-module/users';
import { ScannerDataAction } from './actions';
import { getUser } from '@la-pasta/common';
import { Permission } from '@la-pasta/infrastructures/security';


@Controller('scanner-data')
export class ScannerDataController {
  constructor(private readonly scannerDataService: ScannerDataService) { }


  @Post()
  @UseGuards(JwtGuard)
  async create(@Body() createScannerDataDto: CreateScannerDataDto) {
    // Permission.scannerDataAuthorization(getUser(), ScannerDataAction.CREATE);
    return await this.scannerDataService.createScannerData(createScannerDataDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  async findAllScannerData(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    // Permission.scannerDataAuthorization(user, ScannerDataAction.VIEW);
    return await this.scannerDataService.findAll({ filter: query });
  }

  @Get('volume-order-by-particular-client')
  @UseGuards(JwtGuard)
  async getVolumeOrderByParticularClient(@Query() query: QueryFilter) {
    return await this.scannerDataService.getVolumeOrderByParticularClient({ filter: query });
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  async findOneScannerData(@Param('id') id: string, @GetUser() user: BaseUser) {
    // Permission.scannerDataAuthorization(user, ScannerDataAction.VIEW);
    return await this.scannerDataService.findOne({ filter: { _id: id } });
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async updateScannerData(@Param('id') id: string, @Body() body: UpdateScannerDataDto) {
    // Permission.scannerDataAuthorization(getUser(), ScannerDataAction.UPDATE, { _id: id });
    return await this.scannerDataService.update({ filter: { _id: id } }, body);
  }

}
