import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '@la-pasta-module/auth';
import { InvitationService } from '../invitation/invitation.service';
import { CreateInvitationDto } from './dto/create-invitation.dto';
import { UpdateInvitationDto } from './dto/update-invitation.dto';

@ApiTags('Invitations')
@Controller('invitations')
export class InvitationController {
  constructor(private readonly invitationService: InvitationService) { }

  @ApiBody({ type: CreateInvitationDto })
  @UseGuards(JwtGuard)
  @Post(':id/invite-referral')
  async inviteReferral(@Param('id') id: string, @Body() createInvitationDto: CreateInvitationDto) {
    return await this.invitationService.createInvitation(id, createInvitationDto);
  }

  @UseGuards(JwtGuard)
  @Get(':id/referral')
  async findOne(@Param('id') id: string) {
    return await this.invitationService.findOne({ filter: { _id: id } });
  }

  @UseGuards(JwtGuard)
  @Patch(':id/accept')
  async updateInvitation(
    @Param('id') id: string,
    @Body() updateInvitationDto: UpdateInvitationDto
  ) {
    return await this.invitationService.acceptInvitation(id, updateInvitationDto.referredUserId);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    return await this.invitationService.findAll({ filter: query });
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.invitationService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }
  
  @Get('pending-invitation')
  @UseGuards(JwtGuard)
  async getPendingInvitation(@Query() query: QueryFilter) {
    return await this.invitationService.findOne({ filter: query });;
  }
}
