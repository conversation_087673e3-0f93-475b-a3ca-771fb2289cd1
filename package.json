{"name": "la-pasta-api", "version": "3.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build && npm run copy-files", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": " cross-env NODE_ENV=development npm run authorization:check && nest start --watch", "start:dev": "npm run authorization:check && nest start --watch", "start:debug": "nest start --debug --watch", "start:staging": "cross-env NODE_ENV=staging npm run authorization:check && cross-env NODE_ENV=staging node src/main.js", "start:prod": "cross-env NODE_ENV=production npm run authorization:check && cross-env NODE_ENV=production node src/main.js", "copy-files": "copyfiles -u 1 src/**/*.json src/**/*.html src/**/*.crt src/**/*.key src/**/*.jpeg src/**/*.png src/**/*.xlsx dist/src/ && copyfiles public /dist/public && copyfiles views dist/views", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "authorization:check": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/authorizations/check-authorizations", "authorization:insert": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/authorizations/insert-authorizations", "authorization:insert:profiles": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/authorizations/insert-profiles-authorizations", "directions:insert": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/insert-directions", "mock:insert": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/insert-test-data", "file:insert": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/insert-file", "file:insert:units": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/units", "file:insert:packagings": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/packagings", "file:insert:categories": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/categories", "file:insert:products": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/products", "file:insert:stores": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/stores", "file:insert:companies": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/companies", "file:insert:shippings": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/shippings", "file:insert:users": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/users", "file:insert:prices": "node -r ts-node/register/transpile-only -r tsconfig-paths/register scripts/single-files-scripts/prices"}, "dependencies": {"@faker-js/faker": "^7.5.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^9.0.0", "@nestjs/mapped-types": "2.0.6", "@nestjs/microservices": "^9.3.10", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.4.3", "@nestjs/serve-static": "^3.0.0", "@nestjs/swagger": "^6.0.5", "@types/cache-manager": "^4.0.6", "@types/cors": "^2.8.17", "@types/fs-readfile-promise": "^3.0.1", "@types/glob": "^8.0.0", "@types/image-to-base64": "^2.1.0", "@types/mongoose": "^5.11.97", "amqp-connection-manager": "^4.1.11", "amqplib": "^0.10.3", "axios": "^1.7.2", "basic-auth": "^2.0.1", "bcrypt": "^5.1.1", "body-parser": "^1.20.0", "bson": "^6.10.1", "cache-manager": "5.7.6", "cache-manager-fs-hash": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cli-spinners": "^2.7.0", "convict": "^6.2.3", "cors": "^2.8.5", "cross-env": "^7.0.3", "deep-object-diff": "^1.1.9", "firebase": "^9.9.2", "fluent-logger": "^3.4.1", "fs-extra": "^11.3.0", "fs-readfile-promise": "^3.0.1", "glob": "^8.0.3", "glob-promise": "^5.0.0", "googleapis": "^140.0.0", "gotenberg-js-client": "^0.7.4", "handlebars": "^4.7.7", "hbs": "^4.2.0", "helmet": "^5.1.1", "image-to-base64": "^2.2.0", "json-beautify": "^1.1.1", "la-pasta-api": "file:", "moment": "^2.29.4", "mongodb": "^6.12.0", "mongoose": "^8.10.1", "morgan": "^1.10.0", "nest-winston": "^1.9.4", "nestjs-i18n": "^9.1.10", "nestjs-pino": "^3.1.1", "nestjs-request-context": "^2.0.1", "nestjs-rmq": "^2.8.0", "node-fetch": "^2.7.0", "oauth": "^0.10.0", "oauth-token": "^2.0.1", "ora-classic": "^5.4.2", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "pino-http": "^8.2.0", "pino-pretty": "^8.1.0", "puppeteer": "^22.10.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.8.2", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "url-crypt": "^1.2.1", "webpack": "^5.74.0", "winston": "^3.10.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/basic-auth": "^1.1.3", "@types/bcrypt": "^5.0.0", "@types/bs58": "^4.0.4", "@types/convict": "^6.1.1", "@types/express": "^4.17.13", "@types/jest": "28.1.4", "@types/lodash": "^4.14.197", "@types/morgan": "^1.9.5", "@types/node": "^16.11.59", "@types/node-fetch": "^2.6.2", "@types/passport-jwt": "^3.0.6", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "copyfiles": "^2.4.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.2", "jest-environment-node": "^29.0.3", "nodemon": "^3.0.1", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "tslib": "^2.4.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "./", "modulePaths": ["<rootDir>"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "jest-environment-node"}}