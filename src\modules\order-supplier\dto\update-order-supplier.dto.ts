import { PartialType } from '@nestjs/swagger';
import { CreateOrderSupplierDto } from './create-order-supplier.dto';
import { IsNumber, IsOptional } from 'class-validator';
import { Company } from '@la-pasta-module/companies/entities';
import { Cart } from '@la-pasta-module/price/compute_price/entities';
import { Particular, User } from '@la-pasta-module/users/entities';
import { OrderStatus } from '@la-pasta-module/order/entities/order.entity';
import { OrderSupplier } from '../entities/order-supplier.entity';

export class UpdateOrderSupplierDto extends PartialType(CreateOrderSupplierDto) {

  @IsOptional()
  orders: OrderSupplier;

}
