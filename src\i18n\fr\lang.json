{"CREATED": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "SAVED": "<PERSON><PERSON><PERSON> enregis<PERSON>ée avec succès", "UPDATED": "<PERSON><PERSON><PERSON> modifi<PERSON> avec succès", "SUCCESS_OPERATION": "Operation effectuée avec succès", "USER_CREATED": "Utilisateur c<PERSON>é avec succès", "PASSWORD_RESET": "Mot de passe réinitialisé avec succès", "PAYMENT_INIT": "Votre commande a été envoyée en validation ; vous serez notifié de son l'évolution.", "PAYMENT_RETAIL_INIT": "Votre commande a été envoyée en validation", "NOTIF_CREATED": "Notification créée avec succès.", "NOTIF_REMOVED_FROM_USER": "Notification retiré avec succès", "NOTIF_DELETED": "Notification supprimé avec succès", "NOTIFS_DELETED": "Notifications supprimé avec succès", "VALIDATE_ORDER_RETAILER_AND_CREATE_AE": "Commande validée avec succès et AE crée avec success", "VALIDATE_ORDER_RETAILER": "Commande Transmise à la logistique avec succès", "REJECT_ORDER": "Commande rejet<PERSON>", "CANCEL_ORDER": "Commande annulée", "VALIDATE_ORDER": "Commande validée", "IMAGE_CREATED": "Image créé avec succès", "TRANSACTION_CREATED": "Votre transaction a été enregistrée avec le statut", "IMAGE_UPDATED": "mise a jour de l'image avec success", "EXECEDE_CAPACITY_ANNUAL": "Vous avez dépassé votre quota annuel de produit", "error": {"NO_ACCOUNT": "Aucun compte n'est associé à cette adresse", "NO_COMPANY": "Aucune compagnie trouvée dans le systeme", "NO_PROMO_CODE": "Ce code promo n'existe pas.", "PROMO_CODE_DATE_NOT_VALID": "Le code promo a expiré.", "PROMO_CODE_NOT_VALID": "Le code promo n'est pas valide", "INVALID_TOKEN": "To<PERSON> de réinitialisati<on invalide", "NO_RESET_REQUEST": "Aucune demande de réinitialisation du mot de passe trouvé pour ce compte", "BAD_CREDENTIAL": "Identifiants incorrects", "ON_CREATION": "Une erreur s'est produite lors de la création", "ON_UPDATE": "Une erreur s'est produite lors de la modification", "NOT_FOUND": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "USER_NOT_FOUND": "Aucun utilisateur trouvée pour ce client", "UNIT_NOT_FOUND": "Aucune unité trouvée pour la valeur envoyée", "NO_CATEGORY_AUTHORIZATION": "Aucune autorisation n'est associée à cette catégorie", "PRICE_EXISTE": "Cette offre de prix existe déjà", "CATEGORY_EXIST": "Cette catégorie existe déjà", "SHIPPING_EXIST": "Cette addresse de livraison existe déjà", "ACCOUNT_EXISTE": "Ce compte existe déjà", "NOT_AUTHORIZE": "Vous n'avez pas l'autorisation pour effectuer cette action", "EXPIRE_TIME": "Temps de validité du code expiré", "NOT_ACCEPTABLE": "Email/numeros utilisé par plusieur compte,  ve<PERSON><PERSON><PERSON> ressayer avec une donnée unique", "INSUFFICIENT_BALANCE": "Le solde de votre compte est insuffisant pour effectuer cette commande", "PROMO_CODE_EXIST": "Le code promo existe déjà", "CURRENT_BALANCE_NOT_FOUND": "Le solde courant est introuvable", "M2U_VERIFY_WALLET": "Une erreur est survenue lors de la vérification du wallet", "M2U_PAY_ORDER": "Une erreur est survenue lors du paiement de votre commande", "OCCURRED_ERROR": "Une erreur est survenue", "NO_AMOUNT": "Le montant TTC ne doit pas être inférieur ou égal à zéro", "NO_USER_FOR_LOYALTY_PROGRAM": "Aucun utilisateur trouvée pour ce programme de fidelité", "NO_ORDER_FOR_LOYALTY_PROGRAM": "Aucune commande trouvée pour ce programme de fidelité", "NO_ENOUGH_POINT_TO_VALIDATE": "Le client n'a pas assez de points pour valider cette commande", "COMPANY_ALREADY_EXISTS": "Cette compagnie existe déjà", "WHOLE_SALE_EXIST": "Ce demi-grossiste existe déjà"}, "mail": {"RESET_MAIL_SENT": "Mail de réinitialisation de mot de passe envoyé", "OTP_CODE_SEND": "Votre code otp a été généré et envoyer avec success"}, "JDE_NUMBER": "La commande a été envoyer à JDE", "REWARD_REQUEST_NOT_FOUND": "<PERSON><PERSON><PERSON> de récompense non trouvée", "REWARD_REQUEST_ALREADY_VALIDATED": "<PERSON><PERSON><PERSON> de récompense déjà validée", "REWARD_REQUEST_VALIDATED": "<PERSON><PERSON><PERSON> de récompense validée", "ERROR_REJECT_ORDER_FAILED": "Échec du rejet de la demande", "QR_CODE_NOT_FOUND": "Code QR non trouvé"}