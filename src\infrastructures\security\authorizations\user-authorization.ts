import { UserRole } from "src/modules/users/roles";
import { UserAction } from "src/modules/users/actions";
import { EmployeeType, User } from "@la-pasta-module/users";
import { AuthorizationInterface } from "./authorization.interface";

export class UserAuthorization implements AuthorizationInterface {

  CORDO_RH = 101;
  DRH = 102;

  support(user: User, permission: string): boolean {
    return ([
      UserAction.CREATE,
      UserAction.UPDATE,
      UserAction.VIEW,
      UserAction.CHANGE_PASSWORD,
      UserAction.VALIDATE_USER] as string[])
      .includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string, subject?: any): boolean {
    if (permission == UserAction.UPDATE || permission == UserAction.VIEW) {
      if (subject && '_id' in subject) return user._id.toString() == subject._id || user.roles.includes(UserRole.CLIENT) || user.roles.includes(UserRole.BACKOFFICE);

      return user.roles.includes(UserRole.CLIENT) || user.roles.includes(UserRole.BACKOFFICE);
    }

    if (permission == UserAction.VALIDATE_USER && 'employeeType' in user) {
      return user.employeeType === this.CORDO_RH || user.employeeType === this.DRH;
    }

    return user.roles.includes(UserRole.CLIENT) || user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const UserAuthorizationInstance = new UserAuthorization();