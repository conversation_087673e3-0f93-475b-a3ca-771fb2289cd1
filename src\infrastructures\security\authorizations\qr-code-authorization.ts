import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';
import { QrCodeAction } from '@la-pasta-module/qr-code-management/actions/qr-code.action';


class QrCodeAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      QrCodeAction.DELETE,
      QrCodeAction.CREATE,
      QrCodeAction.UPDATE,
      QrCodeAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission === QrCodeAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const QrCodeAuthorizationInstance = new QrCodeAuthorization();