import { CompanyCategory } from '@la-pasta-module/companies';
import { MongoDatabase } from '@la-pasta/database';
import { Price } from '@la-pasta-module/price';
import { getPackagings } from 'scripts/packaging';
import { getProducts } from 'scripts/products';
import { getStores } from 'scripts/stores';
import { stopLoader, setLoader } from 'scripts/common';
import moment from 'moment';
import { getDataInExcelFile } from '@la-pasta/common';
import { getShippings } from 'scripts/shippings';
import { GlobalShipping, Shipping } from '@la-pasta-module/price/shipping';
import { getCompany } from 'scripts/company';

const database = MongoDatabase.getInstance();
const collectionName = 'prices';

export async function insertPricesDataFile() {
  try {
    setLoader('Chargement des prix depuis le fichier Excel\n');
    const pricesCollection = (await database.getDatabase()).collection(collectionName)

    const datas = getDataInExcelFile('la-pasta.price') as any[];
    const stores = await getStores();
    const products = await getProducts();
    const packagings = await getPackagings();
    const shippings = await getShippings();

    const prices: Price[] = [];
    const pricesDouble: Price[] = [];
    const datasError = [];
    for (const data of datas) {

      const store = stores.find(store => store.storeRef == data.store_storeRef
        && store.label == data.store_label);

      const product = products.find(product =>
        product.label == data.product_label)
      // && product.erpRef == data.product_erpRef);

      const packaging = packagings.find(packaging => packaging.label == data.packaging_label);

      const shipping = GlobalShipping.constructPartialShipping({
        startRef: {
          storeRef: data.shippingPrices_startRef_storeRef,
          label: data.shippingPrices_startRef_label
        },
        endRef: data.shippingPrices_endRef,
        label: data.shippingPrices_label,
        amount: data.shippingPrices_amount,
      });

      const price: Price = {
        store,
        product,
        packaging,
        amount: +data?.amount,
        category: CompanyCategory[data?.category] as any,
        renderType: data?.renderType,
        enable: true,
        create_at: moment().valueOf(),

      }

      if (data?.shippingPrices_erpSoldToId) {
        const company = await getCompany({ erpSoldToId: data?.shippingPrices_erpSoldToId })
        if (!company) { datasError.push(data); continue; }
        price['companyId'] = company?._id.toString();

        if (data?.renderType == 2 && data?.shippingPrices_amount) {
          const shipping: Shipping = shippings.find(shipping =>
            shipping?.startRef == data?.shippingPrices_startRef_storeRef &&
            shipping?.endRef == data?.shippingPrices_endRef
          )

          const shippingPrice = {
            ...shipping,
            amount: data?.shippingPrices_amount,
            companyId: company?._id.toString(),
            erpShipToId: company.erpShipToId,
            erpShipToDesc: company.erpShipToDesc,
          }

          price['shippingPrices'] = [shippingPrice]
        }
      }


      if (!packaging?._id || !product?._id || !price?.store?._id) {
        console.log('\nBad prices formated: ', price);
        continue;
      }

      const existsPrice = prices.find(price => price?.store?._id == store?._id
        && price.product?._id == product?._id && price.packaging?._id == packaging?._id);

      const query = {
        'price.product._id': product?._id,
        'price.packaging._id': packaging?._id,
        'price.store._id': store?._id,
        renderType: price.renderType
      }

      if (existsPrice) {
        pricesDouble.push(price);
        continue;
      }

      const res = await pricesCollection.updateOne(query, { $set: { ...price } }, { upsert: true });
      if (res?.matchedCount == 0 && !res?.upsertedCount)
        prices.push(price);

    }

    if (prices?.length) await pricesCollection.insertMany(prices as unknown[]);

    stopLoader(true);
    console.log('prix insérés');
    console.debug('prices that have been skip creation is:', datasError?.length);
    console.debug('prices that have been is double in file:', pricesDouble?.length);

  } catch (error) {
    console.error('Erreur lors de l\'insertion des prix :', error);
  }
}

