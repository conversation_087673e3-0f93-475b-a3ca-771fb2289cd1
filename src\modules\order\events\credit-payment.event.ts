import { Cart } from "@la-pasta-module/price/compute_price";
import moment from "moment";
import { Order } from "../entities";
import { CompanyCategory } from "@la-pasta-module/companies";
import { CompanyEmployee } from "@la-pasta-module/users";
import { Logo } from "@la-pasta-module/logo/entities/logo.entity";

export class CreditPaymentEvent {

  order_ref: string;
  internal_reference: string;
  order_paid_date: string;
  order_retrievement_point: string;
  order_products: {
    item: string;
    quantity: number;
    amount: string;
  }[];
  order_point: string;
  client_name: string;
  client_email: string;
  subtotal: string;
  transport: string;
  total_ht: string;
  tva: string;
  total: string;
  number_months: number;
  date_start: number | string;
  payment_mode: string;
  reference: string;

  client_logo = '';
  client_has_logo = '';
  client_rccm = '';
  client_niu = '';
  client_cni = 'user.cni';
  client_pobox = 'Bp : 2346 YAOUNDE';
  client_contact = '';
  client_seal_seal = '';
  client_seal_signature = '';
  client_seal_fullname = '';
  client_seal_position = '';
  client_has_seal = '';

  bill_to_label = `À l'attention de : `;
  provider_name = 'CIMENCAM SA';
  provider_rccm = 'RCCM : 3391';
  provider_niu = 'NIU : M066300000649C';
  provider_address = 'Z.I. MAGZI BONABERI';
  provider_pobox = 'Bp : 1323 DOUALA';
  provider_tel = 'Tel : (237) 33 39 11 19 – 33 39 04 92';
  provider_fax = 'Fax : 33 39 09 84 / 33 39 06 08';
  provider_email = '<EMAIL>';

  issue_date_label = 'Date Commande : ';
  issue_date = '';
  due_date_label = 'Date paiement : ';
  due_date = '';

  net_term_label = 'Total = ';
  net_term = '';
  currency_label = 'Devise paiement : ';
  currency = 'XAF';

  items = [];

  item_row_number_label = '';
  item_description_label = 'Produits';
  item_quantity_bags_label = 'Qté sacs';
  // item_quantity_tones_label = 'Qté tonnes';
  item_price_label = 'PU';
  item_line_total_label = 'Total';
  types = '';

  amount_subtotal_label = 'Total ciment';
  amount_subtotal = '';

  amount_transport_label = 'Frais de transport';
  amount_transport = '';

  amount_ht_label = 'Total HT';
  amount_ht = '';

  tax_vat_label = 'TVA';
  tax_vat = '';

  tax_precompte_label = 'Précompte';
  tax_precompte = '';
  tax_precompte_rate;

  amount_total_label = 'Total TTC';
  amount_total;

  amount_paid_label = 'Montant Réglé';
  amount_paid;

  amount_due_label = 'Montant Du';
  amount_due = '0';

  terms_label = 'Autre info';
  terms = 'Commande effectuée sur la plateforme en ligne MyCIMENCAM';
  company_address = 'CIMENCAM SA';
  company_city_zip_state = 'Zone Inustrielle Bonabéri';
  company_phone_fax = '+237 6 90 00 00 00';
  company_email_web = '<EMAIL>';
  not_company_category_baker: boolean;

  dailyNbrOrder: number;
  order_arrival_time: string;

  constructor(order: Order, options: { userValidate?: Logo, currentUser?: CompanyEmployee, dailyNbrOrder?: number, orderArrivalTime?: string }) {
    const { userValidate, currentUser, dailyNbrOrder = 0, orderArrivalTime } = options;

    this.payment_mode = 'A Crédit';
    this.order_ref = order?.appReference;
    this.internal_reference = order?.customerReference;
    this.client_name = order?.user?.firstName + ' ' + order?.user?.lastName;
    this.client_email = order?.user?.email;
    this.order_products = this.formatItems(order?.cart);
    this.order_retrievement_point = order?.cart?.store?.label;
    this.subtotal = this.formatNumber(order?.cart?.amount?.HT);
    this.transport = this.formatNumber(order?.cart?.amount?.shipping);
    this.total_ht = this.formatNumber(order?.cart?.amount?.HT);
    this.tva = this.formatNumber(order?.cart?.amount?.VAT);
    this.total = this.formatNumber(order?.cart?.amount?.TTC);
    this.number_months = order?.payment?.monthlyPayment;
    this.date_start = moment(order?.payment?.startDate).format('/MM/YYYY');
    this.dailyNbrOrder = dailyNbrOrder;
    this.order_arrival_time = orderArrivalTime || moment(order?.dates?.created).format('DD/MM/YYYY');

    this.reference = order?.reference
    this.not_company_category_baker = currentUser?.company?.category !== CompanyCategory.Baker
    this.client_rccm = order?.company?.rccm;
    this.client_niu = order?.company?.nui || order?.user?.nui;
    this.client_cni = order?.user?.cni;
    this.client_contact = order?.company?.tel || (order?.user?.tel as any);
    this.issue_date = moment(order?.dates?.created).format('DD/MM/YYYY');
    this.due_date = moment(order?.dates?.paid).format('DD/MM/YYYY');
    this.net_term = this.formatNumber(order?.cart?.amount?.HT);
    this.amount_subtotal = this.formatNumber(order?.cart?.amount?.HT);
    this.amount_transport = this.formatNumber(order?.cart?.amount?.shipping);
    this.amount_ht = this.formatNumber(order?.cart?.amount?.HT);
    this.tax_vat = this.formatNumber(order?.cart?.amount?.VAT);
    this.tax_precompte = this.formatNumber(order?.cart?.amount?.precompte);
    this.tax_precompte_rate = order?.company?.precompteRate
      ? `(${order?.company?.precompteRate}%)`
      : 'N/A';
    this.amount_total = this.formatNumber(order?.cart?.amount?.TTC);
    this.amount_paid = this.formatNumber(order?.cart?.amount?.TTC);

    order?.cart?.items?.forEach((item, index) => {
      this.items?.push({
        item_row_number: index + 1,
        item_description: `${item?.product?.label}`,
        item_quantity_tones: `${this.formatNumber(item?.quantity * (item?.packaging?.unit?.value ?? 0) * 0.001)}t`,
        item_quantity_bags: `${this.formatNumber(item?.quantity)} `,
        item_price: this.formatNumber(item?.unitPrice),
        item_discount: '19,25%',
        item_tax: order?.company?.precompteRate
          ? `${order?.company?.precompteRate}%`
          : 'N/A',
        item_line_total: this.formatNumber(item?.unitPrice * item?.quantity)
      });
    });
  }

  private formatItems(cart: Cart) {
    return cart?.items?.map(item => {
      return {
        item: item.product.label,
        quantity: item.quantity,
        amount: this.formatNumber(item.quantity * item.unitPrice),
        packaging: item?.packaging?.label
      }
    })
  }

  private formatNumber(amount: number) {
    if (!amount) { return '0' }
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
  }
}