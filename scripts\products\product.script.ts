import { MongoDatabase } from '@la-pasta/database';
import { Product } from '@la-pasta-module/cart';
import moment from 'moment';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { getCategories } from 'scripts/categories/categories.script';

const database = MongoDatabase.getInstance();
const collectionName = 'products';

const products: Omit<Partial<Product>, '_id'>[] = [{
  "label": "PELICAN",
  "image": "https://firebasestorage.googleapis.com/v0/b/mycimencam.appspot.com/o/pelican.png?alt=media&token=d49581eb-9b9d-4f6b-88e1-a23ac4ef0d13",
  "normLabel": "PELICAN ROUGE",
  "erpRef": "PELICAN",
  "description": "Farine de blé. Description du produit.",
  "enable": true
}, {
  "label": "AMIGO",
  "image": "https://firebasestorage.googleapis.com/v0/b/mycimencam.appspot.com/o/amigo.png?alt=media&token=17d3559a-89ba-47ad-906d-c4dd24a447e5",
  "normLabel": "AMIGO",
  "erpRef": "AMIGO",
  "description": "Spécial beignet. Description du produit.",
  "enable": true
}, {
  "label": "LA GENOISE",
  "image": "https://firebasestorage.googleapis.com/v0/b/mycimencam.appspot.com/o/laGenoise.png?alt=media&token=c7db2123-fae3-46dc-8233-3166f9f638ab",
  "normLabel": "LA GENOISE",
  "erpRef": "LAGENOISE",
  "description": "Farine spéciale. Description du produit.",
  "enable": true
}, {
  "label": "LA CAMEROUNAISE",
  "image": "https://firebasestorage.googleapis.com/v0/b/mycimencam.appspot.com/o/laCamerounaise.png?alt=media&token=f28a4568-0352-4bf5-bb09-dbe0171c677b",
  "normLabel": "LACAMEROUNAISE",
  "erpRef": "LACAMEROUNAISE",
  "description": "Farine de blé. Description du produit.",
  "enable": true
}, {
  "label": "COLOMBE",
  "image": "https://firebasestorage.googleapis.com/v0/b/mycimencam.appspot.com/o/colombe.png?alt=media&token=279d974b-ca05-4712-9200-a51b8cef0865",
  "normLabel": "COLOMBE",
  "erpRef": "COLOMBE",
  "description": "Farine de blé. Description du produit.",
  "enable": true
}]

export async function insertProducts() {
  try {
    await dropCollection(collectionName);

    setLoader('Inserttion des produits\n');
    const categories = await getCategories();
    for (const category of categories) {
      delete category.description;
      delete category.created_at;
      products.forEach(product => { product.enable = true; product.category = category; product.create_at = moment().valueOf() });
    }

    const insertedProducts = await (await database.getDatabase()).collection(collectionName).insertMany(products);
    stopLoader(insertedProducts);
  } catch (error) {
    console.error(error);
  }
}

export async function getProducts() {
  try {
    return (await database.getDatabase()).collection(collectionName).find({ enable: true }).toArray() as unknown as Product[];
  } catch (error) {
    console.error(error);
  }
}