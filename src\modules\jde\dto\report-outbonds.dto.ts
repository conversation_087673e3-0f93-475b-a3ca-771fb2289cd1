import { IsDateString, <PERSON>NotEmpty, IsString, Matches, } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class ReportOutboundsDto {
    @IsNotEmpty()
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    soldToID: string;

    @IsNotEmpty()
    @IsDateString()
    @Matches(/^\d{4}(-)(((0)[0-9])|((1)[0-2]))(-)([0-2][0-9]|(3)[0-1])$/i, {
        message: "$property must be formatted as YYYY-MM-DD"
    })
    CustomerStatementFromDate: Date;

    @IsNotEmpty()
    @IsDateString()
    @Matches(/^\d{4}(-)(((0)[0-9])|((1)[0-2]))(-)([0-2][0-9]|(3)[0-1])$/i, {
        message: "$property must be formatted as YYYY-MM-DD"
    }) CustomerStatementToDate: Date;
}