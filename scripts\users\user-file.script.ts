import { MongoDatabase } from '@la-pasta/database';
import { User } from '@la-pasta-module/users';
import * as bcrypt from 'bcrypt';
import moment from 'moment';
import { setLoader, stopLoader } from 'scripts/common';
import { getProfileAuthorization } from 'scripts/profiles';
import { getCompany, updateCompany } from 'scripts/company';
import { getDataInExcelFile } from '@la-pasta/common';
import { setRole } from '.';
import { ObjectId } from 'bson';


const database = MongoDatabase.getInstance();
const collectionName = 'users';

const userCategories = {
    PARTICULAR: 0,
    Retailer: 1,
    EmployeeEntity: 2,
    CLIENT: 3,

    COMMERCIAL: 200,
    ADMIN: 300,
};

const baseUserAdmin: User = {
    "category": 300,
    "address": {
        "region": "Adamaoua",
        "city": "",
        "district": "",
        "commercialRegion": null
    },
    "password": "$2b$10$BntVE3/.EB/A33GORCocLO6viy/9NqsBT2Merd8kk8qGY/JET35cG",
    "tel": *********,
    "email": "<EMAIL>",
    "matricule": "123456",
    "roles": [
        "backoffice"
    ],
    "authorizations": [
        "create_user",
        "view_user",
        "delete_user",
        "change_password",
        "update_user",
        "view_order",
        "delete_order",
        "update_order",
        "validate_order",
        "view_faq",
        "create_faq",
        "update_faq",
        "admin_validate_order_retail",
        "view_customers_order",
        "create_price",
        "delete_price",
        "update_price",
        "view_price",
        "create_location",
        "delete_location",
        "update_location",
        "view_location",
        "create_company",
        "delete_company",
        "update_company",
        "view_company",
        "view_company_user",
        "add_user",
        "create_packaging",
        "delete_packaging",
        "update_packaging",
        "view_packaging",
        "create_product",
        "delete_product",
        "update_product",
        "view_product",
        "create_store",
        "delete_store",
        "update_store",
        "view_store",
        "create_unit",
        "delete_unit",
        "update_unit",
        "view_unit",
        "create_shipping",
        "view_shipping",
        "update_shipping",
        "delete_shipping",
        'create_technicalsheet',
        'update_technicalsheet',
        'delete_technicalsheet',
        'view_technicalsheet',
        'create_category',
        'update_category',
        'delete_category',
        'view_category',
    ],
    "enable": true,
    "firstName": "ADMIN"
};

export async function insertUsersDataFile() {
    try {
        setLoader('Chargement des utilisateurs depuis le fichier Excel\n');
        const usersCollection = (await database.getDatabase()).collection(collectionName)

        const datas = getDataInExcelFile('la-pasta.users') as any;

        let users: User[] = [baseUserAdmin];

        for (const data of datas) {
            const category = userCategories[data?.category];
            const user: User = {
                // _id: new ObjectId() as any,
                lastName: data?.lastName,
                email: data?.email,
                tel: data?.tel,
                category,
                password: await bcrypt.hash(`${data?.password ?? 123456}`, 10),
                cni: data?.cni ?? '',
                nui: data?.nui ?? '',
                address: {
                    region: data?.address_region ?? '',
                    city: data?.address_city ?? '',
                    district: data?.address_district ?? '',
                    commercialRegion: data?.address_commercialRegion ?? '',
                },
                roles: setRole(category),
                authorizations: (await getProfileAuthorization(category)) ?? ['unauthorize'],
                enable: true,
                created_at: moment().valueOf(),

            };

            if (category === 3) {
                const company = await getCompany({ erpSoldToId: data?.company_erpSoldToId });
                user['company'] = { name: company?.name, category: company?.category, _id: company?._id.toString() };
                user['authorizations'] = await getProfileAuthorization(company?.category,);
            }


            const query = {
                $or: [{ email: user?.email }, { tel: user?.tel }]
            }

            const res = await usersCollection.updateOne(query, { $set: { ...user } }, { upsert: true });
            if (res?.matchedCount == 0 && !res?.upsertedCount)
                users.push(user);

            if (category === 200) {
                const company = await getCompany({ erpSoldToId: data?.company_erpSoldToId });
                const currUser = await usersCollection.findOne(query)
                await updateCompany({ erpSoldToId: data?.company_erpSoldToId }, {
                    $set: {
                        associatedCommercial: {
                            _id: currUser?._id?.toString(),
                            firstName: currUser?.firstName,
                            lastName: currUser?.lastName,
                            category: currUser?.category,
                        }
                    }
                });
                currUser['associatedCompanies'] = currUser['associatedCompanies'] instanceof Array ? currUser['associatedCompanies']
                    : [currUser['associatedCompanies'] ?? {}]
                currUser?.associatedCompanies?.push({ name: company?.name, category: company?.category, _id: company?._id.toString() });

                await usersCollection.updateOne(query, { $set: { ...currUser } }, { upsert: true });

            }

        }

        if (users.length)
            await usersCollection.insertMany(users as unknown[]);

        console.log('Utilisateurs insérés');

        stopLoader(true);
    } catch (error) {
        console.error('Erreur lors de l\'insertion des utilisateurs :', error);
    }
}

