import { Advantage } from "@la-pasta-module/advantages/entities/advantage.entity";
import { CompanyCategory } from "@la-pasta-module/companies";
import { UserCategory } from "@la-pasta-module/users";


class LoyaltyProgram {
  private rewardItems: Advantage[] = [];
}


export class Points {
  validate: number;
  unValidated: number;
  status: FidelityStatus;
  totalPoints: number;
  archived?: number;
}

export enum FidelityStatus {
  AMIGO = 1,
  COLOMBE = 2,
  PELICAN = 3,
}
export interface Benefits {
  monthly: string[];
  annual: string[];
  punctual: string[];
}

export interface Purchase {
  id: string;
  userId: string;
  quantityInKg: number;
  date: Date;
}

export interface UserEntity {
  _id: string;
  points: Points;
  advantageId: string;
  category: UserCategory | CompanyCategory;
  advantage?: Advantage;
}