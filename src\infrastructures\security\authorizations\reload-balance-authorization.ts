import { PaymentAction } from '@la-pasta-module/order';
import { User, UserCategory } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';
import { ReloadBalanceAction } from '@la-pasta-module/reload-balance/actions';

class ReloadBalanceAuthorization implements AuthorizationInterface {
    support(user: User, permission: string): boolean {
        return ([
            ReloadBalanceAction.CAN_RELOAD,
            ReloadBalanceAction.VIEW_RELOAD_HISTORY,
        ] as string[]).includes(permission) && user?.authorizations.includes(permission);
    }

    authorise(user: User, permission: string, subject?: any): boolean {

        if (permission == ReloadBalanceAction.CAN_RELOAD) return ('company' in user);

        return user?.enable === true;
    }
}

export const ReloadBalanceAuthorizationInstance = new ReloadBalanceAuthorization();