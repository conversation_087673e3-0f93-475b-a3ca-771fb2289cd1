import { CreatePackagingDto, Packaging } from '@la-pasta-module/cart';
import { MongoDatabase } from '@la-pasta/database';
import moment from 'moment';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { getUnit, getUnits } from 'scripts/units';

const database = MongoDatabase.getInstance();
const collectionName = 'packagings';

export async function insertPackaging() {

  try {
    await dropCollection(collectionName);

    setLoader('Insertion des ensachages\n');

    const units = await getUnits();
    const packagings: Omit<Packaging, '_id'>[] = [];

    units.forEach(unit => {
      const label = `SAC (${unit.value + unit.symbol})`;
      packagings.push({ label, unit: unit });

    });

    // units.forEach(unit => {
    //   packagingLabels.forEach(label => {
    //     if (unit.symbol === 'T') {
    //       label += ` (${unit.value + unit.symbol})`;
    //       packagings.push({ label, unit: unit });
    //     }
    //   });
    // })

    packagings.forEach(packaging => { packaging.enable = true; packaging.create_at = moment().valueOf() });
    const insertedPackagings = await (await database.getDatabase()).collection(collectionName).insertMany(packagings);
    stopLoader(insertedPackagings);
  } catch (error) {
    console.error(error);
  }
}

export async function getPackagings() {
  try {
    return await (await database.getDatabase()).collection(collectionName).find().toArray() as unknown as Packaging[];
  } catch (error) {
    console.error(error);
  }
}