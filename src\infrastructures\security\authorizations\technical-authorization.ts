import { TechnicalSheetAction } from '@la-pasta-module/technical-sheet/actions';
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';

class TechnicalAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      TechnicalSheetAction.CREATE,
      TechnicalSheetAction.DELETE,
      TechnicalSheetAction.UPDATE,
      TechnicalSheetAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == TechnicalSheetAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const TechnicalAuthorizationInstance = new TechnicalAuthorization();