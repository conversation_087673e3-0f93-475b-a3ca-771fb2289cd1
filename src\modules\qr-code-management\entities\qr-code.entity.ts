import { Packaging } from "@la-pasta-module/cart/packagings/entities";
import { Product } from "@la-pasta-module/cart/products/entities";

export class QrCodeData {
  _id: string;
  code: string;
  status: QrCodeStatus;
  product: Partial<Product>;
  packaging: Partial<Packaging>;
  created_at: Date;
  updated_at: Date;
  user_id: string;
  qrCodeFileReference: string;
}

export enum QrCodeStatus {
  ACTIVE = 100,
  SCANNED = 200,
  USED = 300,
  INACTIVE = 99,
}