import { Company } from '@la-pasta-module/companies';
import { User } from '@la-pasta-module/users';

export class Feedback {
  _id?: string;
  ref: string;
  user: Partial<User>;
  company?: Company;
  category: FeedbackCategory;
  subCategory: FeedbackCategory;
  tel?: string;
  message: string;
  status?: statusFeetback;
  dates?: {
    created: number;
    resolved: number;
  };
  attachment?: Attachment;
}

export enum statusFeetback {
  CREATED = 100,
  TREAT = 200
}

export enum statusItems {
  CREATED = 100,
  TREAT = 200
}

export interface FeedbackCategory {
  id: number;
  label: string;
}

export interface Attachment {
  name: string;
  contentType: string;
  file: string;
}
