import { Module } from '@nestjs/common';
import { PlanificationService } from './planification.service';
import { PlanificationController } from './planification.controller';
import { PlanificationRepository } from './repository';
import { ProductsModule, StoresModule } from '@la-pasta-module/cart';

@Module({
  imports: [StoresModule, ProductsModule],
  controllers: [PlanificationController],
  providers: [PlanificationService, PlanificationRepository]
})
export class PlanificationModule { }
