import { BaseService } from '@la-pasta/common';
import { Injectable } from '@nestjs/common';
import { CreateErpItemIdDto } from './dto/create-erp-item-id.dto';
import { UpdateErpItemIdDto } from './dto/update-erp-item-id.dto';
import { ErpItemIdRepository } from './repository';

@Injectable()
export class ErpItemIdService extends BaseService {
  constructor(private erpItemIdrepository: ErpItemIdRepository) {
    super(erpItemIdrepository)
  }

  async updateErpItemId(id: string, erpItemId: UpdateErpItemIdDto) {

    return await this.update({ _id: id }, erpItemId);
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

}
