import { UpdateProductDto } from './dto/update-product.dto';
import { Injectable } from '@nestjs/common';
import { BaseService } from 'src/common/base';
import { ProductRepository } from './repository';
import { CreateProductDto } from './dto';
import { FirebaseCloud } from 'src/infrastructures/image';


@Injectable()
export class ProductsService extends BaseService {
  constructor(
    private readonly productRepository: ProductRepository,
    private firebaseCloud: FirebaseCloud
  ) {
    super(productRepository);
  }

  async createProduct(productDto: CreateProductDto) {
    productDto.image = await this.firebaseCloud.getImageUrl(productDto.image, productDto.erpRef);
    productDto.created_at = Date.now();

    return this.create(productDto);
  }

  async updateProduct(id: string, product: UpdateProductDto) {
    const dataUriRegExp = /^data:image\/(.*);base64/gm;
    
    
    if (product.image && product.image.match(dataUriRegExp) != null)
      product.image = await this.firebaseCloud.getImageUrl(product.image, product.erpRef);
    
    return await this.update({ _id: id }, product);
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }
  
}
