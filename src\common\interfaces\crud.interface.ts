import { DeleteResult, Document } from "mongodb";

export interface CRUDInterface {

    create<T>(data: Document): Promise<T>;

    createMany<T>(data: Document): Promise<T>;

    findAll<T>(query?: QueryOptions): Promise<T[]>;

    findOne<T>(query: QueryOptions): Promise<T>;

    findBy<T>(query: QueryOptions): Promise<T[]>;

    update<T>(filter: QueryFilter, data: Document): Promise<T>;

    remove(id: DataId): Promise<DeleteResult>

}