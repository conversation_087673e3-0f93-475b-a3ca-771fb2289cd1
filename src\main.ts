import { join } from 'path';
import morgan from 'morgan';
import helmet from 'helmet';
import * as cors from 'cors';
import { config } from 'convict-config';
import { AppModule } from './app.module';
import * as bodyParser from 'body-parser';
import { NestFactory } from '@nestjs/core';
import { useContainer } from 'class-validator';
import { subscriberMigrateData } from './common';
import { ValidationPipe } from '@nestjs/common';
import { morganOption } from './infrastructures/logger';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import { i18nValidationErrorFactory, I18nValidationExceptionFilter } from 'nestjs-i18n';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, { bufferLogs: true });

  app.use(cors, helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        frameAncestors: ["'self'", 'https://centinelapistag.cardinalcommerce.com', 'http://localhost:3001', "http://localhost:4200", "https://mobile-mcm.londo-tech.com", "https://dev-refactoring.mycimencam.com", "http://localhost:8100", "http://localhost"]
      },
    },
  }),
  );

  //TODO: setting up logger globaly on app
  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));

  // app.useLogger(app.get(Logger));

  // // captures exceptions and assigns them to the response object err
  // app.useGlobalInterceptors(new LoggerErrorInterceptor());

  app.use(bodyParser.json({ limit: '10mb' }));
  app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));

  app.useGlobalPipes(
    new ValidationPipe({
      forbidNonWhitelisted: true,
      whitelist: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: false,
      },
      exceptionFactory: i18nValidationErrorFactory,
    }),
  );

  app.useGlobalFilters(new I18nValidationExceptionFilter({ detailedErrors: false }));

  app.setGlobalPrefix(config.get('basePath'));

  const swaggerConfig = new DocumentBuilder()
    .setTitle('Pasta API V3')
    .setDescription('Documentation des endpoints de l\'api avec les structure de données en entrée ainsi que les types de retour')
    .setVersion('3.0')
    .build();

  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('documentation', app, document);

  app.enableCors();

  const format = '\n' && `\n\b:remote-addr - ':method :url HTTP/:http-version' :status :response-time ms - :res[content-length] ":referrer" ":user-agent"\n` + '\n\b';
  app.use(morgan(format, morganOption));

  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.setBaseViewsDir(join(__dirname, '..', 'views'));
  app.setViewEngine('hbs');

  useContainer(app.select(AppModule), { fallbackOnErrors: true });

  await app.listen(config.get('port'), async () => {

    await subscriberMigrateData();
    console.log(`Lapasta Core api started. Listening with TCP transport on port: ${config.get('port')} in "${config.get('env')}" mode`);
  })
}
bootstrap();
