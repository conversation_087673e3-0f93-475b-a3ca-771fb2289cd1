import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { FeedbackRepository } from './repository';
import { BaseService, getUser, setResponse, t } from '@la-pasta/common';
import { Feedback, statusFeetback } from './entities';
import { FirebaseCloud } from 'src/infrastructures/image';
import { ClientProxy } from '@nestjs/microservices';

import moment from 'moment';
import { FeedbackCreatedEvent } from './events/feedback-created.event';
import { FeedbackTreadEvent } from './events/feedback-tread.event';
import { CompanyEmployee, User, UserCategory } from '@la-pasta-module/users';
import { NotificationsService } from '@la-pasta-module/notifications/notifications.service';
import { NotificationCategory } from '@la-pasta-module/notifications/entities';
import { ObjectId } from 'mongodb';
import { Company } from '@la-pasta-module/companies';


@Injectable()
export class FeedbacksService extends BaseService {


  constructor(private readonly feedbackRepository: FeedbackRepository,
    private firebaseCloud: FirebaseCloud,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,
    private notificationSrv: NotificationsService

  ) {
    super(feedbackRepository)
  }

  async createFeedBack(createFeedbackDto: CreateFeedbackDto) {
    const user = getUser() as CompanyEmployee;
    let company: Company;
    const objectId = new ObjectId(createFeedbackDto?.user?.company?._id) as unknown as string;
    createFeedbackDto.user.company._id = objectId;
    user?.category === UserCategory.CompanyUser ? company = createFeedbackDto?.user?.company : company = user?.company;

    createFeedbackDto.user = {
      _id: user?._id.toString(),
      lastName: user?.lastName,
      email: user?.email,
      tel: user?.tel,
      company
    }

    createFeedbackDto.status = statusFeetback.CREATED;
    createFeedbackDto.ref = this.generateAppReference(user?.lastName)

    // if ('attachment' in createFeedbackDto) {
    //   createFeedbackDto.attachment.file = await this.firebaseCloud.getImageUrl(createFeedbackDto.attachment?.file, createFeedbackDto.attachment?.name);
    // }

    const res = await this.create(createFeedbackDto);
    if (res.status === 200) {
      this.queueClient.emit(
        'feedback_created',
        new FeedbackCreatedEvent(createFeedbackDto),
      );
    }
    this.notificationSrv.generateActionNotification(NotificationCategory.FEEDBACK, (createFeedbackDto as Feedback));
    return setResponse(HttpStatus.CREATED, t('CREATED'), await res.data);
  }

  async getFeedback(user: User, query: QueryOptions) {
    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }
    if (user?.category === UserCategory.CompanyUser && query.filter['user.company._id'] ) {
      const objectId = new ObjectId(query.filter['user.company._id']);
      query.filter['user.company._id'] = objectId;
    }

    return await this.findAll(query);
  }

  private setDateFilter(query: QueryFilter) {
    query['created_at'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }

  private generateAppReference(storeRef: string) {
    const dateStr = moment().format('YYMMDDHHmmss');
    const random = Math.floor(Math.random() * 999) + 100;
    const mapped = {
      CM10000: 'BON',
      CM10010: 'FIG',
      CM10020: 'NOM',
      CM12000: 'DEPYAO',
      CM12010: 'DEPFIG',
      CM12020: 'DEPMAR',
      CM12030: 'DEPNGA',
      CM12040: 'DEPKYE',
      CM12070: 'DEPGAB',
      CM12090: 'DEPBEL',
      CM12100: 'DEPGAR',
      CM12110: 'DEPKOU',
    };
    return (
      `${mapped[storeRef] ?? 'CLAIM'}${dateStr}`.substr(0, 12) + `${random}`
    );
  }

  async updateFeedback(id: string) {
    const res = await this.update(
      { _id: id },
      { status: statusFeetback.TREAT },
    );
    const { ...feedback } = await this.findOne({ filter: { _id: id } });
    if (res.status === 200) {
      this.queueClient.emit(
        'feedback_treat',
        new FeedbackTreadEvent(feedback, {
          email: feedback?.user.email
        }),
      );
    }

    return res;
  }
}


