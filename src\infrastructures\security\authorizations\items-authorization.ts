
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';
import { ItemsAction } from '@la-pasta-module/items/actions';

class ItemsAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      ItemsAction.DELETE,
      ItemsAction.CREATE,
      ItemsAction.UPDATE,
      ItemsAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == ItemsAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const ItemsAuthorizationInstance = new ItemsAuthorization();