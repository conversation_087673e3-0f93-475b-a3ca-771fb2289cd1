import { config } from 'convict-config';
import { BaseService, t } from '@la-pasta/common';
import { FidelityStatus, UserEntity } from './entities';
import { CompaniesService } from '@la-pasta-module/companies';
import { Cart, CartItem } from '@la-pasta-module/price/compute_price';
import { Order, OrderStatus } from '@la-pasta-module/order';
import { CompanyEmployee, Particular, User, UserCategory, UsersService } from '@la-pasta-module/users';
import { forwardRef, HttpException, HttpStatus, Inject } from '@nestjs/common';
import { Advantage, POINTS_BY_STATUS_AND_PACKAGING } from '@la-pasta-module/advantages/entities/advantage.entity';
import { InvitationService } from '@la-pasta-module/invitation/invitation.service';
import { LoyaltyProgramRepository } from './repository/loyalty-program.repository';
import { AdvantagesService } from '@la-pasta-module/advantages/advantages.service';
import { Invitation, InvitationStatus } from '@la-pasta-module/invitation/entities/invitation.entity';
import { OrderSupplier } from '@la-pasta-module/order-supplier/entities/order-supplier.entity';
import { OrderItem } from '@la-pasta-module/order-items/entities/order-item.entity';
import { OrderSupplierService } from '@la-pasta-module/order-supplier/order-supplier.service';
import { QrCodeService } from '@la-pasta-module/qr-code-management/qr-code.service';
import { QrCodeData, QrCodeStatus } from '@la-pasta-module/qr-code-management/entities/qr-code.entity';
import { ClientProxy } from '@nestjs/microservices/client/client-proxy';
import { Items } from '../order-items/entities/order-item.entity';
import moment from 'moment';
import { ItemsService } from '../items/items.service';
import { NotificationsService } from '@la-pasta-module/notifications/notifications.service';
import { CreateOrderItemDto } from '@la-pasta-module/order-items/dto';
import { OrderItemsService } from '@la-pasta-module/order-items/order-items.service';
import { ObjectId } from 'bson';

export class LoyaltyProgramService extends BaseService {

  private readonly FILLEUL_MIN_THRESHOLD_POINTS = config.get('filluelMinThresholdPoints');
  private readonly SPONSORSHIP_POINTS = config.get('sponsorshipPoints');
  private readonly fidelityStatusThresholds = config.get('fidelity.statusThresholds');

  constructor(
    private qrCodeSrv: QrCodeService,
    private advantageSrv: AdvantagesService,
    private orderSupplierSrv: OrderSupplierService,
    private readonly loyaltyProgram: LoyaltyProgramRepository,
    @Inject(forwardRef(() => UsersService)) private readonly userSrv: UsersService,
    @Inject(forwardRef(() => CompaniesService)) private readonly companySrv: CompaniesService,
    @Inject(forwardRef(() => InvitationService)) private readonly invitationSrv: InvitationService,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,
    private readonly itemsService: ItemsService,
    private orderItemSrv: OrderItemsService,
    private notificationSrv: NotificationsService,
  ) {
    super(loyaltyProgram)
  }

  async getPoints(user: User, query: QueryFilter) {
    try {
      if (query && query?.companyId) user['company']['_id'] = query.companyId;

      const entity = await this.getEntity(user) as any;
      if (entity && entity?.points)
        entity.advantage = await this.advantageSrv.findOne({ filter: { _id: entity.advantageId } }) as unknown as Advantage;

      return entity
    } catch (error) {
      throw new HttpException(t("OCCURRED_ERROR"), HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getBenefitReward(_id: string) {
    return (await this.advantageSrv.findOne({ filter: { _id } }))?._id;

  }

  getRewardBenefit(query: QueryOptions) {
    try {
      return this.advantageSrv.findAll(query);
    } catch (error) {
      this.logger.error(`Error while Reward Benefit Data: ${JSON.stringify(error)}`);
    }
  }

  async setProgramFidelity(order: Order | OrderSupplier): Promise<void> {
    const { user, status, company, cart: { amount } } = order as Order;

    if (!user || !status) throw new HttpException(t('error.NO_USER_FOR_LOYALTY_PROGRAM'), HttpStatus.NOT_FOUND);

    try {
      const entity = await this.getEntity({ ...user, company });
      const totalPoints = this.calculateTotalPointsOrder(order?.cart?.items as CartItem[], entity);

      if (!entity?.points) entity['points'] = {
        validate: 0,
        unValidated: 0,
        totalPoints: 0,
        status: FidelityStatus.AMIGO
      };
      const validatePoints = this.getValidatePoint(status, entity, totalPoints, amount?.discount?.points);
      const points = {
        unValidated: this.getUnValidatePoint(status, entity, totalPoints),
        validate: validatePoints,
        totalPoints: (entity?.points?.totalPoints || entity?.points?.validate || 0) + totalPoints,
        status: this.getFidelityStatus(entity, validatePoints),
      };

      if (points.status !== entity.points.status) {
        const reward = await this.advantageSrv.getRewardByStatus(points.status);
        if (reward) {
          const item = await this.itemsService.findOne({ filter: { _id: reward.itemId } }) as unknown as Items;
          await this.createRewardOrder(user, item);
        }
      }

      await this.desActivateUseQrCode((order as OrderSupplier)?.qrCodeData);
      const advantageId = (await this.advantageSrv.findOne({ filter: { statusValue: points.status } }))?._id;

      const updateData = { points, advantageId };
      const entityQuery = this.getEntityQuery({ ...user, company });

      await entityQuery.service.update({ _id: entity?._id }, updateData);

      // if (await this.verifyOrderExtisMoreThanOne(user?.tel))
      if (points?.validate >= this.FILLEUL_MIN_THRESHOLD_POINTS) await this.setPointToPropossal(user?.tel);

    } catch (error) {
      this.logger.error(`Error when setProgramFidelity: ${JSON.stringify(error)}`);
      throw new HttpException('Failed to set program fidelity due to an internal error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async desActivateUseQrCode(qrCodeData: Partial<QrCodeData>[]) {
    const qrCode = qrCodeData.map((item) => item?.code);
    return await this.qrCodeSrv.updateMany({ code: { $in: qrCode } }, { status: QrCodeStatus.USED });
  }

  async setPointToPropossal(userTel: number) {

    const invitation = await this.checkInvitation(userTel);
    const { referrerId, _id } = invitation;

    if (!invitation?.referrerId) {
      this.logger.debug(`'No referrer found for the user with the number : ${userTel}`);
      return null;
    }

    // Récupération du parrain et cast en Particular
    const referrer = await this.userSrv.findOne({
      filter: { _id: referrerId },
    }) as unknown as Particular;

    if (!referrer._id) {
      this.logger.debug(`No users found for the referrer with ID: ${referrerId}`);
      return;
    }

    // Vérifier que le parrain est bien un `Particular` et qu'il a des points
    // if (!(referrer instanceof Particular)) {
    //   this.logger.debug(`The referrer${(referrer as Particular)._id} is not a Particular user.`);
    //   return;
    // }

    // Calcul du total des quantités commandées par le filleul
    // const totalWeight = await this.calculateTotalWeightOrdered(userTel);

    // if (totalWeight < this.FILLEUL_MIN_ORDER_THRESHOLD_KG) {
    //   this.logger.debug(`Total orders for ${userTel} is ${totalWeight} kg, less than 25 kg. No points awarded.`);
    //   return;
    // }

    // Attribution des points au parrain
    if (!referrer.points) {
      referrer.points = {
        validate: 0,
        unValidated: 0,
        totalPoints: 0,
        status: FidelityStatus.AMIGO,
      };
    }
    const entity = await this.getEntity({ ...referrer });

    const earnedPoints = this.SPONSORSHIP_POINTS;
    const validatePoints = referrer.points.validate + earnedPoints;
    const status = this.getFidelityStatus(entity, validatePoints);

    const advantageId = (await this.advantageSrv.findOne({
      filter: { statusValue: status },
    }))?._id;

    // Mise à jour des points du parrain
    const entityQuery = this.getEntityQuery({ ...referrer });

    await entityQuery.service.update({ _id: entity?._id },
      {
        "points.validate": validatePoints,
        "points.status": status,
        advantageId
      });


    // Mise à jour du statut de l'invitation
    await this.invitationSrv.update(
      { _id },
      { status: InvitationStatus.COMPLETED }
    );

    // Récupération des informations du filleul en tant que Particular
    const refferal = await this.userSrv.findOne({
      filter: { tel: userTel },
    }) as unknown as Particular;

    // Création du nom du filleul pour le SMS (avec ternaire pour gérer le cas où le nom n'est pas disponible)
    const refferalName = refferal?.firstName && refferal?.lastName
      ? `${refferal?.firstName} ${refferal?.lastName}`
      : `votre filleul`;

    // Envoi du SMS au parrain pour l'informer qu'il a reçu des points
    this.queueClient.emit('sms_received', {
      receiver: referrer?.tel,
      message: `🎉 Félicitations ! Vous venez de recevoir ${earnedPoints} points de fidélité grâce à ${refferalName} qui a validé 5 points dans le programme. Votre nouveau statut est : ${status}. Merci pour votre parrainage ! 🌟`,
    });

    this.logger.debug(`Points awarded to the referrer ${referrer?._id} after the referee's first order ${userTel}. SMS notification sent.`);
  }

  async getEntity(user: User): Promise<UserEntity> {
    try {
      const entityQuery = this.getEntityQuery(user);

      return await entityQuery.service.findOne(entityQuery) as unknown as UserEntity;
    } catch (error) {
      this.logger.error(`Error while getting points: ${JSON.stringify(error)}`);
      return error;
    }
  }

  private async verifyOrderExtisMoreThanOne(userTel: number): Promise<boolean> {
    try {
      const count = await this.orderSupplierSrv.count({ filter: { "user.tel": +userTel } }) as number;

      return !(count > 1);
    } catch (error) {
      this.logger.error(`Error while getting order count: ${JSON.stringify(error)}`);
      return error;
    }
  }

  private async checkInvitation(tel: number) {

    // Récupération de l'invitation associée au filleul
    return await this.invitationSrv.findOne({
      filter: { prospectTel: `${tel}`, status: InvitationStatus.VALIDATED },
    }) as unknown as Invitation;
  }

  async countAndAdjustPointByOrderOfMAketPlace(order: OrderItem) {
    const { user, cart: { amount } } = order;

    const entity = await this.getEntity({ ...user });

    if (amount.TTC >= entity?.points?.validate)
      throw new HttpException(t('error.NO_ENOUGH_POINT_TO_VALIDATE'), HttpStatus.FORBIDDEN);

    entity.points.validate -= amount?.TTC;

    const entityQuery = this.getEntityQuery({ ...user });
    await entityQuery.service.update({ _id: entity._id }, { 'points.validate': entity.points.validate });
  }

  private async getDataLoyaltyProgram(user: User): Promise<UserEntity[]> {
    try {
      const { service, filter } = this.getEntityQuery(user);

      return await service.getDataLoyaltyProgram(filter);
    } catch (error) {
      this.logger.error(`Error while getting points: ${JSON.stringify(error)}`);
      return error;
    }
  }

  private getEntityQuery(user: User): { service: UsersService | CompaniesService, filter: any, projection: any } {
    return (user as CompanyEmployee)?.company?._id && user.category === UserCategory.CompanyUser
      ? { service: this.companySrv, filter: { _id: (user as CompanyEmployee)?.company._id }, projection: { points: 1, category: 1, advantageId: 1 } }
      : { service: this.userSrv, filter: { _id: user._id }, projection: { points: 1, category: 1 } };
  }

  getValidatePoint(orderStatus: OrderStatus, { points }: UserEntity, pointsToAdd: number, applyPoints = 0): number {
    return orderStatus === OrderStatus.VALIDATED ? (points?.validate + pointsToAdd - applyPoints) : points?.validate ?? 0;
  }

  private getUnValidatePoint(orderStatus: OrderStatus, entity: UserEntity, points: number): number {
    if (orderStatus === OrderStatus.VALIDATED && entity?.points?.unValidated >= points) {
      return entity?.points?.unValidated - points;
    }

    return (orderStatus !== OrderStatus.VALIDATED) ? (entity?.points?.unValidated || 0) + points : 0;
  }

  private getFidelityStatus(entity: UserEntity, points: number): FidelityStatus {
    const currentStatus = this.fidelityStatusThresholds.find(({ min, max }) => points >= min && points <= max);

    if (!currentStatus) return FidelityStatus.AMIGO;

    const statusKey = currentStatus.status as keyof typeof FidelityStatus;
    return FidelityStatus[statusKey] || FidelityStatus.AMIGO;
  }

  private calculateTotalPointsOrder(items: CartItem[], entity: UserEntity): number {
    const status = entity?.points?.status || FidelityStatus.AMIGO;
    const pointMatrix = POINTS_BY_STATUS_AND_PACKAGING;

    if (!items?.length) {
      this.logger.warn('Panier vide ou inexistant pour le calcul des points');
      return 0;
    }

    return items.reduce((totalPoints, cartElement) => {
      const unit = cartElement?.packaging?.unit?.value;
      const quantity = cartElement?.quantity || 0;

      if (!unit || !pointMatrix?.[status]?.[unit]) {
        this.logger.warn(`Aucune valeur dans la matrice pour ${status} avec ${unit}kg`);
        return totalPoints;
      }

      const pointPerUnit = pointMatrix[status][unit];
      return totalPoints + quantity * pointPerUnit;
    }, 0);
  }

  private validateCartItemData(item: CartItem): {
    unitValue: string;
    quantity: number;
    productLabel: string;
  } {
    return {
      unitValue: String(item?.packaging?.unit?.value || '0'),
      quantity: Math.max(0, item?.quantity || 0), // Assure que la quantité est positive
      productLabel: item?.product?.label || 'Produit inconnu'
    };
  }

  private calculateItemPoints(
    item: CartItem,
    status: FidelityStatus,
    pointMatrix: any
  ): number {
    const { unitValue, quantity, productLabel } = this.validateCartItemData(item);

    // Récupération des points selon la matrice de fidélité
    const statusKey = FidelityStatus[status] || 'AMIGO';
    const pointValue = pointMatrix?.[statusKey]?.[unitValue] || 0;

    // Calcul des points pour cet article (uniquement points fixes)
    const itemPoints = pointValue * quantity;

    this.logger.log(`Article: ${productLabel}, Quantité: ${quantity}, Unité: ${unitValue}, Points: ${itemPoints}`);

    return itemPoints;
  }

  private async calculateTotalWeightOrdered(userTel: number): Promise<number> {
    try {
      // Récupérer toutes les commandes de l'utilisateur par son numéro de téléphone
      const { data } = await this.orderSupplierSrv.findAll({
        filter: { "user.tel": userTel },
      });

      if (!data.length) {
        this.logger.debug(`Aucune commande trouvée pour le numéro : ${userTel}`);
        return 0;
      }

      // Calculer le poids total des commandes
      const totalWeight = data.reduce((total, order) => {
        if (!order?.cart?.items || !Array.isArray(order.cart.items)) {
          this.logger.warn(`Commande invalide ou sans articles pour l'ID : ${order?._id}`);
          return total;
        }

        const orderWeight = order.cart.items.reduce((itemTotal: number, item: CartItem) => {
          // Vérifier que les données nécessaires sont disponibles
          const itemWeight = item?.packaging?.unit?.value || 0;
          const itemQuantity = item?.quantity || 0;
          return itemTotal + itemQuantity * itemWeight;
        }, 0);

        return total + orderWeight;
      }, 0);

      this.logger.debug(`Poids total pour ${userTel} : ${totalWeight} kg`);
      return totalWeight;
    } catch (error) {
      this.logger.error(
        `Erreur lors du calcul du poids total des commandes pour ${userTel}:`,
        {
          message: error.message || 'Erreur inconnue',
          stack: error.stack,
          name: error.name
        }
      );
      return 0;
    }
  }

  // loyalty-program.service.ts
  async rewardUserForFidelityStatusChange(user: User, newStatus: FidelityStatus) {
    const itemIds = await this.advantageSrv.getRewardItemsByFidelityStatus(newStatus);

    const cartItems: Items[] = itemIds.map((itemId) => ({
      item: itemId,
      quantity: 1,
    }));

    const orderDto: CreateOrderItemDto = {
      user,
      cart: {
        items: cartItems,
        amount: 0, // gratuit
      },
      status: OrderStatus.VALIDATED,
      appReference: await this.orderItemSrv.generateAppReference(user._id),
      dates: { created: moment().valueOf() },
    };

    if ('company' in user) {
      orderDto.company = {
        ...user.company,
        _id: new ObjectId(user.company._id).toString(),
      };
    }

    const order = await this.orderItemSrv.create(orderDto);

    await this.notificationSrv.sendSmsNotification({
      user,
      content: `🎉 Félicitations ! En atteignant le statut ${newStatus}, vous avez gagné un lot gratuit. Il est disponible dans vos commandes.`
    });

    return order;
  }

}
