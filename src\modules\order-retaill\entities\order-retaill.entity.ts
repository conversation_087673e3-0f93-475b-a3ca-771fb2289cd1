import { Product } from '@la-pasta-module/cart';
import { Packaging } from '@la-pasta-module/cart/packagings';
import { Company } from '@la-pasta-module/companies';
import { User } from '@la-pasta-module/users';

export class OrderRetaill {
  _id: string;
  appRef: string;
  custumerRef?: string;
  distributors: Partial<Company>;
  packaging: Partial<Packaging>;

  adresse: Partial<Address>;
  status: ResellerOrderStatus;
  user: Partial<User> | string;
  items: CartItemReseller[];
  created_at?: number;

  validation?: {
    prevalidate?: {
      date: number;
      user: Partial<User> | string;
      raison: string;
    };
    validate?: {
      date: number;
      user?: Partial<User> | string;
      raison?: string;
    };
    reject?: {
      date: number;
      user: Partial<User> | string;
      raison: string;
    };
  };

  points?: {
    ordered: number;
    unvalidate: number;
  };
}

export declare type BaseOrderRetail = {
  _id: string;
  appRef: string;
  status: number
  items: [],
  created_at: number,
  packaging: Packaging
}

export declare type CartItemReseller = {
  product: Partial<Product>;
  quantity: number;
  quantityShipped?: number;
};

export enum ResellerOrderStatus {
  CREATED = 100,
  PREVALIDATED = 200,
  VALIDATED = 300,
  REJECTED = 99,
}
