import { PartialType } from '@nestjs/swagger';
import { CreateFeedbackDto } from './create-feedback.dto';
import { IsEmpty, IsNotEmpty, IsOptional } from "class-validator";
import { Attachment, FeedbackCategory, statusFeetback } from "../entities";
import { User } from "@la-pasta-module/users";

export class UpdateFeedbackDto extends PartialType(CreateFeedbackDto) {

  @IsEmpty()
  ref?: string;

  @IsNotEmpty()
  category: FeedbackCategory;

  @IsNotEmpty()
  subCategory: FeedbackCategory;

  @IsNotEmpty()
  user: Partial<User>;

  @IsEmpty()
  dates?: {
    resolved?: number;
    created?: number
  };

  @IsOptional()
  attachment?: Attachment;
}
