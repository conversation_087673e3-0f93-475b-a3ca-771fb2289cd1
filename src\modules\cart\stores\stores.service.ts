import { Injectable } from '@nestjs/common';
import { BaseService } from 'src/common/base';
import { StoreRepository } from './repository';

@Injectable()
export class StoresService extends BaseService {
  constructor(
    private readonly storeRepository: StoreRepository
  ) {
    super(storeRepository);
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }
}
