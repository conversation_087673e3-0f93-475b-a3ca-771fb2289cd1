import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Company } from "@la-pasta-module/companies";
import { i18nValidationMessage } from "nestjs-i18n";
import { UserExists } from "@la-pasta-module/users/validations";
import { UserRole } from "@la-pasta-module/users";

export class CreateCompanyUserDto {
  @ApiProperty({ type: String })
  @IsEmail({}, { message: i18nValidationMessage('validation.INVALID_EMAIL') })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @UserExists()
  email: string;

  @ApiProperty({ type: String })
  @IsNumber({}, { message: i18nValidationMessage('validation.BAD_PHONE') })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  tel: number;

  @ApiProperty({ type: String })
  @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  password: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  firstName: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  lastName: string;

  @ApiProperty()
  @IsOptional()
  address: Address;

  @ApiProperty({ type: Number })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  category: number;

  @ApiPropertyOptional({ enum: UserRole })
  @IsOptional()
  roles: UserRole[];

  @ApiPropertyOptional({ type: [String] })
  @IsOptional()
  authorizations: string[];

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  createAt?: number;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  updateAt?: number;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  afrilandKey?: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  cni?: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  nui?: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  isRetired?: boolean;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  direction?: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  service?: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  position: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  matricule: string;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  capacityTonnage: number;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  capacityTonnageYear: number;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  socialReason: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  profession: string;

  @ApiPropertyOptional()
  @IsOptional()
  points: Point;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  company: Company;
}