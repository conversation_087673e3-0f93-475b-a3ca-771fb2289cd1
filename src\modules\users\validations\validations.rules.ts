import { ForbiddenException, Injectable } from "@nestjs/common";
import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from "class-validator";
import { t } from '@la-pasta/common';
import { UserRepository } from "../repository";

@ValidatorConstraint({ name: 'UserExists', async: true })
@Injectable()
export class UserExistsRule implements ValidatorConstraintInterface {

  constructor(private userRepository: UserRepository) { }

  async validate(value: any, validationArguments: ValidationArguments): Promise<boolean> {
    try {
      const existVerify = await this.userRepository.findOne({ filter: { [validationArguments.property]: value } });
      if (existVerify) throw new ForbiddenException(t('error.ACCOUNT_EXISTE'));
    } catch (error) {
      return false;
    }

    return true;
  }

  defaultMessage(validationArguments?: ValidationArguments): string {
    return t('error.ACCOUNT_EXISTE')
  }

}