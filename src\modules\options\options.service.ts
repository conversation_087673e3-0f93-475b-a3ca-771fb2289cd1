import { Injectable } from '@nestjs/common';
import { BaseService } from 'src/common/base';
import { OptionRepository } from './repository';
import { CreateOptionDto } from './dto/create-option.dto';
import { UpdateOptionDto } from './dto/update-option.dto';

@Injectable()
export class OptionsService extends BaseService {
  constructor(private readonly optionsRepository: OptionRepository) {
    super(optionsRepository);
  }

  async createOption(createOptionDto: CreateOptionDto) {
    createOptionDto.created_at = Date.now();
    return this.create(createOptionDto);
  }

  async updateProduct(id: string, updateOptionDto: UpdateOptionDto) {
    return await this.update({ _id: id }, updateOptionDto);
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }
}
