import readline from 'readline';
import { insertUnitsDataFile } from './units/unit-file.script';
import { insertPackaginsDataFile } from './packaging/packaging-file.script';
import { insertCategoriesDataFile } from './categories/category-file.script';
import { insertProductsDataFile } from './products/product-file.script';
import { insertPricesDataFile } from './price-offers/price-file.script';
import { insertStoreDataFile } from './stores/store-file.script';
import { insertShippingsDataFile } from './shippings/shipping-file.script';
import { insertCompagniesDataFile } from './company/compagny-file.script';
import { insertUsersDataFile } from './users/user-file.script';

(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données utilisateurs de la-pasta, ce processus effacera les collections existantes.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertUnitsDataFile();
      console.log('Données des unités insérées avec succès.');


      await insertPackaginsDataFile();
      console.log('Données d\'emballage insérées avec succès.');


      await insertCategoriesDataFile();
      console.log('Données des catégories insérées avec succès.');


      await insertProductsDataFile();
      console.log('Données des produits insérées avec succès.');

      await insertStoreDataFile();
      console.log('Données des magasins insérées avec succès.');


      await insertShippingsDataFile();
      console.log('Données d\'expédition insérées avec succès.');

      await insertCompagniesDataFile();
      console.log('Données des compagnies insérées avec succès.');


      await insertUsersDataFile();
      console.log('Données des utilisateurs insérées avec succès.');

      await insertPricesDataFile();
      console.log('Données des prix insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();

