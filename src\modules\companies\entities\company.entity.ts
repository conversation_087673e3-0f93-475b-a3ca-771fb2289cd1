import { Option } from "@la-pasta-module/options/entities";
import { CompanyEmployee, User } from "@la-pasta-module/users";

export class Company {
    _id?: string;
    address?: Address;
    name?: string;
    defaultStore?: string;
    afrilandKey?: string;
    nui?: string;
    rccm?: string;
    tel?: number;
    erpShipToDesc?: string;
    erpSoldToId?: string;
    erpShipToId?: number;
    precompteRate?: PrecompteRate;
    category?: CompanyCategory;
    q1Enabled?: boolean;
    annualOrderEnabled?: boolean;
    users?: CompanyEmployee[];
    associatedCommercial?: Partial<User>;
    isLoyaltyProgDistributor?: boolean;
    loyaltyProgDistributor?: {
        city: string;
        nbResellers: number;
    };
    points?: number;
    enable?: boolean;
    created_at?: number;
    updateAt?: number;
    logo?: string;
    signature?: string;
    options?: Option[] | string[];
    associatedShippingOption?: {
        optionId: string;
        shippingAddressId: string[];
    };
}


export enum CompanyCategory {
    Baker = 101,
    WholeSaler = 102,
    GMS = 103,
    Group = 104,
    Industry = 105,
    EXPORT = 106
}

export enum PrecompteRate {
    Simple = 0.02,
    Real = 0.05,
    withholding = 0.1
}