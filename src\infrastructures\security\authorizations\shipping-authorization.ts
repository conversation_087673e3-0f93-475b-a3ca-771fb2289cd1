import { ShippingAction } from '@la-pasta-module/price/shipping';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class ShippingAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      ShippingAction.CREATE,
      ShippingAction.DELETE,
      ShippingAction.UPDATE,
      ShippingAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (!user.enable) return false;

    return true;
  }
}

export const ShippingAuthorizationInstance = new ShippingAuthorization();