import { Controller, Get } from '@nestjs/common';
import { DirectionService } from './direction.service';
import { <PERSON>noLogger } from 'nestjs-pino';
import { pinoLoggerConfig } from '@la-pasta/infrastructures/logger';

@Controller('directions')
export class DirectionController {

  constructor(private readonly directionService: DirectionService) {
  }

  @Get()
  async findAll() {
    return this.directionService.getDirections();
  }
}
