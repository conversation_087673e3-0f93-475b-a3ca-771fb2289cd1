import { MongoDatabase } from "@la-pasta/database";
import { set<PERSON>oader, stopLoader, table } from "scripts/common";
import { fetchAuthorizations } from "./fetch-authorizations";

const database = MongoDatabase.getInstance();
const collectionName = 'authorizations';

async function getAuthorizationsInDB() {
  setLoader('Récuperation des autorisations dans la DB\n');
  const dbAuthorizations = await (await database.getDatabase()).collection(collectionName).find().project({ _id: 0 }).toArray() as unknown as AppAuthorization[];
  stopLoader(true);

  return dbAuthorizations;
}

function getIncompatibleAuthorizations(loopedAuthorization: AppAuthorization[], filteredAuthorization: AppAuthorization[]) {
  const loopedActions = loopedAuthorization.map(authorization => [...authorization.actions]).flat();
  const filteredActions = filteredAuthorization.map(authorization => [...authorization.actions]).flat();

  return loopedActions.filter(action => !filteredActions.includes(action));
}

function displayResult(resultName: string, data: any[]) {
  if (data.length) table([{ [resultName]: data.join(', ') }]);
}

(async () => {
  const appAuthorizations = await fetchAuthorizations();
  const dbAuthorizations = await getAuthorizationsInDB();

  if (JSON.stringify(dbAuthorizations) !== JSON.stringify(appAuthorizations)) {

    console.log('\n* Des incohérences dans les autorisations ont été trouvées!');
    console.log('* Ci-dessous la liste des autorisations incohérentes.');

    const authorizationNotInApp = getIncompatibleAuthorizations(dbAuthorizations, appAuthorizations);
    const authorizationNotInDb = getIncompatibleAuthorizations(appAuthorizations, dbAuthorizations);

    displayResult('autorisations non présent dans le code', authorizationNotInApp)
    displayResult('autorisations non présent dans la db', authorizationNotInDb)

    process.exit();
  }

  console.log('\n* Vérification des autorisations terminée');
  process.exit()
})()