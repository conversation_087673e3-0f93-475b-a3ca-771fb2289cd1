import { ArrayMinSize, IsEnum, IsNotEmpty, IsOptional } from "class-validator";
import { Packaging, RenderType, Store } from "@la-pasta-module/cart";
import { Shipping } from "@la-pasta-module/price/shipping";
import { CartItem, OrderPrice, ShippingInfo } from "../entities";
import { User } from "@la-pasta-module/users";

export class CreateComputePriceDto {
    @IsNotEmpty()
    store: Partial<Store>;

    @IsNotEmpty()
    @IsEnum(RenderType)
    renderType: RenderType;

    // @IsNotEmpty()
    // packaging: Partial<Packaging>;

    @IsNotEmpty()
    @ArrayMinSize(1)
    items: CartItem[];

    @IsOptional()
    shipping?: Shipping;

    @IsOptional()
    user?: User;

    @IsOptional()
    optionsDiscount?: {
        points: boolean
    };

    @IsOptional()
    amount?: OrderPrice

    @IsOptional()
    shippingInfo?: ShippingInfo
}
