import { IsNotEmpty, <PERSON>Optional, IsBoolean, IsString, IsNumber, IsIn, IsArray } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateOptionDto {

    @IsOptional()
    _id?: object | string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    name: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    description?: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsNumber()
    value: number;

    @IsString()
    @IsIn(['currency', 'percentage', 'quantity'])
    unit: 'currency' | 'percentage' | 'quantity';

    @IsString()
    @IsIn(['discount', 'feature', 'other'])
    type: 'discount' | 'feature' | 'other';

    @IsString()
    @IsOptional()
    formula: string;

    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @IsOptional()
    @IsArray()
    assignedClients?: (object | string)[];

    @IsOptional()
    @IsArray()
    associatedProductsId?: string[];

    @IsOptional()
    created_at: number;
}
