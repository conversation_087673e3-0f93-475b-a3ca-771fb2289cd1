import { LoyaltyProgramService } from '@la-pasta-module/loyalty-program/loyalty-program.service';
import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { QrCodeRepository } from './repository/qr-code.repository';
import { QrCodeData, QrCodeStatus } from './entities/qr-code.entity';
import { BaseService, getUser, t } from '@la-pasta/common';
import moment from 'moment';
import { OrderStatus } from '@la-pasta-module/order';
import { OrderSupplier } from '@la-pasta-module/order-supplier/entities/order-supplier.entity';
import { Particular, UsersService } from '@la-pasta-module/users';


@Injectable()
export class QrCodeService extends BaseService {
  constructor(
    private qrCodeRepository: QrCodeRepository,

    @Inject(forwardRef(() => LoyaltyProgramService))
    private readonly loyaltyProgramService: LoyaltyProgramService,
    @Inject(forwardRef(() => UsersService))
    private readonly userService: UsersService,
  ) {
    super(qrCodeRepository);
  }

  async findAndModifyQrCode(code: string, query?: QueryFilter) {
    let user = getUser() as Particular;

    if ('particularUser' in query)
      user = await this.userService.findOne({ filter: { _id: query?.particularUser } }) as unknown as Particular

    const qrCodeFilter = {
      code,
      status: { $in: [QrCodeStatus.ACTIVE, QrCodeStatus.SCANNED] }
    };

    const dataQrCode = await this.findAndModify(
      {
        filter: qrCodeFilter,
        projection: { created_at: 0, updated_at: 0, enable: 0 }
      },
      {
        updated_at: moment().valueOf()
      }
    );
    
    if (!dataQrCode?._id) {
      this.logger.warn(`QR code ${code} not found or already processed.`);
      throw new HttpException(t('QR_CODE_NOT_FOUND'), HttpStatus.NOT_FOUND);
    }

    try {
      const orderSupplier = this.buildOrderSupplier(dataQrCode as unknown as QrCodeData, user);


      await this.loyaltyProgramService.setProgramFidelity(orderSupplier as OrderSupplier);

      return dataQrCode;

    } catch (error) {
      this.logger.error(`Error processing QR code ${code}:`, error.stack);
      throw error;
    }
  }

  buildOrderSupplier(dataQrCode: QrCodeData, user: Particular): Partial<OrderSupplier> {
    return {
      user,
      status: OrderStatus.VALIDATED,
      cart: {
        items: [{
          ...dataQrCode,
          quantity: 1,
        }],
      },
      qrCodeData: [dataQrCode],
    };
  }

}
