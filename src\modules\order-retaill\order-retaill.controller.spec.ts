import { Test, TestingModule } from '@nestjs/testing';
import { OrderRetaillController } from './order-retaill.controller';
import { OrderRetaillService } from './order-retaill.service';

describe('OrderRetaillController', () => {
  let controller: OrderRetaillController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrderRetaillController],
      providers: [OrderRetaillService],
    }).compile();

    controller = module.get<OrderRetaillController>(OrderRetaillController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
