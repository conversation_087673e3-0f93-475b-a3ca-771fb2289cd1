import { initializeApp } from "firebase/app";
import { getStorage, ref, uploadString, getDownloadURL } from "firebase/storage";

export class FirebaseCloud {
  firebaseConfig = {
    apiKey: "AIzaSyA-1kN6nkZ9WaG9GSz_0of7rEbNZ4mgvgc",
    authDomain: "monchantier-4b302.firebaseapp.com",
    projectId: "monchantier-4b302",
    storageBucket: "monchantier-4b302.appspot.com",
    messagingSenderId: "467934123335",
    appId: "1:467934123335:web:912374b7b8055cd94ee862",
    measurementId: "G-MB0NY9ZTT2"
  };

  app = initializeApp(this.firebaseConfig);
  storage = getStorage(this.app);
  storageRef = ref(this.storage)

  async uploadImage(image: string, nameRef: string) {
    const storeRef = ref(this.storage, 'lapasta/' + nameRef);
    await uploadString(storeRef, image, 'data_url');
    return storeRef;
  }

  async getImageUrl(image: string, name: string) {
    return await getDownloadURL(await this.uploadImage(image, name))
  }
}