import { Price } from "@la-pasta-module/price";
import { capitalizeFirstLetter } from "@la-pasta/common";
import { MongoDatabase } from "@la-pasta/database";
import { ObjectId } from "mongodb";

import { setLoader, stopLoader } from 'scripts/common';

const database = MongoDatabase.getInstance();
const collectionPrice = 'prices';

const mapRegions = {
    littoral: 'R1',
    ouest: 'R1',
    'nord-ouest': 'R1',
    'sud-ouest': 'R1',
    centre: 'R2',
    sud: 'R2',
    est: 'R2',
    adamaoua: 'R3',
    nord: 'R3',
    'extrême-nord': 'R3',
}

export async function changePriceRegionCase() {
    try {
        setLoader('Update case of region name to capitalize\n');

        const db = await database.getDatabase();

        const prices = await db.collection(collectionPrice)
            .find({ 'store.address.region': { $regex: /^[A-Z]+$/ } })
            .toArray() as unknown as Price[];

        for (const price of prices) {
            const region = capitalizeFirstLetter(price?.store?.address?.region.toLowerCase());

            await db.collection(collectionPrice).updateOne(
                { "_id": new ObjectId(price?._id) },
                { $set: { 'store.address.region': region } }
            );
        }

        stopLoader(true);
    } catch (error) {
        console.error(error);
        return error;
    }
}


export async function updateAddressStorePrice() {
    try {
        setLoader('Update address store of price');

        const db = await database.getDatabase();

        const prices = await db.collection(collectionPrice)
            .find({ 'store.address.region': { $exists: true }, 'store.address.commercialRegion': { $exists: false } })
            .toArray() as unknown as Price[];

        for (const price of prices) {
            const commercialRegion = mapRegions[price?.store?.address?.region.toLowerCase()];

            if (commercialRegion) {
                await db.collection(collectionPrice).updateOne(
                    { "_id": new ObjectId(price?._id) },
                    { $set: { 'store.address.commercialRegion': commercialRegion } }
                );
            } else
                console.log(`Price store with _id: ${price?._id} have not found commercial region with region: ${price?.store?.address?.region}`)
        }

        stopLoader(true);
    } catch (error) {
        console.error();
        return error;
    }

}