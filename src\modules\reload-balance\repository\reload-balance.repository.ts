import { BaseRepository } from "@la-pasta/common";
import { Injectable } from "@nestjs/common";

@Injectable()
export class ReloadBalanceRepository extends BaseRepository {
    monthOfYear = {
        $dateToString: {
            format: '%m',
            date: { $toDate: { $toLong: '$created_at' } }
        }
    };

    constructor() {
        super();
    }

    async getEvolutionPaymentForReloadBalance(query: QueryFilter) {
        const aggregateExpressions = [];

        const matchExpression = {
            $match: {
                'payment.mode.id': { $exists: true, $ne: null },
                ...query
            }
        };
        aggregateExpressions.push(matchExpression);

        const projectExpression = {
            $project: {
                monthOfYear: this.monthOfYear,
                payment: 1
            }
        };
        aggregateExpressions.push(projectExpression);
        return (await this.getCollection()).aggregate(aggregateExpressions).toArray();
    }

    async getTotalAmountByPaymentMode(query: QueryFilter) {
        const aggregateExpressions = [];

        const matchExpression = {
            $match: {
                'payment.mode.id': { $exists: true, $ne: null },
                ...query
            }
        };
        aggregateExpressions.push(matchExpression);

        const groupExpression = {
            $group: {

                _id: '$payment.mode.id',
                total: { $sum: '$payment.amount' }
            }
        };
        aggregateExpressions.push(groupExpression);

        return (await this.getCollection()).aggregate(aggregateExpressions).toArray();
    }

    async getEvolutionByCompaniesReloadBalances(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [];
        const matchExpression = {
            $match: {
                'company.category': { $exists: true },
                ...query,
            }
        };
        aggregateExpressions.push(matchExpression);

        const addFelidsExpression = {
            $addFields: { category: "$company.category" }
        };
        aggregateExpressions.push(addFelidsExpression);

        const addFelidsExpression1 = {
            $addFields: { amount: "$payment.amount" }
        };

        aggregateExpressions.push(addFelidsExpression1);

        const projectExpression = {
            $project: {
                monthOfYear: this.monthOfYear,
                category: 1,
                amount: 1,
                _id: 0
            }
        };
        aggregateExpressions.push(projectExpression);

        return (await this.getCollection()).aggregate(aggregateExpressions).toArray();
    }

    async getTotalReloadByStatusJde(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [];
        const matchExpression = {
            $match: {
                status: { $exists: true },
                ...query,
            }
        };
        aggregateExpressions.push(matchExpression);

        const groupExpression = {
            $group: {
                _id: { status: '$status' },
                total: { $sum: 1 }
            }
        };
        aggregateExpressions.push(groupExpression);

        const addFieldsExpression = {
            $addFields: {
                status: '$_id.status'
            }
        };
        aggregateExpressions.push(addFieldsExpression);

        const projectExpression = {
            $project: {
                status: 1,
                total: 1,
                _id: 0
            }
        }
        aggregateExpressions.push(projectExpression);

        return (await this.getCollection()).aggregate(aggregateExpressions).toArray();

    }

    async getTotalReloadByStatusOperator(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [];
        const matchExpression = {
            $match: {
                status: { $exists: true },
                ...query,
            }
        };
        aggregateExpressions.push(matchExpression);

        const addFelidsExpression = {
            $addFields: {
                unifiedStatus: {
                    $cond: {
                        if: { $eq: ["$paymentInfo.status", "SUCCESSFULL"] },
                        then: "SUCCESSFUL",
                        else: "$paymentInfo.status"
                    }
                }
            }
        }
        aggregateExpressions.push(addFelidsExpression);

        const groupExpression = {
            $group: {
                _id: { status: '$unifiedStatus' },
                total: { $sum: 1 }
            }
        };
        aggregateExpressions.push(groupExpression);

        const addFieldsExpression = {
            $addFields: {
                status: '$_id.status'
            }
        };
        aggregateExpressions.push(addFieldsExpression);

        const projectExpression = {
            $project: {
                status: 1,
                total: 1,
                _id: 0
            }
        }
        aggregateExpressions.push(projectExpression);

        return (await this.getCollection()).aggregate(aggregateExpressions).toArray();


    }
    async getTotalAmountReloadByCategory(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [];

        const matchExpression = {
            $match: {
                'company.category': { $exists: true },
                ...query,
            }
        };
        aggregateExpressions.push(matchExpression);

        const groupExpression = {
            $group: {
                _id: '$company.category',
                total: { $sum: '$payment.amount' }
            }
        };

        aggregateExpressions.push(groupExpression);

        return (await this.getCollection()).aggregate(aggregateExpressions).toArray();

    }

    async getAllReloadTotals(query: QueryFilter) {
        const aggregateExpressions = [];

        const matchExpression = {
            $match: {
                'company.category': { $exists: true },
                ...query,
            }
        };
        aggregateExpressions.push(matchExpression);

        const groupExpression = {
            $group: {
                _id: null,
                totalReload: { $sum: 1 },
                totalAmount: { $sum: '$payment.amount' }
            }
        };
        aggregateExpressions.push(groupExpression);

        const projectExpression = {
            $project: {
                _id: 0
            }
        }
        aggregateExpressions.push(projectExpression);

        return (await this.getCollection()).aggregate(aggregateExpressions).toArray();
    }

}