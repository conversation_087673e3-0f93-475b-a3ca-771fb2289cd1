import { Logger } from "@nestjs/common";
import { config } from "../../convict-config";
import { Db, MongoClient, MongoClientOptions } from "mongodb";

export class DatabaseMongoDBV2 {

    private static instance: DatabaseMongoDBV2;

    private dbV2!: Db;
    private options!: MongoClientOptions;

    private constructor(private readonly logger?: Logger) {
    }

    public static getInstance = (): DatabaseMongoDBV2 => DatabaseMongoDBV2.instance ??= new DatabaseMongoDBV2();

    private async setDatabaseV2(): Promise<Db> {
        if (config.get('dbV2.auth.user') && config.get('dbV2.auth.password')) {
            this.options.auth = {
                username: config.get('dbV2.auth.user'),
                password: config.get('dbV2.auth.password')
            }
        }

        try {
            const mongoDBURL = this.getMongoDbURL();
            const connection = await MongoClient.connect(mongoDBURL, this.options);
            return connection.db();
        } catch (error: unknown) {
            console.log('error', error);

            this.logger?.error(error);

        }
    }

    async getDatabaseV2(): Promise<Db> {
        return this.dbV2 ??= await this.setDatabaseV2();
    }

    // private getMongoDbURL(): string {//TODO: review this method
    //     return config.get('env') == 'staging'
    //         ? `${config.get('dbV2.host')}/${config.get('dbV2.name')}?retryWrites=true`
    //         : (config.get('dbV2.auth.user') && config.get('dbV2.auth.password'))
    //             ? `mongodb://${config.get('dbV2.auth.user')}:${config.get('dbV2.auth.password')}@${config.get('dbV2.host')}/${config.get('dbV2.name')}?retryWrites=true&w=majority`
    //             : `mongodb://${config.get('dbV2.host')}/${config.get('dbV2.name')}?retryWrites=true&w=majority`;
    // }

    private getMongoDbURL(): string {
        return config.get('dbV2.mongoUrl')

        // return `mongodb+srv://mycimencam:<EMAIL>/mycimencam?retryWrites=true`
    }
}