import {
  NotFoundException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { Document, WithId } from 'mongodb';
import moment from 'moment';
import {
  converDateFilter,
  convertFilter,
  convertParams,
  extractPaginationData,
  extractProjectionData,
  extractSortingData,
  getUser,
  setDateFilter,
  setResponse,
  t,
} from '../helpers';
import { BaseRepository } from './base.repository';
import { ServiceInterface } from '../interfaces';
import { Snitch } from '@la-pasta-module/snitchs/entities';
import { config } from 'convict-config';

export class BaseService implements ServiceInterface {
  // TODO: make logger globally accessible by all services without Dependency Injection
  protected logger: Logger;
  constructor(protected readonly repository?: BaseRepository) {
    this.logger = new Logger(this.constructor.name)
  }

  async create(data: Document): Promise<QueryResult> {
    try {
      data.enable ??= true;
      data.created_at ??= moment().valueOf();

      const newDocument = await this.repository.create(data);

      if (!newDocument.insertedId)
        throw new InternalServerErrorException(t('error.ON_CREATION'));

      if (config.get('activeSnitch')) {
        const user = getUser();

        const snitchObject: Snitch = {
          action: 'CREATE',
          user: {
            _id: user?._id || 'SYSTEME',
            category: user?.category || 300,
            firstName: user?.firstName || 'SYSTEME',
            lastName: user?.lastName || 'SUDO',
            tel: user?.tel,
          },
          newData: {
            _id: newDocument?.insertedId
          },
          collectionName: this.repository?.collectionName,
          description: `The user ${user?.firstName || user?._id} created the data with id ${newDocument?.insertedId} in the collection ${this.repository?.collectionName} `,
          created_at: moment().valueOf()
        };
        (await this.repository?.getSnitchCollection()).insertOne(snitchObject);
      }

      return setResponse(200, t('CREATED'), newDocument.insertedId);
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }


  async createMany(datas: Document[]): Promise<QueryResult> {
    try {
      datas.forEach((element) => {
        element.enable ??= true;
        element.created_at ??= moment().valueOf();
      });

      const newDocuments = await this.repository.createMany(datas);

      if (!newDocuments.insertedIds)
        throw new InternalServerErrorException(t('error.ON_CREATION'));

      if (config.get('activeSnitch')) {
        const user = getUser();

        const snitchObject: Snitch = {
          action: 'CREATE',
          user: {
            _id: user?._id || 'SYSTEME',
            category: user?.category || 300,
            firstName: user?.firstName || 'SYSTEME',
            lastName: user?.lastName || 'SUDO',
            tel: user?.tel,
          },
          newData: {
            _id: newDocuments?.insertedIds
          },
          collectionName: this.repository?.collectionName,
          description: `The user ${user?.firstName || user?._id} created the data with id ${newDocuments?.insertedIds} in the collection ${this.repository?.collectionName} `,
          created_at: moment().valueOf()
        };
        (await this.repository?.getSnitchCollection()).insertOne(snitchObject);
      }

      return setResponse(200, t('CREATED'), newDocuments.insertedIds);
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async findAll(query?: QueryOptions): Promise<getAllResult> {
    try {
      query = extractPaginationData(query);
      query = extractSortingData(query);
      query = extractProjectionData(query);
      query = convertParams(query);

      if ('startDate' in query?.filter || 'endDate' in query?.filter) {
        query = setDateFilter(query);
      }

      const data = await this.repository.findAll(query ?? null);
      const count = await this.count(query.filter);

      if (typeof count == 'number') { return { data, count }; }

    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async findAllAggregate(aggregation: any): Promise<Document[]> {
    try {
      return await this.repository.findAllAggregate(aggregation ?? []);

    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async findOne(query: QueryOptions): Promise<WithId<Document>> {
    try {
      query = convertParams(query);
      query = extractSortingData(query);
      query = extractProjectionData(query);

      const document = await this.repository.findOne(query);

      if (!document) throw new NotFoundException(t('error.NOT_FOUND'));

      return document;
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async findAndModify(query: QueryOptions, data: Document): Promise<WithId<Document>> {
    try {
      query = convertParams(query);
      query = extractSortingData(query);
      query = extractProjectionData(query);

      const document = await this.repository.findAndModify(query, data);

      if (!document) throw new NotFoundException(t('error.NOT_FOUND'));

      return document;
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async count(query: QueryFilter): Promise<number | QueryResult> {
    try {
      if ('startDate' in query || 'endDate' in query) {
        query = converDateFilter(query);
      }

      const numberOfDocuments = await this.repository.count(query);

      return numberOfDocuments;
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async update(filter: QueryFilter, data: Document): Promise<QueryResult> {
    try {
      const existVerify = await this.repository.findOne({ filter });
      if (!existVerify) throw new NotFoundException(t('error.NOT_FOUND'));
      delete data?._id;

      const user = getUser();

      const updatedDocument = await this.repository.update(filter, data);

      if (config.get('activeSnitch')) {
        const snitchObject: Snitch = {
          action: 'UPDATE',
          user: {
            _id: user?._id.toString(),
            category: user?.category,
            firstName: user?.firstName,
            lastName: user?.lastName,
            tel: user?.tel,
          },
          newData: {
            ...data
          },
          oldData: existVerify,
          collectionName: this.repository?.collectionName,
          description: `The user  ${user?.firstName || user?._id} carried out a modification on the data ${existVerify?._id} from the collection ${this.repository?.collectionName}`,
          created_at: moment().valueOf()
        };
        (await this.repository?.getSnitchCollection()).insertOne(snitchObject);
      }

      if (!updatedDocument.acknowledged)
        throw new InternalServerErrorException(t('error.ON_UPDATE'));

      return setResponse(200, t('UPDATED'));
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async updateMany(filter: QueryFilter, data: Document | any[]): Promise<QueryResult> {
    try {
      // const existVerify = await this.repository.findOne({ filter });
      // if (!existVerify) throw new NotFoundException(t('error.NOT_FOUND'));
      if (!Array.isArray(data)) {
        delete data?._id;
      }

      const user = getUser();

      const updatedDocument = await this.repository.updateMany(filter, data);

      if (config.get('activeSnitch')) {
        const snitchObject: Snitch = {
          action: 'UPDATE',
          user: {
            _id: user?._id.toString(),
            category: user?.category,
            firstName: user?.firstName,
            lastName: user?.lastName,
            tel: user?.tel,
          },
          newData: {
            ...data
          },
          // oldData: existVerify,
          collectionName: this.repository?.collectionName,
          description: `The user  ${user?.firstName || user?._id} carried out a modification on the data from the collection ${this.repository?.collectionName}`,
          created_at: moment().valueOf()
        };
        (await this.repository?.getSnitchCollection()).insertOne(snitchObject);
      }

      if (!updatedDocument.acknowledged)
        throw new InternalServerErrorException(t('error.ON_UPDATE'));

      return setResponse(200, t('UPDATED'));
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    query = converDateFilter({ filter: query });

    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async verifyDataExist(filter: QueryFilter) {
    try {
      const document = await this.repository.findOne({ filter });
      if (document) return false;
    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }

}
