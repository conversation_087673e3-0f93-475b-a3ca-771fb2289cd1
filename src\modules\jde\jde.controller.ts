import { Controller, Get, UseGuards, Query, Inject, Post, Body } from '@nestjs/common';
import { JdeService } from './jde.service';
import { JwtGuard } from '@la-pasta-module/auth';
import { ClientProxy } from '@nestjs/microservices';
import { keyEventJDE } from '@la-pasta-module/order/events';
import { LoadsDispatchedDto, ReportOutboundsDto } from './dto';
import { Order } from '@la-pasta-module/order';
import { ReloadBalance } from '@la-pasta-module/reload-balance/entities/reload-balance.entity';

@Controller('jde')
export class JdeController {
  constructor(
    private readonly jdeService: JdeService,
    @Inject('JDE') private readonly JDEService: ClientProxy,

  ) { }

  @UseGuards(JwtGuard)
  @Get('bill')
  getBillEdition(@Query() query: QueryFilter) {
    const { documentNumber } = query;
    return this.JDEService.send({ cmd: documentNumber ? keyEventJDE.GET_REPRINT_INVOICE : keyEventJDE.GET_BILL_EDITION },
      { ...query });
  }

  @UseGuards(JwtGuard)
  @Get('loads-not-dispatched')
  async getJdeLoadsNotDispatched(@Query() query: LoadsDispatchedDto) {
    return this.JDEService.send({ cmd: keyEventJDE.GET_LOAD_NOT_DISPATCHED }, { ...query });
  }

  @UseGuards(JwtGuard)
  @Get('loads-not-dispatched')
  getLowNotDispatched(@Query() query: LoadsDispatchedDto) {
    return this.JDEService.send({ cmd: keyEventJDE.GET_LOAD_NOT_DISPATCHED }, { ...query });
  }

  @UseGuards(JwtGuard)
  @Get('report-outbond')
  async getJdeReport(@Query() query: ReportOutboundsDto) {
    const { soldToID, CustomerStatementFromDate, CustomerStatementToDate } = query;
    return this.JDEService.send({ cmd: keyEventJDE.GET_REPORT }, { soldToID, CustomerStatementFromDate, CustomerStatementToDate });
  }

  @UseGuards(JwtGuard)
  @Post('resend-order')
  async reCreateOrderInJde(@Body() partialOrder: Partial<Order>) {

    return this.jdeService.reCreateOrderInJde(partialOrder)
  }

  @UseGuards(JwtGuard)
  @Post('payment-request-jde')
  async paymentRequestJde(@Body() reloadBalance: Partial<ReloadBalance>) {

    return await this.jdeService.paymentRequestToJde(reloadBalance?._id.toString())
  }

  @UseGuards(JwtGuard)
  @Post('check-status-payment-request-jde')
  async checkPaymentRequestJde(@Body() reloadBalance: Partial<ReloadBalance>) {

    return await this.jdeService.checkPaymentRequestToJde(reloadBalance?._id.toString())
  }
}
