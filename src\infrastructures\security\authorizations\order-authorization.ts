import { OrderAction } from '@la-pasta-module/order';
import {
  EmployeeType,
  User,
  UserCategory,
  UserRole,
} from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

export class OrderAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return (
      (
        [
          OrderAction.CREATE,
          OrderAction.UPDATE,
          OrderAction.VIEW,
          OrderAction.DELETE,
          OrderAction.CANCEL,
          OrderAction.INIT,
          OrderAction.PLAN,
          OrderAction.VALIDATE,
          OrderAction.VIEW_EMPLOYEES,
        ] as string[]
      ).includes(permission) && user.authorizations.includes(permission)
    );
  }

  authorise(user: User, permission: string): boolean {
    if (permission == OrderAction.VIEW)
      return (
        user.roles.includes(UserRole.BACKOFFICE) ||
        user.roles.includes(UserRole.CLIENT)
      );

    // if (permission == OrderAction.UPDATE)
    //   return user.roles.includes(UserRole.BACKOFFICE);

    if (permission == OrderAction.VALIDATE)
      return (
        ('isRetired' in user &&
          (user.employeeType === EmployeeType.CORDO_RH ||
            user.employeeType === EmployeeType.DRH)) ||
        user.category === UserCategory.Administrator || user.category === UserCategory.Commercial
      );

    return user.enable;
  }
}

export const OrderAuthorizationInstance = new OrderAuthorization();
