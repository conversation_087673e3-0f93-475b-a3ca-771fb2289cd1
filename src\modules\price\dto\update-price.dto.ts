import { PartialType } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsOptional } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';
import { CreatePriceDto } from './create-price.dto';

export class UpdatePriceDto extends PartialType(CreatePriceDto) {
  @IsOptional()
  _id: string;

  @IsOptional()
  @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
  enable: boolean;

  @IsOptional()
  @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
  created_at:number;

  @IsOptional()
  @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
  updated_at: number;
}
