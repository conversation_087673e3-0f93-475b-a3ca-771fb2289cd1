import { ExpressExchangeTransaction } from "../entities/express-exchange.entity";

export class EexTransactionEvent {
    amount?: number
    cimencam_account?: string;
    agency: string;
    city: string;
    reference?: string;
    transaction_date?: string;
    client_actor?: string;
    client_tel?: number | string;
    companyName?: string;
    email?: string;
    constructor(
        public eexTransaction: ExpressExchangeTransaction
    ) {
        this.amount = eexTransaction?.amount;
        this.cimencam_account = eexTransaction?.cimmencamAccount;
        this.agency = eexTransaction?.address?.agency;
        this.city = eexTransaction?.address?.city;
        this.reference = eexTransaction?.referenceKey;
        this.transaction_date = eexTransaction?.transactionDate;
        this.client_actor = eexTransaction?.user?.firstName;
        this.client_tel = eexTransaction?.user?.tel;
        this.companyName = eexTransaction?.company?.name;
        this.email = eexTransaction?.user?.email;
    }
}