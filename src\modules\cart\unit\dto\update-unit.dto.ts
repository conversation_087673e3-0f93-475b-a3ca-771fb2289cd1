import { PartialType } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';
import { CreateUnitDto } from './create-unit.dto';

export class UpdateUnitDto extends PartialType(CreateUnitDto) {
  @IsOptional()
  _id: string;

  @IsOptional()
  @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
  enable: boolean;

  @IsOptional()
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  created_at:number;

  @IsOptional()
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  updated_at: number;
}
