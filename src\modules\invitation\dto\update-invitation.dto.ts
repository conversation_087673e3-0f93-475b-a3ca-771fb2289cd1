import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { InvitationStatus } from '../entities/invitation.entity';

export class UpdateInvitationDto {
    @ApiProperty({ type: Number, enum: InvitationStatus, required: false })
    @IsOptional()
    @IsEnum(InvitationStatus)
    status?: InvitationStatus;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    referredUserId?: string;
}