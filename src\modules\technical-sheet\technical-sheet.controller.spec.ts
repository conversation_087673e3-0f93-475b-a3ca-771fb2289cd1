import { Test, TestingModule } from '@nestjs/testing';
import { TechnicalSheetController } from './technical-sheet.controller';
import { TechnicalSheetService } from './technical-sheet.service';

describe('TechnicalSheetController', () => {
  let controller: TechnicalSheetController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TechnicalSheetController],
      providers: [TechnicalSheetService],
    }).compile();

    controller = module.get<TechnicalSheetController>(TechnicalSheetController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
