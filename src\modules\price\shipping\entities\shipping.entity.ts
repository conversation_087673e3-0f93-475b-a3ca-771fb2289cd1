import { Store } from "@la-pasta-module/cart";
import { CompanyCategory } from "@la-pasta-module/companies";
import { UserCategory } from "@la-pasta-module/users";

export class GlobalShipping {
    _id?: string;
    deliveryDate?: string;
    startRef: Partial<Store>;
    endRef: string;
    label: string;
    amount: number;
    location?: string;
    tel?: string;
    enable?: boolean;
    create_at?: number;


    static constructPartialShipping(currentShipping: any) {
        const newShipping: CompanyShipping = {
            _id: currentShipping?._id,
            startRef: currentShipping?.startRef,
            erpShipToId: currentShipping?.erpShipToId,
            erpShipToDesc: currentShipping?.erpShipToDesc,
            amount: currentShipping?.amount,
            companyId: currentShipping?.company?._id.toString(),
            category: currentShipping?.category,
            endRef: currentShipping?.endRef,
            label: currentShipping?.label,
            enable: currentShipping?.enable
        }
        return newShipping;
    }
}

export class CompanyShipping extends GlobalShipping {
    companyId: string;
    deliveryDate?: string;
    category: CompanyCategory;
    erpShipToId: number;
    erpShipToDesc: string;
}

export class ParticularShipping extends GlobalShipping {
    category: UserCategory;
    deliveryDate?: string;
    address: Address;
}

export declare type Shipping = GlobalShipping | CompanyShipping | ParticularShipping;