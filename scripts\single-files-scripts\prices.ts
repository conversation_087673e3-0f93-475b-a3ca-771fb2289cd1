import readline from 'readline';
import { insertPricesDataFile } from 'scripts/price-offers/price-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données des prix de Click CADYST, ce processus effacera la collection prices.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertPricesDataFile();
      console.log('Données des prix insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();