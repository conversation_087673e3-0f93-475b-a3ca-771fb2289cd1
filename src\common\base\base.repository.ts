import { Injectable } from '@nestjs/common';
import {
  Collection,
  Document,
  InsertManyResult,
  InsertOneResult,
  ObjectId,
  UpdateResult,
  WithId,
} from 'mongodb';
import { MongoDatabase } from 'src/database/database-mongodb';
import { RepositoryInterface } from '../interfaces';
import { MongoDatabaseCadystLogistic } from '@la-pasta/database/database-mongodb-cadyst-logistic';

@Injectable()
export class BaseRepository implements RepositoryInterface {
  collectionName: string;
  private snitchCollectionName = 'snitchs';

  public database: MongoDatabase;


  constructor() {
    if (!this.collectionName) this.collectionName = this.getCollectionName();
    this.database = MongoDatabase.getInstance();
  }

  async create(document: Document): Promise<InsertOneResult<Document>> {
    return await (await this.getCollection()).insertOne(document);
  }


  async createMany(document: Document[]): Promise<InsertManyResult<Document>> {
    return await (await this.getCollection()).insertMany(document);
  }

  async findAll(query?: QueryOptions): Promise<Document[]> {
    const collection = await this.getCollection();
    return collection
      .find(query?.filter ?? {})
      .project(query?.projection ?? {})
      .sort(query?.sort ?? '_id', query?.way ?? -1)
      .skip(query?.offset ?? 0)
      .limit(query?.limit ?? 0)
      .toArray();
  }

  async findAllAggregate(agregation: any): Promise<Document[]> {
    return (await this.getCollection()).aggregate(agregation ?? []).toArray();
  }

  async baseAggregation(query: QueryOptions, keyToAggragete: string): Promise<BaseAggregationResult[]> {
    const value = keyToAggragete === 'ItemNumber' ? { $toDouble: '$TransactionQuantity' } : 1;
    const matchExpression = {
      $match: {
        ...query?.filter,
        [`${keyToAggragete}`]: { $exists: true }
      }
    };

    const groupExpression = {
      $group: {
        '_id': `$${keyToAggragete}`,
        'total': {
          '$sum': value

        }
      }
    };

    const projectExpression = {
      $project: {
        "label": "$_id",
        "total": '$total',
        _id: 0
      }
    };

    const sortExpressions = { $sort: { 'total': -1 } }

    return await this.findAllAggregate([matchExpression, groupExpression, projectExpression, sortExpressions]) as unknown as BaseAggregationResult[];

  }

  async getFilterAuthorization(query: QueryFilter, keyForFilters: string[]) {
    const data = {};

    for (const idKey of keyForFilters) {
      const matchExpression = {
        $match: {
          ...query,
          [`${idKey}`]: { $exists: true },
        }
      };

      const groupExpression = {
        $group: {
          '_id': `$${idKey}`,
          'total': {
            '$sum': 1
          }
        }
      };

      const projectExpression = {
        $project: {
          label: "$_id",
          _id: 0
        }
      }
      const result = await this.findAllAggregate([matchExpression, groupExpression, projectExpression]);
      data[`data${idKey}`] = result ?? [];
    }

    return data;

  }

  async findAndModify(query: QueryOptions, document: Document): Promise<any> {
    this.setMongoId(query.filter);
    const collection = await this.getCollection();

    return  await collection.findOneAndUpdate(query.filter, { $set: document }, { projection: query.projection ?? {} });
  }

  async findOne(query: QueryOptions): Promise<WithId<Document>> {
    this.setMongoId(query.filter);

    return (await this.getCollection()).findOne(query.filter, {
      projection: query.projection ?? {},
    });
  }

  async count(query: QueryFilter): Promise<number> {
    return (await this.getCollection()).countDocuments(query);
  }

  async update(filter: QueryFilter, document: Document): Promise<UpdateResult> {
    this.setMongoId(filter);
    return (await this.getCollection()).updateOne(filter, { $set: document });
  }

  // async updateMany(filter: QueryFilter, document: Document): Promise<any> {
  //   this.setMongoId(filter);
  //   return (await this.getCollection()).updateMany(filter, { $set: document });
  // }

  async updateMany(filter: QueryFilter, document: Document | any[]): Promise<any> {
    this.setMongoId(filter);
    if (Array.isArray(document)) {
      // If document is an aggregation pipeline, use the pipeline
      return (await this.getCollection()).updateMany(filter, document);
    } else {
      // Default behavior with $set
      return (await this.getCollection()).updateMany(filter, { $set: document });
    }
  }

  async updateDeleteFeild(
    filter: QueryFilter,
    document: Document,
  ): Promise<UpdateResult> {
    this.setMongoId(filter);

    return (await this.getCollection()).updateOne(filter, { $unset: document });
  }

  protected async getCollection(): Promise<Collection<Document>> {
    return (await this.database.getDatabase()).collection(this.collectionName);
  }

 

  async getSnitchCollection(): Promise<Collection<Document>> {
    return (await this.database.getDatabase()).collection(this.snitchCollectionName);
  }

  protected getCollectionName(): string {
    const className = this.constructor.name;
    const regexResult = className.match(/[A-Z][a-z]+/g);
    if (!regexResult) return '';

    let collectionName = '';
    const match = regexResult[1];

    collectionName = match === 'Repository'
      ? className.replace(match, '').toLowerCase()
      : className.replace(`${match}Repository`, `_${match}`).toLowerCase();

    return collectionName.endsWith('y')
      ? collectionName.replace('y', 'ies')
      : collectionName + 's';
  }

  private setMongoId(filter: QueryFilter): void {
    if ('_id' in filter && typeof filter?._id === 'string') {
      filter._id = new ObjectId(filter?._id);
    }
  }
}
