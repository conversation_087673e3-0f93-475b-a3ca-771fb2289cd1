import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { SnitchsService } from './snitchs.service';
import { CreateSnitchDto } from './dto/create-snitch.dto';
import { UpdateSnitchDto } from './dto/update-snitch.dto';

@Controller('snitchs')
export class SnitchsController {
  constructor(private readonly snitchsService: SnitchsService) { }

  @Post()
  create(@Body() createSnitchDto: CreateSnitchDto) {
    return this.snitchsService.create(createSnitchDto);
  }

  @Get()
  findAll() {
    return this.snitchsService.findAll();
  }

}
