#!/bin/bash

# Store the name of the current branch
branch=$(git rev-parse --abbrev-ref HEAD)

if [[ "$branch" =~ ^(feature/|bugfix/) ]]; then
	# Checkout to develop branch
	git checkout develop

	# Pull latest changes from the origin develop branch
	git pull origin develop

	# Checkout back to the current branch
	git checkout "$branch"

	# Attempt to rebase current branch onto develop
	if git rebase develop; then
		echo "Rebase successful, proceeding with push..."
		exit 0
	else
		echo "Rebase failed. Resolve conflicts and continue the rebase manually."
		exit 1
	fi
fi

exit 0
