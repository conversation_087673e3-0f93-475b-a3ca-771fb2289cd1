import { faker } from '@faker-js/faker';
import { MongoDatabase } from '@la-pasta/database';
import { BaseUser, UserCategory, UserRole } from '@la-pasta-module/users';
import * as bcrypt from 'bcrypt';
import moment from 'moment';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { getProfileAuthorization } from 'scripts/profiles';
import { getCompany } from 'scripts/company';

const database = MongoDatabase.getInstance();
const collectionName = 'users';

const basicUsers: any = [
  {
    "category": 300,
    "address": {
      "region": "Adamaoua",
      "city": "",
      "district": "",
      "commercialRegion": null
    },
    "password": "$2b$10$BntVE3/.EB/A33GORCocLO6viy/9NqsBT2Merd8kk8qGY/JET35cG",
    "tel": '*********',
    "email": "<EMAIL>",
    "matricule": "123456",
    "create_at": {
      "$numberLong": "1587978742000"
    },
    "roles": [
      "backoffice"
    ],
    "authorizations": [
      "create_user",
      "view_user",
      "delete_user",
      "change_password",
      "update_user",
      "view_order",
      "delete_order",
      "update_order",
      "validate_order",
      "view_faq",
      "create_faq",
      "update_faq",
      "admin_validate_order_retail",
      "view_customers_order",
      "create_price",
      "delete_price",
      "update_price",
      "view_price",
      "create_location",
      "delete_location",
      "update_location",
      "view_location",
      "create_company",
      "delete_company",
      "update_company",
      "view_company",
      "view_company_user",
      "add_user",
      "create_packaging",
      "delete_packaging",
      "update_packaging",
      "view_packaging",
      "create_product",
      "delete_product",
      "update_product",
      "view_product",
      "create_store",
      "delete_store",
      "update_store",
      "view_store",
      "create_unit",
      "delete_unit",
      "update_unit",
      "view_unit",
      "create_shipping",
      "view_shipping",
      "update_shipping",
      "delete_shipping",
      'create_technicalsheet',
      'update_technicalsheet',
      'delete_technicalsheet',
      'view_technicalsheet',
      'create_category',
      'update_category',
      'delete_category',
      'view_category',
    ],
    "enable": true,
    "created_at": {
      "$numberLong": "1690721417203"
    },
    "firstName": "ADMIN"
  },
  {
    "email": "<EMAIL>",
    "firstName": "",
    "lastName": "Client 1",
    "password": "$2b$10$H2WyZ42jAnVfLm8Ly09tFuGZ7tbPkhCEYH8i3QP6bEXrNIwlVNfVm",
    "cni": "",
    "nui": "",
    "tel": '*********',
    "matricule": "123458",
    "address": {
      "region": "",
      "city": "",
      "district": "",
      commercialRegion: "",
    },
    "category": 3,
    "company": {
      "_id": "650b1ed4717657f1a46a5972",
      "category": 101
    },
    "authorizations": [
      "view_product",
      "update_user",
      "view_user",
      "update_company",
      "view_order",
      "view_faq",
      "init_order",
      "plan_order",
      "create_order",
      "view_price",
      "view_store",
      "view_unit",
      "view_shipping",
      "custom_planification",
      "account_pay"
    ],
    "roles": [
      "client"
    ],
    "enable": true,
    "created_at": {
      "$numberLong": "*************"
    }
  },
  {
    "email": "<EMAIL>",
    "firstName": "Guy",
    "lastName": "Landry",
    "password": "$2b$10$XPUP.dVSRNb/v.ZrNnqQxeDMhZ0vkmaBTkOwbKuxGNF3hzw6a6bJG",
    "cni": 123,
    "nui": 123456,
    "address": {
      "region": "Centre",
      "city": "Yaoundé",
      "district": "",
      "commercialRegion": "R2"
    },
    "category": 3,
    "company": {
      "_id": "650b1ed4717657f1a46a5973",
      "category": 102
    },
    "tel": *********,
    "authorizations": [
      "view_product",
      "update_user",
      "view_user",
      "update_company",
      "view_order",
      "view_faq",
      "init_order",
      "plan_order",
      "create_order",
      "view_price",
      "view_store",
      "view_unit",
      "view_shipping",
      "custom_planification",
      "account_pay"
    ],
    "roles": [
      "client"
    ],
    "enable": true,
    "created_at": {
      "$numberLong": "*************"
    }
  }];

export function setRole(category: number) {
  if (category < 10) return [UserRole.CLIENT];

  if (category === UserCategory.Commercial) return [UserRole.BACKOFFICE, UserRole.CLIENT];

  return [UserRole.BACKOFFICE];
}

export function getUsesrCategories() {
  return Object.values(UserCategory).filter(
    (value) => typeof value != 'string',
  ) as number[];
}

let users: Omit<BaseUser, '_id'>[] = [];

export async function insertUsers(...userCategories: number[]) {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des utilisateurs\n');

    if (!userCategories.length) userCategories = getUsesrCategories();

    for await (const category of userCategories) {
      for (let i = 0; i <= 5; i++) {
        const gender = faker.name.sex() as 'female' | 'male';
        const firstName = faker.name.firstName(gender);
        const lastName = faker.name.lastName(gender);

        const user = {
          firstName,
          lastName,
          email: faker.internet.email(firstName, lastName),
          password: await bcrypt.hash('123456', 10),
          cni:
            faker.helpers.arrayElement(['LT', 'YD']) + faker.random.numeric(6),
          nui: faker.random.numeric(12),
          category,
          roles: setRole(category),
          authorizations: (await getProfileAuthorization(category)) ?? [
            'unauthorize',
          ],
          enable: true,
          address: {
            region: '',
            city: '',
            district: '',
          },
        }

        if (category === UserCategory.CompanyUser) {
          const company = await getCompany({ name: `Distributeur ${i + 1}` });
          user['company'] = { name: company?.name, category: company?.category, _id: company?._id.toString() };
          user['authorizations'] = await getProfileAuthorization(company?.category,);
        }
        users.push(user);
      }
    }

    users = users.concat(basicUsers);

    users.forEach((user) => {
      user.enable = true;
      user.created_at = moment().valueOf();
    });
    const insertedUsers = await (await database.getDatabase())
      .collection(collectionName)
      .insertMany(users);
    stopLoader(insertedUsers);
  } catch (error) {
    console.error(error);
  }
}

