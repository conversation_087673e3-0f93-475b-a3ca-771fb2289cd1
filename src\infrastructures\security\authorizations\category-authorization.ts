import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';
import { CategoryAction } from '@la-pasta-module/category/actions';

class CategoryAuthorization implements AuthorizationInterface {
    support(user: User, permission: string): boolean {
        return ([
            CategoryAction.CREATE,
            CategoryAction.DELETE,
            CategoryAction.UPDATE,
            CategoryAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
    }

    authorise(user: User, permission: string): boolean {
        return user.roles.includes(UserRole.BACKOFFICE);
    }
}

export const CategoryAuthorizationInstance = new CategoryAuthorization();