import { PartialType } from '@nestjs/mapped-types';
import { IsArray, IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';
import { CreateCompanyDto } from './create-company.dto';
import { Option } from '@la-pasta-module/options/entities';

export class UpdateCompanyDto extends PartialType(CreateCompanyDto) {
  @IsOptional()
  _id: string;

  @IsOptional()
  @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
  enable: boolean;

  @IsOptional()
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  created_at: number;

  @IsOptional()
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  updated_at: number;

  @IsOptional()
  loyaltyProgDistributor: {
    city: string;
    nbResellers: number;
  };

  @IsOptional()
  option?: Option;

  @IsOptional()
  associatedShippingOption?: {
      optionId: string;
      shippingAddressId: string[];
  };
}
