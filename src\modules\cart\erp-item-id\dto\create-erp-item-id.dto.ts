import { Store } from '@la-pasta-module/cart/stores';
import { Packaging } from './../../packagings/entities/packaging.entity';
import { IsNotEmpty, IsString } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";
import { Product } from '@la-pasta-module/cart/products';

export class CreateErpItemIdDto {
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    // @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    store: Partial<Store>;


    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    // @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    product: Partial<Product>;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    // @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    packaging: Partial<Packaging>;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    value: number;

}
