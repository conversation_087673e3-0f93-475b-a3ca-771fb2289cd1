import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ReportingOrdersService } from './reporting-orders.service';
import { Permission } from '@la-pasta/infrastructures/security';
import { getUser } from '@la-pasta/common';
import { ReportingAction } from '../actions';
import { JwtGuard } from '@la-pasta-module/auth';

@Controller('reporting')
@UseGuards(JwtGuard)
export class ReportingOrdersController {
    constructor(private readonly reportingOrdersServices: ReportingOrdersService) { }

    @Get('payment-mode')
    async paymentMode(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getPayementMode(query);
    }

    @Get('cimencam-mode')
    async cimencamMode(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getCimencamMode(query);
    }
    @Get('evolution-sale-users')
    async orders(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getSalesEvolutionByUsers(query);
    }

    @Get('evolution-sales_products')
    async product(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getEvolutionProduct(query);
    }

    @Get('evolution-sales_products_particular')
    async productParticular(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getEvolutionProductParticularByScannerOrder(query);
    }

    @Get('evolution-product_total')
    async getProductTotal(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getProductTotal(query);
    }

    @Get('evolution-product_total_particular')
    async getProductTotalParticular(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getProductTotalParticular(query);
    }

    @Get('evolution-sales_payment')
    async salePayment(@Query() query: QueryFilter) {
        // Permission.ReportingAuthorization(getUser(), ReportingAction.VIEW_REPORTING_AMOUNT);

        return await this.reportingOrdersServices.getEvolutionSalePayment(query);
    }

    @Get('evolution-sales_payment_particular')
    async salePaymentParticular(@Query() query: QueryFilter) {
        Permission.ReportingAuthorization(getUser(), ReportingAction.VIEW_REPORTING_AMOUNT);

        return await this.reportingOrdersServices.getEvolutionSalePaymentParticular(query);
    }
    @Get('evolution-region')
    async region(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getEvolutionRegion(query);
    }

    @Get('evolution-payement')
    async payement(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getEvolutionPayement(query);
    }

    @Get('getTotalSale')
    async sale(@Query() query: QueryFilter) {
        Permission.ReportingAuthorization(getUser(), ReportingAction.VIEW_REPORTING_AMOUNT);
        return await this.reportingOrdersServices.getTotalSales(query);
    }

    @Get('getTotalSale-employees')
    async saleEmployees(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getTotalSalesEmployees(query);
    }

    @Get('user-reporting')
    async getReportingUser(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getTotalUsers(query);
    }

    @Get('user-reporting-employees')
    async getReportingUserEmployees(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getTotalUsersEmployees(query);
    }


    @Get('cement-volume')
    async getVolume(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getCementSalesVolumes(query);
    }

    @Get('cement-volume-particular')
    async getVolumeParticular(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getCementSalesVolumesParticular(query);
    }

    @Get('rankings')
    async getRankings(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getRankings(query);
    }

    @Get('supplier-points')
    async getSupplierByPoints(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getSupplierByPoints(query);
    }

    @Get('distributor-volume')
    async getDistributorByVolume(@Query() query: QueryFilter) {
        Permission.ReportingAuthorization(getUser(), ReportingAction.DISTRUTOR_VOLUME); (getUser(), ReportingAction.DISTRUTOR_VOLUME);

        return await this.reportingOrdersServices.getDistributorsByVolume(query);
    }

    @Get('total-quantity')
    async getQuantitiesOrderRetail(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getQuantitiesOrderSupplier(query);
    }

    @Get('product-quantity')
    async getQuantitiesByProduct(@Query() query: QueryFilter) {
        Permission.ReportingAuthorization(getUser(), ReportingAction.PRODUCT_QUANTITY); (getUser(), ReportingAction.PRODUCT_QUANTITY);

        return await this.reportingOrdersServices.getQuantitiesByProduct(query);
    }
    @Get('product-evolution-supplier')
    async getProductsEvolutions(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getProductsEvolutionSupplier(query);
    }

    @Get('points-evolution')
    async getEvolutionPoints(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getEvolutionPoints(query);
    }

    @Get('total-quantity-evolution')
    async getQuantitiesEvolutionOrderRetail(@Query() query: QueryFilter) {
        Permission.ReportingAuthorization(getUser(), ReportingAction.TOTAL_QUANTITY_EVOLUTION); (getUser(), ReportingAction.TOTAL_QUANTITY_EVOLUTION);
        return await this.reportingOrdersServices.getQuantitiesEvolutionOrderRetail(query);
    }

    @Get('avg-time-validation')
    async getAvgTimeValidations(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getAvgTimeValidations(query);
    }

    @Get('avg-time-validation_particular')
    async getAvgTimeValidationsParticular(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getAvgTimeValidationsParticular(query);
    }

    // @Get('getTotalUser')
    // async   totalUser() {
    //     return await this.reportingServices.getTotalUsers();
    // }

    @Get('export-recap')
    async getExportRecapOrder(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getExportRecapOrder(query);
    }

    @Get('top-donut-animators-by-mami-count')
    async getTopDonutAnimatorsByMamiCount(@Query() query: QueryFilter) {
        return await this.reportingOrdersServices.getTopDonutAnimatorsByMamiCount(query);
    }
}
