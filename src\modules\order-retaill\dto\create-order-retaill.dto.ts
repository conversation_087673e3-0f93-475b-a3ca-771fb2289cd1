import { Packaging } from '@la-pasta-module/cart/packagings';
import { Company } from '@la-pasta-module/companies';
import { User } from '@la-pasta-module/users';
import { IsNotEmpty, IsOptional } from "class-validator";

import {
  CartItemReseller,
  ResellerOrderStatus,
} from '../entities/order-retaill.entity';

export class CreateOrderRetaillDto {
  appRef: string;

  @IsOptional()
  custumerRef?: string;

  @IsNotEmpty()
  distributors: Partial<Company>;

  @IsNotEmpty()
  packaging: Partial<Packaging>;

  @IsNotEmpty()
  adresse: {
    city: string;
    region: string;
  };

  status: ResellerOrderStatus;
  @IsNotEmpty()
  user: Partial<User> | string;

  @IsNotEmpty()
  items: CartItemReseller[];
  validation?: {
    prevalidate: {
      date: number;
      user: Partial<User> | string;
      raison: string;
    };
    validate: {
      date: number;
      user: Partial<User> | string;
      raison: string;
    };
    reject: {
      date: number;
      user: Partial<User> | string;
      raison: string;
    };
  };

  @IsOptional()
  points?: {
    ordered: number;
  };
}
