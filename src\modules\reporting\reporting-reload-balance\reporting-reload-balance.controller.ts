import { Controller, Get, Query } from '@nestjs/common';
import { ReportingReloadBalanceService } from './reporting-reload-balance.service';

@Controller('reporting-reload-balance')
export class ReportingReloadBalanceController {
  constructor(private readonly reportingReloadBalanceService: ReportingReloadBalanceService) { }

  @Get('evolution-reloadBalance')
  async reloadBalance(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getEvolutionPayementReloadBalance(query);
  }

  @Get('evolution-reload-companies')
  async reloadCompanies(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getSalesEvolutionByCompanies(query);
  }

  @Get('evolution-amount-operator')
  async AmountPerOperator(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getAmountEvolutionPerOperatorReloadBalance(query);
  }

  @Get('evolution-amount-company')
  async AmountPerCompanies(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getSalesAmountEvolutionByCompanies(query);
  }

  @Get('jde-status')
  async jdeStatus(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getTotalReloadByStatusJde(query);
  }

  @Get('operator-status')
  async OperatorStatus(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getTotalReloadByStatusOperator(query);
  }

  @Get('category-amount')
  async AmountPerCategory(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getTotalAmountReloadByCategory(query);
  }

  @Get('payment-amount')
  async TotalAmountPerOperator(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getTotalAmountByPaymentMode(query);
  }
  @Get('reload-totals')
  async getAllReloadTotals(@Query() query: QueryFilter) {
    return await this.reportingReloadBalanceService.getAllReloadTotals(query);
  }

}
