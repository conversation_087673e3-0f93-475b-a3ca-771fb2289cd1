import { Module } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { ManualOrderService } from './manual-order.service';
import { ManualOrderController } from './manual-order.controller';

@Module({
  imports: [
    CacheModule.register({
      isGlobal: true, // Permet d'accéder au cache globalement dans l'application
      ttl: 0, // Pas d'expiration automatique, on gère manuellement
    }),
  ],
  controllers: [ManualOrderController],
  providers: [ManualOrderService],
})
export class ManualOrderModule {}