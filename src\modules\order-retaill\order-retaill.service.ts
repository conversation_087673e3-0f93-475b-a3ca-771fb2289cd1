import { OrderAction, OrderStatus } from '@la-pasta-module/order';
import { User, UserCategory, UsersService } from '@la-pasta-module/users';
import { BaseService, convertParams, getUser, setResponse, t } from '@la-pasta/common';
import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import moment from 'moment';
import { ArchiveSessionDto, OrderRetailRejectDto, OrderRetailValidationDto } from './dto';
import { CreateOrderRetaillDto } from './dto/create-order-retaill.dto';
import {
  CartItemReseller,
  OrderRetaill,
  ResellerOrderStatus,
} from './entities';
import { OrderCreatedEvent } from './events/order-created.event';
import { OrderRejectedEvent } from './events/order-rejected.event';
import { OrderValidatedByCimenEvent } from './events/order-validatedBy-cimen.event .ts';
import { OrderValidatedByDistrbEvent } from './events/order-validatedBy-distrb.event ';
import { OrderRetailRepository } from './repository/order-retaill.repository';
import { LoyaltyProgramRepository } from './repository';
import { ObjectId } from 'mongodb';

@Injectable()
export class OrderRetaillService extends BaseService {
  constructor(
    private readonly orderRetailRepository: OrderRetailRepository,
    private readonly loyaltyProgramRepository: LoyaltyProgramRepository,
    private readonly userService: UsersService,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,
  ) {
    super(orderRetailRepository);
  }

  async getOrderRetails(query: QueryOptions) {
    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }

    return await this.findAll(query);
  }

  async createRetaill(createOrderRetaillDto: CreateOrderRetaillDto) {
    createOrderRetaillDto.appRef = this.generateAppReference(
      createOrderRetaillDto?.adresse?.city,
    );
    createOrderRetaillDto.status = ResellerOrderStatus.CREATED;
    createOrderRetaillDto.points = {
      ordered: this.computePoints(
        createOrderRetaillDto.items,
        createOrderRetaillDto.packaging.unit.value,
      ),
    };

    const res = await this.create(createOrderRetaillDto);
    const user = (await this.userService.findOne({
      filter: { _id: createOrderRetaillDto.user },
    })) as unknown as User;
    this.queueClient.emit(
      'order_retail_created',
      new OrderCreatedEvent(createOrderRetaillDto, {
        fullName: `${user.firstName} ${user.lastName}`,
        email: user.email,
      }),
    );
    this.sendSmsNotificationCreateOrderToRetail(createOrderRetaillDto, user);

    return setResponse(HttpStatus.CREATED, t('PAYMENT_RETAIL_INIT'), res.data);
  }

  async getUserOrders(user: User, query: QueryOptions) {

    if (user?._id) {
      query.filter["$or"] = [
        { "user._id": user._id.toString() },
        { "user": user._id.toString() }
      ];

    }


    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }

    return await this.findAll(query);
  }

  sendSmsNotificationCreateOrderToRetail(createOrderRetaillDto: CreateOrderRetaillDto, user: User) {
    const itemsStr = createOrderRetaillDto?.items.map((item) => {
      const qty = item?.quantity * (createOrderRetaillDto?.packaging?.unit?.value / 1000);
      return `${item?.product?.label}: ${qty.toFixed(2)}t`;
    })
      .join(",");

    this.queueClient.emit('sms_received',
      { receiver: user?.tel, message: `Votre commande est enregistrée !.\n Bonjour, La commande ${createOrderRetaillDto?.appRef} de ${itemsStr} a été créé . Merci pour votre confiance..` });

  }
  sendSmsNotificationValidateOrderToRetail(createOrderRetaillDto: CreateOrderRetaillDto | OrderRetaill, user: User) {
    const itemsStr = createOrderRetaillDto?.items.map((item) => {
      const qty = item?.quantity * (createOrderRetaillDto?.packaging?.unit?.value / 1000);
      return `${item?.product?.label}: ${qty.toFixed(2)}t`;
    })
      .join(",");

    this.queueClient.emit('sms_received',
      { receiver: user?.tel, message: `Bonjour, La commande ${createOrderRetaillDto?.appRef} de ${itemsStr} a été validée .\n Merci de faire confiance à Cimencam.` });

  }

  sendSmsNotificationRejectOrderToRetail(createOrderRetaillDto: OrderRetaill, user: User) {
    this.queueClient.emit('sms_received',
      { receiver: user?.tel, message: `Bonjour, Le grossiste ${createOrderRetaillDto?.distributors?.name} a rejeté la commande N° ${createOrderRetaillDto?.appRef} .\n Merci de faire confiance à Cimencam.` });
  }

  async RejectOrder(id: string, orderToReject: OrderRetailRejectDto) {
    const order = await this.getUnValidatedOrder(id);
    const user = getUser();

    const reject = {
      raison: orderToReject.rejectMessage,
      date: moment().valueOf(),
      user: user._id,
    };
    const users = (await this.userService.findOne({
      filter: { _id: order.user },
    })) as unknown as User;
    this.queueClient.emit(
      'order_retail_rejected',
      new OrderRejectedEvent(order, {
        fullName: `${users.firstName} ${users.lastName}`,
        email: users.email,
      }),
    );
    this.sendSmsNotificationRejectOrderToRetail(order, user)

    return await this.update(
      { _id: order._id },
      { status: ResellerOrderStatus.REJECTED, reject },
    );
  }

  async validateOrder(
    id: string,
    orderToValidate: OrderRetailValidationDto,
    createOrderRetaillDto?: CreateOrderRetaillDto,
  ) {
    const order = await this.getUnValidatedOrder(id);
    const user = getUser();
    order.items = orderToValidate.cart;

    const res = await this.updateValidationStatus(order, user);

    if (user.category == UserCategory.Commercial) {
      const users = (await this.userService.findOne({
        filter: { category: UserCategory.Administrator },
      })) as unknown as User;
      this.queueClient.emit(
        'order_retail_validated_distrb',
        new OrderValidatedByDistrbEvent(createOrderRetaillDto, {
          fullName: `${users?.firstName} ${users?.lastName}`,
          email: users?.email,
        }),
      );
      this.sendSmsNotificationValidateOrderToRetail(createOrderRetaillDto, user);
    }

    if (user.category == UserCategory.Administrator) {
      const users = (await this.userService.findOne({
        filter: { _id: order.user },
      })) as unknown as User;
      this.queueClient.emit(
        'order_retail_validated_cimen',
        new OrderValidatedByCimenEvent(order, {
          fullName: `${users?.firstName}`,
          email: users?.email,
        }),
      );
      this.sendSmsNotificationValidateOrderToRetail(order, user)
    }

    return res;
  }

  private async getUnValidatedOrder(orderId: string) {
    return (await this.findOne({
      filter: { _id: orderId, status: { $ne: ResellerOrderStatus.REJECTED } },
    })) as unknown as OrderRetaill;
  }

  private async updateValidationStatus(order: OrderRetaill, user: User) {
    const userOrder = order.user as User;
    const points = this.computePoints(
      order?.items,
      order?.packaging?.unit?.value,
    );
    const validationStatus = this.getValidationStatus(user);

    if (validationStatus === ResellerOrderStatus.VALIDATED) {
      await this.userService.updateUserPoints(userOrder?._id as string, points);
    }

    await this.update(
      { _id: order?._id },
      {
        status: validationStatus,
        'points.retrieved': points,
        items: order?.items,
        validations: { user: user?._id, date: moment().valueOf() },
      },
    );

    return setResponse(HttpStatus.OK, await t('VALIDATE_ORDER_RETAILER'));
  }

  private getValidationStatus(user: User) {
    return user.authorizations.includes(OrderAction.ADMIN_VALIDATION)
      ? ResellerOrderStatus.VALIDATED
      : ResellerOrderStatus.PREVALIDATED;
  }

  private computePoints(cart: CartItemReseller[], unit: number) {
    return cart.reduce(
      (accumulator, item) =>
        accumulator +
        ((item?.quantityShipped ?? item?.quantity) / (1000 / unit)) *
        (item?.product?.fidelityPoint ?? 1),
      0,
    );
  }
  async ArchivePoint(sessions: ArchiveSessionDto) {
    const { session } = sessions;
    const users = (await this.userService.findAll({
      filter: { category: 1, points: { $gt: 0 } },
    })).data;

    const defaultSession = session ?? new Date().getFullYear() - 1;

    for (const user of users) {
      const points = 0;
      const userIdString = new ObjectId(user._id).toString();

      const loyaltyProgramUpdate = { ['points' + defaultSession]: user.points };
      const loyaltyProgramCreate = {
        userId: userIdString,
        userName: `${user?.fname} ${user?.lname} || ''`,
        userEmail: user.email,
        tel: user.tel,
        ['points' + defaultSession]: user.points
      };

      const loyaltyProgramUpdateRes = await this.loyaltyProgramRepository.update({ userId: userIdString }, loyaltyProgramUpdate);

      if (!loyaltyProgramUpdateRes?.matchedCount) {
        await this.loyaltyProgramRepository.create(loyaltyProgramCreate);
      }

      const userUpdate = { 'points': points, 'pointsArchives': user.points + (user.pointsArchives || 0) }

      await this.userService.update({ _id: user._id }, userUpdate);
    }

  }


  private generateAppReference(storeRef: string) {
    const dateStr = moment().format('YYMMDDHHmmss');
    const random = Math.floor(Math.random() * 999) + 100;
    const mapped = {
      CM10000: 'BON',
      CM10010: 'FIG',
      CM10020: 'NOM',
      CM12000: 'DEPYAO',
      CM12010: 'DEPFIG',
      CM12020: 'DEPMAR',
      CM12030: 'DEPNGA',
      CM12040: 'DEPKYE',
      CM12070: 'DEPGAB',
      CM12090: 'DEPBEL',
      CM12100: 'DEPGAR',
      CM12110: 'DEPKOU',
    };
    return (
      `${mapped[storeRef] ?? 'CUST'}${dateStr}`.substr(0, 12) + `${random}`
    );
  }

  private setDateFilter(query: QueryFilter) {
    query['created_at'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async getRecapInfosForOrderList(query: QueryOptions) {

    query.filter.status ??= { $in: [OrderStatus.CREATED, OrderStatus.PAID, OrderStatus.VALIDATED] };

    if ('status' in query.filter && typeof query.filter?.status === 'string' && query?.filter?.status?.includes('$'))
      delete query.filter.status;

    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }
    query = convertParams(query);

    const res = await this.orderRetailRepository.getRecapInfosForOrderList(query?.filter);
    return res;
  }

}
