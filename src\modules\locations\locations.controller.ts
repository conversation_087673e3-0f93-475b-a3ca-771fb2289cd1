import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { LocationsService } from './locations.service';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { ApiTags } from '@nestjs/swagger';
import { getUser, setResponseController } from '@la-pasta/common';
import { JwtGuard } from '@la-pasta-module/auth';
import { Permission } from '@la-pasta/infrastructures/security';
import { LocationAction } from './actions';

@ApiTags('locations')
@Controller('locations')
export class LocationsController {
  constructor(private readonly locationsService: LocationsService) { }

  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createLocationDto: CreateLocationDto) {
    Permission.locationAuthorization(getUser(), LocationAction.CREATE);
    return this.locationsService.createLocation(createLocationDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  findAll(@Query() query: QueryFilter) {
    Permission.locationAuthorization(getUser(), LocationAction.VIEW);
    const data = this.locationsService.getLocations({ filter: query });
    return setResponseController(data);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  async update(@Param('id') id: string, @Body() updateLocationDto: UpdateLocationDto) {
    Permission.locationAuthorization(getUser(), LocationAction.UPDATE);
    const data = await this.locationsService.update({ _id: id }, updateLocationDto);
    return setResponseController(data);
  }

}
