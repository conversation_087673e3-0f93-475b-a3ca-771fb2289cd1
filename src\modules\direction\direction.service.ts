import { BaseService } from '@la-pasta/common';
import { Injectable } from '@nestjs/common';
import { DirectionRepository } from './repository';

@Injectable()
export class DirectionService extends BaseService {

  constructor(private directionRepository: DirectionRepository) {
    super(directionRepository);
  }

  async getDirections() {
    return await this.directionRepository.getDirections();
  }

}
