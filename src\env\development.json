{"env": "development", "port": 5201, "host": "localhost", "db": {"host": "127.0.0.1:27017", "name": "la-pasta"}, "db_images": {"host": "127.0.0.1:27017", "name": "la-pasta"}, "jwt": {"secret": "n1l894y7c0br3k7", "expiration": "360h"}, "baseUrl": "http://localhost:5201/", "basePath": "api/v3", "queue": {"port": 5203, "host": "localhost"}, "payment": {"port": 5202, "host": "localhost"}, "cyberSource": {"targetOrigins": ["http://localhost:4200", "http://localhost:8100"]}, "jde": {"port": 5204, "host": "localhost"}, "cron": {"port": 5205, "host": "localhost"}, "time_otp_expire": {"value": 15, "format": "minutes"}, "londo": {"login": "LONDO", "password": "Vui2na3t0lrNn7Cxl"}, "afriland": {"login": "Afriland Server", "password": "Ex8pJGlupP9u"}, "eexTransaction": {"login": "express_exchange_mcm", "password": "C9FVCi@iKhZ}6Nj$mQZayn1"}, "rabbitmq": {"hostname": "localhost", "port": 5672, "protocol": "amqp", "username": "", "password": ""}, "minimalMobileBuildVersion": {"android": "1.0.4", "ios": "1.0.2"}, "activeSnitch": true, "isMockOtp": true, "isDisableOrderProcess": false, "isSendingDataViaEndpoint": false}