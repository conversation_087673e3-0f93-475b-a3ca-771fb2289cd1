import readline from 'readline';
import { insertUnitsDataFile } from 'scripts/units/unit-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données des unitités de Click CADYST, ce processus effacera la collection units.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertUnitsDataFile();
      console.log('Données des unitités insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();