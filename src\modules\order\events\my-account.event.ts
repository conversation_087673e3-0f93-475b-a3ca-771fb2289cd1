import { Cart } from "@la-pasta-module/price/compute_price";
import moment from "moment";
import { Order } from "../entities";
import { Logo } from "@la-pasta-module/logo/entities/logo.entity";
import { CompanyCategory } from "@la-pasta-module/companies";
import { CompanyEmployee, User } from "@la-pasta-module/users";
import { splitArray } from "@la-pasta/common";

export class MyAccountPaymentEvent {
  order_ref: string;
  internal_reference: string;
  order_paid_date: string;
  order_retrievement_point: string;
  order_products: {
    item: string;
    quantity: number;
    amount: string;
    packaging: string;
  }[];
  order_point: string;
  client_name: string;
  client_email: string;
  subtotal: string;
  transport: string;
  total_ht: string;
  tva: string;
  total: string;
  number_months: number;
  date_start: number | string;
  payment_mode: string;

  client_logo = '';
  client_has_logo = '';
  client_signature = '';
  client_has_signature = '';
  admin_signature = '';
  admin_has_signature = '';
  client_rccm = '';
  client_niu = '';
  client_cni = 'user.cni';
  client_pobox = 'Bp : 2346 YAOUNDE';
  client_contact = '';
  client_seal_signature = '';
  client_seal_fullname = '';
  client_seal_position = 'Client';
  user_full_name_seal = '';
  admin_seal_position = 'Adminstrateur de vente';
  client_code = '';

  bill_to_label = `À l'attention de : `;
  provider_name = 'LA PASTA SA';
  provider_rccm = 'RCCM : 3391';
  provider_niu = 'NIU : M066300000649C';
  // provider_address = 'Z.I. MAGZI BONABERI';
  provider_pobox = 'Bp : 1323 DOUALA';
  provider_tel = 'Tel : (237) 79 76 35 81 ';
  provider_fax = 'Fax : 33 39 09 84 / 33 39 06 08';
  provider_email = '<EMAIL> / <EMAIL>';
  invoice_title = 'BON DE COMMANDE';

  issue_date_label = 'Date Commande : ';
  issue_date = '';
  due_date_label = 'Livraison souhaitée : ';
  due_date = '';

  net_term_label = 'Total = ';
  net_term = '';
  currency_label = 'Devise paiement: ';
  currency = 'XAF';

  items = [];

  item_row_number_label = '';
  item_description_label = 'Produit(s)';
  item_quantity_bags_label = 'Qté sac(s)';
  item_quantity_convert_label = 'Ensachage(s)';
  item_price_label = 'PU';
  item_line_total_label = 'Total';
  types = '';

  amount_subtotal_label = 'Total Produit(s)';
  amount_subtotal = '';

  amount_transport_label = 'Frais de transport';
  amount_transport = '';

  amount_ht_label = 'Total HT';
  amount_ht = '';

  tax_vat_label = 'Taxes';
  tax_vat = '';

  tax_precompte_label = 'Précompte';
  tax_precompte = '';
  tax_precompte_rate;

  amount_total_label = 'Total TTC';
  amount_total;

  point_of_sale_label = 'Point de vente'
  point_of_sale;

  delivery_address_label = " Adresse de livraison"
  delivery_address;

  amount_paid_label = 'Montant Réglé';
  amount_paid;

  year_copyright;

  amount_due_label = 'Montant Du';
  amount_due = '0';

  terms_label = 'Autre info';
  terms = 'Commande effectuée sur la plateforme en ligne Click Cadyst';
  tel_receiver_label = 'Numeros Téléphone du receptioniste';
  company_address = 'CADYST SA';
  company_city_zip_state = 'Zone Inustrielle Bonabéri';
  company_phone_fax = '+237 6 90 00 00 00';
  company_email_web = '<EMAIL>';
  admin_full_name_seal = '';
  associateCommercialEmail = '';
  associateCommercialPhone: number;
  not_company_category_baker: boolean;
  tel_receiver: any;
  dailyNbrOrder: number;
  order_arrival_time: string;

  constructor(order: Order, options: { userValidate?: Logo, currentUser?: CompanyEmployee, allUserMails?: Partial<User[]>, dailyNbrOrder?: number, orderArrivalTime?: string }) {
    const { userValidate, currentUser, allUserMails, dailyNbrOrder = 0, orderArrivalTime } = options;

    this.payment_mode = 'Mon compte';
    this.order_ref = order.appReference;
    this.dailyNbrOrder = dailyNbrOrder;
    this.order_arrival_time = orderArrivalTime || this.getFormattedDate(order.dates.created);
    this.user_full_name_seal = (order?.user?.firstName ? order?.user?.firstName : '') + ' ' +
      (order?.user?.lastName ? order?.user?.lastName : '') || order?.user?.email;
    this.admin_full_name_seal = (userValidate?.user?.firstName ? userValidate?.user?.firstName : '') + ' ' +
      (userValidate?.user?.lastName ? userValidate?.user?.lastName : '') || userValidate?.user?.email;
    this.internal_reference = order.customerReference;
    this.point_of_sale = order.cart.store?.label;
    this.delivery_address = (order?.cart?.shipping?.label || order.cart.store?.address?.region) + '-' + (order?.cart?.shipping?.location || order.cart.store?.address?.city);
    this.tel_receiver = order?.cart?.shipping?.tel || '';
    this.associateCommercialEmail = order?.company?.associatedCommercial?.email
    this.associateCommercialPhone = order?.company?.associatedCommercial?.tel
    this.client_name = order?.company?.name || order.user.firstName + ' ' + order.user.lastName;
    this.client_email = splitArray(allUserMails, 'email') ?? order?.user?.email;
    this.client_code = order?.company?.erpSoldToId,
      this.order_products = this.formatItems(order.cart);
    this.order_retrievement_point = order.cart.store.label;
    this.subtotal = this.formatNumber(order.cart.amount.HT);
    this.transport = this.formatNumber(order.cart.amount.shipping);
    this.total_ht = this.formatNumber(order.cart.amount.HT);
    this.tva = this.formatNumber(order.cart.amount.VAT);
    this.total = this.formatNumber(order.cart.amount.TTC);

    this.client_rccm = order.company?.rccm;
    this.client_niu = order?.company?.nui || order.user.nui;
    this.client_cni = order.user.cni;
    this.client_has_logo = (order?.company?.logo || order?.user?.logo);
    this.client_logo = order?.company?.logo || order?.user?.logo;
    this.client_has_signature = (order?.company?.signature || order?.user?.signature);
    this.client_signature = order?.company?.signature || order?.user?.signature;
    this.not_company_category_baker = currentUser?.company?.category !== CompanyCategory.Baker
    // && (currentUser?.category === UserCategory.CompanyUser && currentUser?.category === UserCategory.Commercial);
    this.admin_has_signature = (userValidate?.value || userValidate?.user?.firstName);
    this.admin_signature = userValidate?.value || userValidate?.user?.firstName;
    this.client_contact = order.company?.tel || (order.user.tel as any);
    this.issue_date = moment(order?.dates?.created).format('DD/MM/YYYY');
    this.due_date = order?.cart?.shipping?.deliveryDate ? moment(order?.cart?.shipping?.deliveryDate).format('DD/MM/YYYY') : 'N/A';
    this.year_copyright = moment(order?.dates?.paid).format('DD/MM/YYYY');
    this.net_term = this.formatNumber(order.cart.amount.HT);
    this.amount_subtotal = this.formatNumber(order.cart.amount.HT);
    this.amount_transport = this.formatNumber(order.cart.amount.shipping);
    this.amount_ht = this.formatNumber(order.cart.amount.HT);
    this.tax_vat = this.formatNumber(order.cart.amount.VAT);
    this.tax_precompte = this.formatNumber(order.cart.amount.precompte);
    this.tax_precompte_rate = order.company?.precompteRate
      ? `(${order.company?.precompteRate}%)`
      : 'N/A';
    this.amount_total = this.formatNumber(order.cart.amount.TTC);
    this.amount_paid = this.formatNumber(order.cart.amount.TTC);

    order.cart.items.forEach((item, index) => {
      this.items.push({
        item_row_number: index + 1,
        item_description: `${item.product.label}`,
        item_quantity_tones: item?.packaging?.label,
        item_quantity_bags: `${this.formatNumber(item.quantity)} `,
        item_price: this.formatNumber(item.unitPrice),
        item_discount: '19,25%',
        item_tax: order.company?.precompteRate
          ? `${order.company?.precompteRate}%`
          : 'N/A',
        item_line_total: this.formatNumber(item.unitPrice * item.quantity)
      });
    });
  }

  private formatItems(cart: Cart) {
    return cart?.items?.map(item => {
      return {
        item: item.product.label,
        quantity: item.quantity,
        amount: this.formatNumber(item.quantity * item.unitPrice),
        packaging: item?.packaging?.label
      }
    })
  }

  private formatNumber(amount: number) {
    if (!amount) { return '0' }
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
  }

  private getFormattedDate(timestamp: number): string {
    return moment(new Date(timestamp)).format('DD/MM/YYYY');
  }
}