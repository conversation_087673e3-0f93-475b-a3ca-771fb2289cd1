
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';
import { FaqAction } from '@la-pasta-module/faq/actions/faq.action';


class FaqAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      FaqAction.CREATE,
      FaqAction.DELETE,
      FaqAction.UPDATE,
      FaqAction.VIEW] as string[]).includes(permission) && user?.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (!user.enable) return false;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const FaqAuthorizationInstance = new FaqAuthorization();