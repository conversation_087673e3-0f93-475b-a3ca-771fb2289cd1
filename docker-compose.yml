version: '3'
services:
  mongodb:
    image: 'mongo:7'
    restart: unless-stopped
    volumes:
      - /home/<USER>/la-pasta/mongo-volume:/data/db

  backoffice:
    image: 'londotech/la-pasta-backoffice-v3:test-1.0.0'
    restart: unless-stopped
    depends_on:
      - api
    ports:
      - '3005:4000'

  website:
    image: 'londotech/la-pasta-website-v3:test-1.0.0'
    restart: unless-stopped
    depends_on:
      - api
    ports:
      - '3006:4000'
      
  api:
    image: "londotech/la-pasta-api-v3:test-1.0.0"
    restart: unless-stopped
    environment:
      - NODE_ENV=staging
      - HOST=api
      - PORT=3000
      - ORANGE_CHANNELUSER=691301143
      - ORANGE_PIN=2222
      - DB_V2_MONGO_HOST=mongodb
      - DB_V2_MONGO_NAME=la-pasta
      - DB_la-pasta_USERNAME_V2=logistic
      - DB_la-pasta_PASSWORD_V2=_6FeRzrHG2wMVBTj
      - MINIMAL_MOBILE_BUILD_VERSION=1.6.10
      - FLUENTD_HOST=**************
      - DB_MONGO_NAME=la-pasta
    volumes:
      - /home/<USER>/la-pasta/logs/api:/usr/src/cimencam/logs
    #entrypoint: npm run start:staging
    entrypoint: node src/main.js
    depends_on:
      - mongodb
    ports:
      - '3004:3000'

  notif-api:
    image: "londotech/la-pasta-notif-api:test"
    restart: unless-stopped
    environment:
      - NODE_ENV=staging
      - HOST=notif-api
      - DB_MONGO_NAME=la-pasta
      - COLLECTION_MONGO_NAME=queue
      - DB_MONGO_HOST=mongodb
      - CLIENT_ID=9544980c-2e81-4625-9102-9b5774b31a2d
      - SENDER_ADDRESS=<EMAIL>
    volumes:
      - /home/<USER>/la-pasta/logs/notif-api:/usr/src/cimencam/logs
    entrypoint: npm run start:staging
    depends_on:
      - mongodb

  payment-api:
    image: "londotech/la-pasta-payment:test-1.0.0"
    restart: unless-stopped
    environment:
      - HOST=payment-api
      - NODE_ENV=staging
      - DB_MONGO_NAME=la-pasta
    volumes:
      - /home/<USER>/la-pasta/logs/payment:/usr/src/cimencam/logs
    entrypoint: npm run start:staging
    depends_on:
      - mongodb

  queue-api:
    image: "londotech/la-pasta-queue:test-1.0.0"
    restart: unless-stopped
    environment:
      - HOST=queue-api
      - NODE_ENV=staging
      - DB_MONGO_NAME=la-pasta
    volumes:
      - /home/<USER>/la-pasta/logs/queue:/usr/src/cimencam/logs
    entrypoint: npm run start:staging
    depends_on:
      - mongodb

  jde-api:
    image: "londotech/la-pasta-jde:test-1.0.0"
    restart: unless-stopped
    environment:
      - HOST=jde-api
      - NODE_ENV=staging
      - DB_MONGO_NAME=la-pasta
    volumes:
      - /home/<USER>/la-pasta/logs/jde:/usr/src/cimencam/logs
    entrypoint: npm run start:staging
    depends_on:
      - mongodb

  cron-api:
    image: "londotech/la-pasta-cron-v2:test-1.0.0"
    restart: unless-stopped
    environment:
      - HOST=cron-api
      - NODE_ENV=staging
      - DB_MONGO_NAME=la-pasta
    volumes:
      - /home/<USER>/la-pasta/logs/cron:/usr/src/cimencam/logs
    entrypoint: npm run start:staging
    depends_on:
      - mongodb

  gotenberg:
    image: 'gotenberg/gotenberg:7'
    restart: unless-stopped
