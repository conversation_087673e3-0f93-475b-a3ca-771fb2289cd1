import { Test, TestingModule } from '@nestjs/testing';
import { ImageBannerController } from './image-banner.controller';
import { ImageBannerService } from './image-banner.service';

describe('ImageBannerController', () => {
  let controller: ImageBannerController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ImageBannerController],
      providers: [ImageBannerService],
    }).compile();

    controller = module.get<ImageBannerController>(ImageBannerController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
