import { BaseRepository } from '@la-pasta/common';
import { LoyaltyReward } from '../entities/loyalty-reward.entity';
import { FidelityStatus } from '../../loyalty-program/entities';
import { Document, InsertOneResult, ObjectId, UpdateResult, WithId } from 'mongodb';

export class LoyaltyRewardRepository extends BaseRepository {
  constructor() {
    super();
    this.collectionName = 'loyalty_rewards';
  }

  async findByStatus(status: FidelityStatus): Promise<WithId<Document>> {
    return this.findOne({ filter: { status } });
  }

  async createReward(reward: LoyaltyReward): Promise<InsertOneResult<Document>> {
    return this.create(reward as Document);
  }

  async updateReward(id: string, updates: Partial<LoyaltyReward>): Promise<UpdateResult> {
    return this.update({ _id: new ObjectId(id) }, updates as Document);
  }

  async deleteReward(id: string): Promise<any> {
    return (await this.getCollection()).deleteOne({ _id: new ObjectId(id) });
  }
}