import { BaseRepository, ReportingHelpers, extractPaginationData, extractProjectionData, extractSortingData, setDateFilter } from "@la-pasta/common";
import { RenderType, StatusAES } from "../entities";

export class AuthorizationRemovalRepository extends BaseRepository {
  repository: any;
  logger: any;

  constructor(
  ) {
    super();
  }

  async getFilterAuthorization(query: QueryFilter, keyForFilters: string[]) {
    const data = {};

    for (const idKey of keyForFilters) {
      const result = await this.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;

  }

  async getRepartitionsAes(query: QueryFilter) {
    const matchExpression = {
      $match: {
        ...query,
      }
    };

    const groupExpression = {
      $group: {
        '_id': '$LoadStatus',
        'total': {
          '$sum': 1

        }

      }
    };
    return await this.findAllAggregate([matchExpression, groupExpression]);
  }



  async getEvolutionAes(query: QueryFilter) {
    let aggregateExpressions = [];
    aggregateExpressions.push({ $match: { LoadStatus: { $ne: StatusAES.REJECTED } } });
    const matchExpression = {
      $match: {
        ...query,
      }
    };

    aggregateExpressions.push(matchExpression);

    const subQuery = { $facet: {} };
    subQuery.$facet = {
      render: ReportingHelpers.getChartBaseQueryRemoval({ FreightHandlingCode: RenderType.RENDER, }),
      pickup: ReportingHelpers.getChartBaseQueryRemoval({ FreightHandlingCode: RenderType.PICKUP }),
    }
    aggregateExpressions = aggregateExpressions.concat(subQuery);
    const data = (await this.getCollection())
      .aggregate(aggregateExpressions)
      .toArray();
    return data
  }


  async getUserRemovals(query?: QueryOptions): Promise<getAllResult> {
    try {
      query = extractPaginationData(query);
      query = extractSortingData(query);
      query = extractProjectionData(query);

      if ('startDate' in query.filter || 'endDate' in query.filter) {
        query = setDateFilter(query);
      }

      const data = await this.findAll(query ?? null);
      const count = await this.count(query.filter);

      if (typeof count == 'number') { return { data, count }; }

    } catch (error) {
      this.logger.error(error);
      return error;
    }
  }


}




