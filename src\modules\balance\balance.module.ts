import { Module, forwardRef } from '@nestjs/common';
import { BalanceService } from './balance.service';
import { BalanceController } from './balance.controller';
import { BalanceRepository } from './repository';
import { CompaniesModule, CompaniesService, CompanyRepository } from '@la-pasta-module/companies';

@Module({
  controllers: [BalanceController],
  imports: [
    forwardRef(() => CompaniesModule),

  ],
  providers: [BalanceService, BalanceRepository,],
  exports: [BalanceService, BalanceRepository]
})
export class BalanceModule { }
