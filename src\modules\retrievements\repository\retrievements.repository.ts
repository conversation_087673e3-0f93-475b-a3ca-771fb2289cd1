import { BaseRepository } from "@la-pasta/common";
import { Injectable } from "@nestjs/common";

@Injectable()
export class RetrievementRepository extends BaseRepository {

  constructor() {
    super();
  }

  async getEvolutionAes(query: QueryFilter) {

    const aggregateExpressions = [
      { $match: { ...query } },
      {
        $project: {
          yearMonthDate: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: { $toDate: { $toLong: '$actualShipDate' } }
            }
          },
        }
      },
      { $group: { _id: '$yearMonthDate', total: { $sum: 1 } } },
      { $sort: { '_id': 1 } },
      { $project: { _id: 0, date: '$_id', total: 1 } }
    ];

    const data = (await this.getCollection())
      .aggregate(aggregateExpressions)
      .toArray();
    return data
  }

}