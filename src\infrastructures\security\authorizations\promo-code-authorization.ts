import { PromoCode } from './../../../modules/promo-code/entities/promo-code.entity';
import { PromoCodeAction } from '@la-pasta-module/promo-code/actions';
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';

class PromoCodeAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      PromoCodeAction.CREATE,
      PromoCodeAction.DELETE,
      PromoCodeAction.UPDATE,
      PromoCodeAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == PromoCodeAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const PromoCodeAuthorizationInstance = new PromoCodeAuthorization();