import { <PERSON>DataUR<PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";
import { TechnicalSheet } from "../entities";
import { Product } from "@la-pasta-module/cart/products";

export class CreateTechnicalSheetDto extends TechnicalSheet {

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    product: Partial<Product>;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsDataURI({ message: i18nValidationMessage('validation.BAD_DATA_URI') })
    imgPdfUrl: string;



}
