import { Module } from '@nestjs/common';
import { PackagingsService } from './packagings.service';
import { PackagingsController } from './packagings.controller';
import { PackagingRepository } from './repository';
import { UnitModule } from '../unit';

@Module({
  imports: [UnitModule],
  controllers: [PackagingsController],
  providers: [PackagingsService, PackagingRepository],
  exports: [PackagingsService, PackagingRepository]
})
export class PackagingsModule {}
