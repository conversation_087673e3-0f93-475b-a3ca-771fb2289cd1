import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, Matches, Max, Min } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";
import { AssociatedDonutAnimator } from "../entities/whole-sale.entity";

class AddressAssociatedDonutAnimator {
    @ApiProperty({ type: String })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    region: string;

    @ApiProperty({ type: String })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    city: string;

    @ApiProperty({ type: String })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    district: string;

    @ApiProperty({ type: String })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    commercialRegion: string;
}

export class CreateWholeSaleDto {

    @ApiProperty({ type: String })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    name: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    address: AddressAssociatedDonutAnimator;

    @ApiPropertyOptional({ type: Number })
    @IsNumber({}, { message: i18nValidationMessage('validation.BAD_PHONE') })
    @Min(100000000, { message: i18nValidationMessage('validation.BAD_PHONE') }) // plus petit nombre à 9 chiffres
    @Max(999999999, { message: i18nValidationMessage('validation.BAD_PHONE') }) // plus grand nombre à 9 chiffres
    @IsOptional()
    tel: number;
}

