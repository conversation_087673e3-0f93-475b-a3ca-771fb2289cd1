import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { WholeSaleService } from './whole-sale.service';
import { CreateWholeSaleDto } from './dto/create-whole-sale.dto';
import { UpdateWholeSaleDto } from './dto/update-whole-sale.dto';
import { Permission } from '@la-pasta/infrastructures/security';
import { getUser } from '@la-pasta/common';
import { WholeSaleAction } from './action';
import { JwtGuard } from '@la-pasta-module/auth';

@Controller('whole-sale')
export class WholeSaleController {
  constructor(private readonly wholeSaleService: WholeSaleService) { }

  @Post()
  @UseGuards(JwtGuard)
  create(@Body() createWholeSaleDto: CreateWholeSaleDto) {
    // Permission.wholeSaleAuthorization(getUser(), WholeSaleAction.CREATE);
    return this.wholeSaleService.createWholeSale(createWholeSaleDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  findAll(@Query() query: QueryFilter) {
    return this.wholeSaleService.findAll({ filter: query});
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  findOne(@Param('id') id: string) {
    return this.wholeSaleService.findOne({ filter: { _id: id } });
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  update(@Param('id') id: string, @Body() updateWholeSaleDto: unknown) {
    return this.wholeSaleService.update({ _id: id }, updateWholeSaleDto);
  }
}
