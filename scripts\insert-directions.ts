import readline from 'readline';
import { insertDirections, insertFonctions, insertServices } from './directions';

(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log(`* Vous êtes sur le point inséré les direction, services et poste dans la base de données. \n ce processus effacera des collections existantes.\n`);

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {
    if (answer == 'y') {
      await insertDirections();

      await insertServices();

      await insertFonctions();
    }

    prompt.close();
  })

  prompt.on('close', () => {
    process.exit()
  })
})();