import { QrCodeService } from './qr-code.service';
import { BaseUser } from '@la-pasta-module/users';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { CreateManyQrCodeDto, CreateQrCodeDto } from './dto/create-qr-code.dto';
import { UpdateQrCodeDto } from './dto/update-qr-code.dto';
import { Permission } from '@la-pasta/infrastructures/security/permission';
import { Controller, Get, Post, Body, Patch, Param, UseGuards, Query } from '@nestjs/common';


@Controller('qr-code')
export class QrCodeController {
  constructor(private readonly qrCodeService: QrCodeService) { }

  @Post()
  @UseGuards(JwtGuard)
  async createQrCode(@GetUser() user: BaseUser, @Body() createQrCodeDto: CreateQrCodeDto) {
    //Permission.qrCodeAuthorization(user, QrCodeAction.CREATE);
    return await this.qrCodeService.create(createQrCodeDto);
  }

  @Post('save-many')
  @UseGuards(JwtGuard)
  async createManyQrCode(@GetUser() user: BaseUser, @Body() createManyQrCodeDto: CreateManyQrCodeDto) {
    //Permission.qrCodeAuthorization(user, QrCodeAction.CREATE);
    return await this.qrCodeService.createMany(createManyQrCodeDto?.qrCodesData);
  }

  @Get()
  @UseGuards(JwtGuard)
  async findAllQrCode(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    //Permission.qrCodeAuthorization(user, QrCodeAction.VIEW);
    return await this.qrCodeService.findAll({ filter: query });
  }

  @Get('search-update/:code')
  @UseGuards(JwtGuard)
  searchAndUpdateScannedQrCode(@Param('code') code: string, @Query() query?: QueryFilter) {
    //Permission.qrCodeAuthorization(user, QrCodeAction.VIEW);
    const res = this.qrCodeService.findAndModifyQrCode(code, query);
    return res;
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  async findOneQrCode(@Param('id') id: string, @GetUser() user: BaseUser) {
    //Permission.qrCodeAuthorization(user, QrCodeAction.VIEW);
    return await this.qrCodeService.findOne({ filter: { _id: id } });
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  async updateQrCode(@Param('id') id: string, @Body() updateQrCodeDto: UpdateQrCodeDto) {
    //Permission.qrCodeAuthorization(getUser(), QrCodeAction.UPDATE);
    return await this.qrCodeService.update({ _id: id }, updateQrCodeDto);
  }


  @Get('by-reference/:qrCodeFileReference')
  async getQrCodesByReference(@Param('qrCodeFileReference') qrCodeFileReference: string) {
    return await this.qrCodeService.findAll({ filter: { qrCodeFileReference } });
  }

}
