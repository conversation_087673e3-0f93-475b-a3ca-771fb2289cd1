import { Company } from "@la-pasta-module/companies";
import { User } from "@la-pasta-module/users";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateExpressExchangeDto {

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    user: Partial<User>;

    @ApiProperty()
    @IsString()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    transactionId: string;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    amount: number;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    referenceKey: string;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    cimencamAccount: string;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    company: Partial<Company>;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    transactionDate: string;

    @ApiPropertyOptional()
    @IsOptional()
    transactionReason: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: Partial<Address>;

    @ApiPropertyOptional()
    @IsOptional()
    enable: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    status: string;

    @ApiPropertyOptional()
    @IsOptional()
    isReloaded: string;
}
