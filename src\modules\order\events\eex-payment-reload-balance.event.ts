import { PaymentReloadBalance, ReloadBalance, TransactionStatus } from "@la-pasta-module/reload-balance/entities/reload-balance.entity";

export class ExpressExchangePaymentEvent {
    reloadBalance: Partial<ReloadBalance>;
    status: TransactionStatus
    payment: PaymentReloadBalance;

    constructor(reloadBalance: ReloadBalance) {
        this.payment = reloadBalance?.payment;
        this.reloadBalance = {
            _id: reloadBalance?._id.toString(),
            user: reloadBalance?.user
        };
    }
}