import { MongoDatabase } from '@la-pasta/database';
import { Product } from '@la-pasta-module/cart';
import { setLoader, stopLoader } from 'scripts/common';
import { getDataInExcelFile } from '@la-pasta/common';
import { getCategories } from 'scripts/categories/categories.script';
import moment from 'moment';

const database = MongoDatabase.getInstance();
const collectionName = 'products';

export async function insertProductsDataFile() {
  try {
    setLoader('Chargement des produits depuis le fichier Excel\n');
    const productsCollection = (await database.getDatabase()).collection(collectionName)
    const datas = getDataInExcelFile('la-pasta.products') as any[];

    const categories = await getCategories();

    const imagesProduct = {
      AMIGO: 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/amigo.png?alt=media&token=6ebbc9c4-ba39-44c5-ab00-b19873a988b9',
      PELICAN: 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/pelican.png?alt=media&token=11fa8cf1-6ca2-4942-ba37-627efbe7636c',
      'LA CAMEROUNAISE': 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/la-camerounaise.png?alt=media&token=510ef645-6528-4242-8400-ffef92d60006',
      COLOMBE: 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/colombe.png?alt=media&token=d46818d9-4e6c-4b38-b5f4-f27ee5a2f4c2',
      POWER: 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/SAC%20004.png?alt=media&token=7f94aae7-426e-4657-853e-88bcdeec384f',
      SEMOULE: 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/SAC%20003.png?alt=media&token=4c65561d-579a-4387-bce9-fab2d03d74f9',
      BISCUTIERE: 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/SAC%20001.png?alt=media&token=7107a71a-4073-4c0f-9d2d-02a4a7ae5edd',
      INDUSTRIELLE: 'https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/SAC%20002.png?alt=media&token=215172ec-c95b-47fd-a1a6-2956b4c13717',
    }

    let products: Product[] = [];

    for (const data of datas) {
      const category = categories.find(category => category.code == data.category_code);
      const product: Product = {
        category,
        description: data?.description,
        label: data?.label,
        image: imagesProduct[`${data?.label}`] ?? '',
        normLabel: data?.normLabel,
        erpRef: data?.erpRef,
        specifications: data?.specifications,
        enable: true,
        create_at: moment().valueOf()
      }

      const query = {
        erpRef: data?.erpRef,
        label: data?.label,
      }

      const res = await productsCollection.updateOne(query, { $set: { ...product } }, { upsert: true });
      if (res?.matchedCount == 0 && !res?.upsertedCount)
        products.push(product);
    }

    if (products?.length)
      await productsCollection.insertMany(products as unknown[]);

    stopLoader(true);
    console.log('produits insérés')

  } catch (error) {
    console.error('Erreur lors de l\'insertion des produits :', error);
  }
}