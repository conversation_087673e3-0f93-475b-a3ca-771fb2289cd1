import { BaseRepository, ReportingHelpers } from "@la-pasta/common";


export class OrderRetailRepository extends BaseRepository {
  constructor(
  ) {
    super();
  }

  monthOfYear = {
    $dateToString: {
      format: '%m',
      date: { $toDate: { $toLong: '$created_at' } }
    }
  };

  async getRetailsByPoints(query: QueryFilter) {
    const matchExpression = {
      $match: {
        ...query
      }
    };

    const groupExpression = {
      $group: {
        '_id': '$user',
        'points': {
          '$sum': '$points.ordered'
        }
      }
    };

    const sortExpressions = { $sort: { 'points': -1 } }

    const projectExpression = {
      $project: {
        user: '$_id',
        points: 1,
        _id: 0
      }
    }

    return (await this.getCollection())
      .aggregate([matchExpression, groupExpression, sortExpressions, projectExpression])
      .skip(query.offset ?? 0)
      .limit(query.limit ?? 10)
      .toArray();
  }

  async getDistributorsByVolume(query: QueryFilter) {
    const matchExpression = {
      $match: {
        ...query
      }
    };

    const unwindExpression = {
      $unwind: {
        'path': '$items',
        'includeArrayIndex': 'string',
        'preserveNullAndEmptyArrays': false
      }
    }

    const groupExpression = {
      $group: {
        '_id': '$distributors._id',
        'name': { $first: '$distributors.name' },
        'volume': {
          '$sum': '$items.quantity'
        }
      }
    };

    const sortExpressions = { $sort: { 'volume': -1 } }

    return (await this.getCollection())
      .aggregate([matchExpression, unwindExpression, groupExpression, sortExpressions])
      .skip(query.offset ?? 0)
      .limit(query.limit ?? 10)
      .toArray();
  }

  async getQuantityOrdersRetail(query: QueryFilter) {
    const unwindExpression = {
      $unwind: {
        'path': '$items',
      }
    }

    const matchExpression = {
      $match: {
        ...query
      }
    }

    const groupExpression = {
      $group: {
        '_id': '',
        total: { $sum: { $divide: ["$items.quantity", "$packaging.unit.ratioToTone"] } }
      }
    }

    return await this.findAllAggregate([unwindExpression, matchExpression, groupExpression]);
  }

  async getQuantityEvolutionOrdersRetail(query: QueryFilter) {
    const aggregateExpression = ReportingHelpers.getChartEvolutionBaseQuery(query);
    return await this.findAllAggregate(aggregateExpression);
  }

  async getQuantitiesByProduct(query: QueryFilter) {

    const unwindExpression = {
      $unwind: {
        'path': '$items',
        'includeArrayIndex': 'string',
        'preserveNullAndEmptyArrays': false
      }
    }

    const matchExpression = {
      $match: {
        ...query
      }
    }

    const groupExpression = {
      $group: {
        '_id': '$items.product._id',
        'label': {
          '$first': '$items.product.label'
        },
        'ratioToTone': {
          '$first': '$packaging.unit.ratioToTone'
        },
        'totalInKg': {
          '$sum': '$items.quantity'
        }
      }
    }

    const projectExpression = {
      $project: {
        'label': 1,
        'totalInKg': 1,
        'totalTone': {
          '$divide': [
            '$totalInKg', '$ratioToTone'
          ]
        }
      }
    }

    const sortExpressions = { $sort: { 'totalTone': -1 } }

    return await this.findAllAggregate([unwindExpression, matchExpression, groupExpression, projectExpression, sortExpressions]);
  }

  async getEvolutionProduct(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [];
    const unwindExpressions = {
      $unwind: { path: "$items" }
    }
    aggregateExpressions.push(unwindExpressions);

    const matchExpression = {
      $match: { ...query }
    };
    aggregateExpressions.push(matchExpression);

    const addFieldsExpression = {
      $addFields: { label: "$items.product.label" }
    };
    aggregateExpressions.push(addFieldsExpression);

    const projectExpression = {
      $project: {
        monthOfYear: this.monthOfYear,
        label: 1,
        _id: 0
      }
    };
    aggregateExpressions.push(projectExpression);
    return await (await this.getCollection()).aggregate(aggregateExpressions).toArray();
  }

  async getEvolutionPoints(query: QueryFilter) {
    const aggregateExpression = ReportingHelpers.getChartBaseQuery(query);
    return await this.findAllAggregate(aggregateExpression);
  }

  async getRecapInfosForOrderList(query: QueryFilter) {
    const aggregateExpressions = [];
    const matchExpression = {
      $match: { ...query }
    }
    aggregateExpressions.push(matchExpression);

    const unwindExpressions = {
      $unwind: { path: '$items' }
    }
    aggregateExpressions.push(unwindExpressions);

    const groupExpression1 = {
      $group: {
        _id: '$_id',
        totalTonnes: { $sum: { $divide: ["$items.quantity", "$packaging.unit.ratioToTone"] } },
      }
    }
    aggregateExpressions.push(groupExpression1);

    const projectExpression1 = {
      $project: {
        totalTonnes: '$totalTonnes',
      }
    }
    aggregateExpressions.push(projectExpression1);

    const groupExpression2 = {
      $group: {
        _id: null,
        totalTonnes: { '$sum': '$totalTonnes' },
        numberOfOrders: { '$sum': 1 }
      }
    }
    aggregateExpressions.push(groupExpression2);

    const projectExpression2 = {
      $project: {
        _id: 0,
        totalAmount: { $sum: '$totalAmount' },
        totalTonnes: 1,
        numberOfOrders: 1
      }
    }
    aggregateExpressions.push(projectExpression2);

    return await this.findAllAggregate(aggregateExpressions);

  }

}



