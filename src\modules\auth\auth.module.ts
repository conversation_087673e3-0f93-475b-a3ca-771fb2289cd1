import { Module, forwardRef } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtModule } from '@nestjs/jwt';
import { JwtStrategy } from './strategy';
import { CompaniesModule } from '../companies/companies.module';
import { ResetPasswordRepository } from './repository';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { UsersModule } from '@la-pasta-module/users';
import { UserRepository } from '../users/repository/index';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => CompaniesModule),
    JwtModule.register({}),
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    ResetPasswordRepository,
    UserRepository,
  ],
})
export class AuthModule { }
