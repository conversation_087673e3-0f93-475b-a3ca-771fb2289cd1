import { IsString, IsDateString, <PERSON><PERSON>ptional, IsPhoneNumber, Matches } from 'class-validator';
import { IsNotEmpty } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';


export class LoadsDispatchedDto {
    @IsNotEmpty()
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    soldToID: number;

    @IsOptional()
    @IsDateString()
    @Matches(/^\d{4}(-)(((0)[0-9])|((1)[0-2]))(-)([0-2][0-9]|(3)[0-1])$/i, {
        message: "$property must be formatted as YYYY-MM-DD"
    })
    reportFromDate: Date;

    @IsOptional()
    @IsDateString()
    @Matches(/^\d{4}(-)(((0)[0-9])|((1)[0-2]))(-)([0-2][0-9]|(3)[0-1])$/i, {
        message: "$property must be formatted as YYYY-MM-DD"
    }) 
    @IsDateString()
    reportToDate: Date;

    @IsOptional()
    @IsPhoneNumber()
    documentNumber: Date;
    // @Matches(/^\+6[0-9]{8})$/i, {
    //     message: "$property must documentNumber be matche phone number"
    // }) 

}