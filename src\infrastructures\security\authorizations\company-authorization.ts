import { CompanyAction } from "@la-pasta-module/companies";
import { User, UserRole } from "@la-pasta-module/users";
import { AuthorizationInterface } from "./authorization.interface";


export class CompanyAuthorization implements AuthorizationInterface {

  support(user: User, permission: string): boolean {
    return ([
      CompanyAction.CREATE,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      CompanyAction.VIEW_USERS,
      CompanyAction.ADD_USER] as string[])
      .includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string, subject: any): boolean {
    if (permission == CompanyAction.VIEW) return user.enable === true;

    if (permission == CompanyAction.UPDATE && 'company' in user && '_id' in subject)
      return user.company._id.toString() == subject._id;

    return user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const CompnayAuthorizationInstance = new CompanyAuthorization();