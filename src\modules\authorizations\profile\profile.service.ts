import { Injectable } from "@nestjs/common";
import { BaseService } from "@la-pasta/common";
import { ProfileRepository } from "./profile.repository";

@Injectable()
export class ProfileService extends BaseService {
  constructor(private readonly profileRepository: ProfileRepository) {
    super(profileRepository);
  }

  async findProfiles(query: QueryOptions) {
    return (await this.findAll(query)).data;
  }
}