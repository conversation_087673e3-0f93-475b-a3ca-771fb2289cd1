import { lastValueFrom } from 'rxjs';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  Query,
  Res,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express'; // Importez le type Express.Response
import { ApiTags } from '@nestjs/swagger';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { BaseUser } from '@la-pasta-module/users';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto, ValidateNunOrderDto } from './dto/update-order.dto';
import { Permission } from '@la-pasta/infrastructures/security';
import { getUser } from '@la-pasta/common';
import { OrderAction, PaymentAction } from './actions';
import { config } from 'convict-config';
import { Order } from './entities/order.entity';
import { Document } from 'mongodb';

@ApiTags('Orders')
@Controller('orders')
export class OrderController {
  constructor(
    private readonly orderService: OrderService,
  ) {}

  @Post()
  @UseGuards(JwtGuard)
  create(@GetUser() user: BaseUser, @Body() createOrderDto: CreateOrderDto) {
    Permission.orderAuthorization(getUser(), OrderAction.CREATE);
    return this.orderService.createOrder(user, createOrderDto);
  }


  @Post('reparation-order')
  @UseGuards(JwtGuard)
  createBons(@Body() data) {    
    Permission.orderAuthorization(getUser(), OrderAction.VALIDATE);
    return this.orderService.transmitValidatedOrderToCadystLogistic(data);
  }


  @Post(':companyId')
  @UseGuards(JwtGuard)
  createOrderByCommercialForClient(@Param('companyId') companyId: string, @Body() createOrderDto: CreateOrderDto) {
    const user = getUser();
    Permission.orderAuthorization(getUser(), OrderAction.CREATE);
    return this.orderService.createOrderByCommercialForClient({ companyId }, createOrderDto, user);
  }

  @Post('afriland-otp')
  @UseGuards(JwtGuard)
  afrilandOtp(@Body() body: any) {
    Permission.paymentAuthorization(getUser(), PaymentAction.AFRILAND);

    this.orderService.sendAfrilandOTP(body);
  }

  @Get()
  @UseGuards(JwtGuard)
  findAll(@Query() query: QueryFilter) {
    Permission.orderAuthorization(getUser(), OrderAction.VIEW);

    return this.orderService.getAllOrders({ filter: query });
  }

  @Get('commercials')
  @UseGuards(JwtGuard)
  findAllCommercialOrders(@Query() query: QueryFilter) {
    Permission.orderAuthorization(getUser(), OrderAction.VIEW);

    // query.status ??= { "$in": [OrderStatus.PAID, OrderStatus.VALIDATED] }

    return this.orderService.getUserOrdersCommercials({ filter: query });
  }

  @Get('history')
  @UseGuards(JwtGuard)
  getUserOrders(@Query() query: QueryFilter) {
    return this.orderService.getUserOrders(getUser(), { filter: query });
  }

  @Get('recap-order-list')
  @UseGuards(JwtGuard)
  getRecapInfosForOrderList(@Query() query: QueryFilter) {
    return this.orderService.getRecapInfosForOrderList({ filter: query });
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.orderService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  findOne(@Param('id') id: string) {
    Permission.orderAuthorization(getUser(), OrderAction.VIEW);
    return this.orderService.findOne({ filter: { _id: id } });
  }

  @Patch(':id/save-num-order')
  @UseGuards(JwtGuard)
  saveNumOrder(@Param('id') id: string, @Body() data: ValidateNunOrderDto) {
    Permission.orderAuthorization(getUser(), OrderAction.VALIDATE);

    return this.orderService.saveNumOrderErp(id, { ...data });
  }

  @Patch(':id/validate')
  @UseGuards(JwtGuard)
  validate(@Param('id') id: string, @Body() data: UpdateOrderDto) {
    Permission.orderAuthorization(getUser(), OrderAction.VALIDATE);

    return this.orderService.validateOrder(id, data);
  }

  @Patch(':id/reject')
  @UseGuards(JwtGuard)
  async reject(@Param('id') id: string, @Body() data: UpdateOrderDto) {
    Permission.orderAuthorization(getUser(), OrderAction.VALIDATE);

    return await this.orderService.validateOrder(id, { ...data, rejected: true });
  }

  @Patch(':id/reject-order')
  @UseGuards(JwtGuard)
  async rejectOrder(@Param('id') id: string, @Body() data: any) {
    Permission.orderAuthorization(getUser(), OrderAction.DELETE);

    return await this.orderService.rejectOrder(id, data);
  }

  @Patch(':id/cancel-order')
  @UseGuards(JwtGuard)
  async cancelOrder(@Param('id') id: string, @Body() data: any) {
    Permission.orderAuthorization(getUser(), OrderAction.CANCEL);

    return await this.orderService.rejectOrder(id, data);
  }

  @Patch(':id/reject-cancel-demand')
  @UseGuards(JwtGuard)
  async cancelOrderDemandRefuse(@Param('id') id: string, @Body() data: UpdateOrderDto) {
    Permission.orderAuthorization(getUser(), OrderAction.CANCEL);

    return await this.orderService.updateOrderForCancelDemand(id, data);
  }


  @UseGuards(JwtGuard)
  @Patch(':id')
  udpate(@Param('id') id: string, @Body() body: UpdateOrderDto) {
    Permission.orderAuthorization(getUser(), OrderAction.UPDATE, { _id: id });

    return this.orderService.updateOrder(id, body);
  }

  @UseGuards(JwtGuard)
  @Patch(':id/add-carrier')
  update(@Param('id') id: string, @Body() body: UpdateOrderDto) {
    Permission.orderAuthorization(getUser(), OrderAction.UPDATE, { _id: id });

    const queryFilter: QueryFilter = { _id: id };
    return this.orderService.update(queryFilter, body);
  }
  
  @UseGuards(JwtGuard)
  @Post('verify-Wallet-Nber')
  verifyM2UWallet(@Body() body: Data) {
    // Permission.orderAuthorization(getUser(), OrderAction.UPDATE, { });

    return this.orderService.verifyM2UWalletNumber(body);
  }

  @UseGuards(JwtGuard)
  @Post('m2u-paymentRequest')
  paymentRequest(@Body() body: { data: Data, order: CreateOrderDto }, @GetUser() user: BaseUser) {
    Permission.orderAuthorization(getUser(), OrderAction.CREATE);
    return this.orderService.paymentRequest(body, user);
  }

  // Visa
  @UseGuards(JwtGuard)
  @Post('order-generate-visa-key')
  async orderGenerateVisaKey() {

    Permission.paymentAuthorization(getUser(), PaymentAction.VISA);

    return await this.orderService.orderGenerateVisaKey()
  }

  @UseGuards(JwtGuard)
  @Post('order-setup-payer-auth')
  async orderSetupPayerAuthentication(@Body() body: any) { //TODO: DO DTO FOR VALIDATION
    Permission.paymentAuthorization(getUser(), PaymentAction.VISA);
    return await this.orderService.setupCompletionWithFlexTransientToken(body)
  }

  @UseGuards(JwtGuard)
  @Post('order-authorization-pay-enroll')
  async orderAuthorizationPayEnroll(@GetUser() user: BaseUser, @Body() body: any) { //TODO: DO DTO FOR VALIDATION
    Permission.paymentAuthorization(getUser(), PaymentAction.VISA);
    Permission.orderAuthorization(getUser(), OrderAction.CREATE);

    return await this.orderService.AuthorizationWithPayEnrollAuthenticationNeeded(user, body)
  }

  @Post('cybersource-callback')
  async handleCyberSourceCallback(@GetUser() user: BaseUser, @Body() body: any, @Res() res: Response): Promise<void> {

    // this?.logger.warn (`cybersource-callback: ${JSON.stringify(body)}`);

    const { TransactionId, MD } = body;
    const data = await this.orderService.authorizationWithPayerAuthValidation({ transactionId: TransactionId, transientTokenId: MD, user });
    res.header('Content-Type', 'text/html');
    res.header('Content-Security-Policy', `frame-ancestors ${config.get('cyberSource.targetOrigins').join(' ')}`);
    res.status(200).send(data);
  }

  @Get(':id/exist')
  getOrderByTransientTokenId(@Param('id') id: string) {
    return this.orderService.findOne({ filter: { "payment.transientTokenId": id } });
  }

  @UseGuards(JwtGuard)
  @Get(':id/generate-purchase')
  async generatePurchaseOrder(@Param('id') id: any, @Res() res: any) {
    const user = getUser();
    Permission.orderAuthorization(getUser(), OrderAction.VIEW);

    const response = await this.orderService.generateExportPurchaseOrderLink(id, user);
    res.status(200).json({ ...response });
  }


  @Get(':code/generate-pdf')
  async generatePurchaseOrderPdf(@Param('code') code: any, @Res() res: any) {

    const { pdfBuffer, client } = await this.orderService.exportPurchaseOrder(code);
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': 'attachment; filename=file.pdf',
      'Content-Length': pdfBuffer?.length,
    });
    res.setHeader('Content-Disposition', `attachment; filename=Bon -${client} ${new Date().getDate()}.pdf`);

    return res.status(200).send(pdfBuffer)
  }

  @Patch(':id/comments')
  async addCommentToOrder(@Param('id') id: string, @Body() commentData: { userId: string, commentText: string }) {
    return this.orderService.addCommentToOrder(id, commentData.userId, commentData.commentText);
  }

  @Patch(':id/cancellation-order')
  @UseGuards(JwtGuard)
  async cancellationOrder( @Param('id') id: string, @Body() body: { messageCancellation: string }) {
    Permission.orderAuthorization(getUser(), OrderAction.UPDATE);

    return this.orderService.cancellationOrder(id, { message: body.messageCancellation });
  }
}
