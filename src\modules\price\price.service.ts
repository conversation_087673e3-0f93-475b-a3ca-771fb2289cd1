import { Injectable, NotFoundException, HttpException, InternalServerErrorException, HttpStatus } from '@nestjs/common';
import { ObjectId } from 'mongodb';
import { Packaging, PackagingsService, Product, ProductsService, Store, StoresService } from '@la-pasta-module/cart';
import { CompanyEmployee, BaseUser, User, UsersService, EmployeeEntity, Particular, Retailer, UserCategory } from '@la-pasta-module/users';
import { BaseService, convertParams, setResponse, t } from '@la-pasta/common';
import { CreatePriceDto } from './dto';
import { Price } from './entities';
import { PriceRepository } from './repository';
import { GlobalShipping, ShippingService } from './shipping';
import { CompaniesService } from '@la-pasta-module/companies';
import path from 'path';
import * as fs from 'fs';
import { config } from 'convict-config';

@Injectable()
export class PriceService extends BaseService {

  constructor(
    private userService: UsersService,
    private storeService: StoresService,
    private productService: ProductsService,
    private shippingService: ShippingService,
    private packagingService: PackagingsService,
    private companiesService: CompaniesService,
    private readonly priceRepository: PriceRepository,
  ) {
    super(priceRepository);
  }


  async createCategoryPriceOffer(newPriceOffer: CreatePriceDto | Price) {
    const offerExist = await this.checkIfCategoryPriceOfferExiste(newPriceOffer as CreatePriceDto);

    if (offerExist) throw new HttpException(t('error.PRICE_EXISTE'), 400);

    newPriceOffer.store = await this.getStore(newPriceOffer.store as string);
    newPriceOffer.product = await this.getProduct(newPriceOffer.product as string);
    newPriceOffer.packaging = await this.getPackaging(newPriceOffer.packaging as string);

    return await this.create(newPriceOffer);
  }

  async createPrice(newPrice: CreatePriceDto | Price) {
    if (!('userId' in newPrice) && !('companyId' in newPrice)) {
      return this.createCategoryPriceOffer(newPrice)
    }

    const priceOffer = await this.checkIfPriceOfferExiste(newPrice as CreatePriceDto) as unknown as Price;

    newPrice.store = await this.getStore(newPrice.store as string);
    newPrice.product = await this.getProduct(newPrice.product as string);
    newPrice.packaging = await this.getPackaging(newPrice.packaging as string);

    if (priceOffer instanceof NotFoundException) {
      if ('shippingPrice' in newPrice) {
        const { shippingPrice } = newPrice;
        const shipping = await this.getShipping(shippingPrice._id);
        delete newPrice.shippingPrice;
        newPrice = newPrice as unknown as Price;
        newPrice.shippingPrices = [{ ...shipping, amount: shippingPrice.amount }];
      }
      return await this.create(newPrice);
    }

    if (!newPrice.userId && !newPrice.companyId) throw new HttpException(t('error.PRICE_EXISTE'), 400);

    //TODO: verifier si une offre de prix en rendu existe déjà, si oui mettre à jour la donnée.
    if ('shippingPrice' in newPrice) {
      const { shippingPrice } = newPrice;
      const shipping = await this.getShipping(shippingPrice._id);
      priceOffer.shippingPrices ??= [];

      const shippingPriceOffer = priceOffer.shippingPrices.find(price =>
        (price.startRef?.storeRef === shipping?.startRef?.storeRef) && (price?.endRef === shipping?.endRef)
        && (price['companyId'] === newPrice?.companyId) && price?.enable);

      priceOffer.shippingPrices = priceOffer.shippingPrices.filter(price => (price?._id?.toString()) !== (shippingPriceOffer?._id?.toString()))

      priceOffer.shippingPrices.push({ ...shipping, amount: shippingPrice.amount })
      newPrice = priceOffer;
    }

    return await this.update({ _id: priceOffer._id }, newPrice);
  }

  // TODO: remove this method after execution of migrate script (surchage)
  async createPriceScriptMigration(newPrice: CreatePriceDto | Price) {
    if (!('userId' in newPrice) && !('companyId' in newPrice)) {
      const offerExist = await this.checkIfCategoryPriceOfferExiste(newPrice as CreatePriceDto);

      if (offerExist) return new Error(t('error.PRICE_EXISTE'));

      newPrice.store = await this.getStore(newPrice.store as string);
      newPrice.product = await this.getProduct(newPrice.product as string);
      newPrice.packaging = await this.getPackaging(newPrice.packaging as string);

      return await this.create(newPrice);
    }

    const priceOffer = await this.checkIfPriceOfferExiste(newPrice as CreatePriceDto) as unknown as Price;

    newPrice.store = await this.getStore(newPrice.store as string);
    newPrice.product = await this.getProduct(newPrice.product as string);
    newPrice.packaging = await this.getPackaging(newPrice.packaging as string);

    if (priceOffer instanceof NotFoundException) {
      if ('shippingPrice' in newPrice) {
        const { shippingPrice } = newPrice;
        const shipping = await this.getShipping(shippingPrice._id);
        delete newPrice.shippingPrice;
        newPrice = newPrice as unknown as Price;
        newPrice.shippingPrices = [{ ...shipping, amount: shippingPrice.amount }];
      }
      return await this.create(newPrice);
    }

    if (!newPrice?.userId && !newPrice?.companyId) {
      return new Error(t('error.PRICE_EXISTE'));
    }

    //TODO: verifier si une offre de prix en rendu existe déjà, si oui mettre à jour la donnée.
    if ('shippingPrice' in newPrice) {
      const { shippingPrice } = newPrice;
      const shipping = await this.getShipping(shippingPrice._id);
      priceOffer.shippingPrices ??= []
      priceOffer.shippingPrices.push({ ...shipping, amount: shippingPrice.amount })
      newPrice = priceOffer;
    }

    return await this.update({ _id: priceOffer._id }, newPrice);
  }

  async findStoresByCommercialSales(query: QueryFilter) {

    const user = await this.companiesService.getUserCompany(query, query["company._id"]);
    if (!user) {
      throw new HttpException(t("error.USER_NOT_FOUND"), HttpStatus.NOT_FOUND);
    }

    // Appeler getStores avec l'utilisateur et query
    return await this.getStores(user, query);
  }

  async getStores(user: User, query: QueryFilter) {
    try {
      return await this.priceRepository.findStoresWithPackaging(user, query);
    } catch (error) {
      console.error('Error in getStores:', error);
      throw new HttpException('Error fetching stores', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getStoreProducts(user: BaseUser | CompanyEmployee, storeId: string, packaging: string) {
    if ((!storeId && user?.category === UserCategory.Particular) || storeId.includes('{'))
      storeId = (await this.storeService.findOne({ filter: { storeRef: 'L10' } }))?._id.toString();

    return await this.priceRepository.findStoreProducts(user, storeId, packaging);
  }

  async getStoreProductsForchangeByCommercialSales(query: QueryFilter,) {

    const user = await this.companiesService.getUserCompany(query, query["company._id"]);
    if (!user) {
      throw new HttpException(t("error.USER_NOT_FOUND"), HttpStatus.NOT_FOUND);
    }

    // Appeler getStores avec l'utilisateur et query
    return await this.getStoreProducts(user, query?.store, query?.packaging);
  }

  async getStoreProductsForchange(userId: string, storeId: string, packaging: string) {
    const user = await this.userService.findOne({ filter: { _id: userId } }) as unknown as User;
    return await this.getStoreProducts(user, storeId, packaging);
  }

  async getCategoriesPrices(query: QueryOptions) {
    try {
      query = convertParams(query);
      return await this.priceRepository.findCategoriesPrices(query.filter.store, query.filter.packaging, query.filter.renderType, query.filter.commercialRegion);
    } catch (error) {
      return error;
    }
  }

  async getPrices(query: QueryOptions) {
    if ('store._id' in query.filter) query.filter['store._id'] = new ObjectId(query.filter['store._id'])
    if ('product._id' in query.filter) query.filter['product._id'] = new ObjectId(query.filter['product._id'])
    if ('packaging._id' in query.filter) query.filter['packaging._id'] = new ObjectId(query.filter['packaging._id']);
    if ('renderType' in query?.filter) query.filter['renderType'] = +query?.filter?.renderType;

    return await this.findAll(query);
  }

  async updateShippingPrice(id: string, shipping: { _id: string;[data: string]: any }) {
    try {
      const updateResult = await this.priceRepository.updateShippingPrice(id, shipping);

      if (updateResult.acknowledged == false) throw new InternalServerErrorException(t('error.ON_UPDATE'));

      return setResponse(200, await t('UPDATED'));
    } catch (error) {
      return error;
    }
  }

  private async checkIfPriceOfferExiste(priceOffer: CreatePriceDto) {
    try {
      const query = {
        "store._id": new ObjectId(priceOffer.store),
        "product._id": new ObjectId(priceOffer.product),
        "packaging._id": new ObjectId(priceOffer.packaging),
        // "commercialRegion": priceOffer.commercialRegion,
        "category": priceOffer.category,
        "renderType": priceOffer.renderType,
        "$or": [
          { "userId": `${priceOffer.userId ?? ''}` },
          { "companyId": `${priceOffer.companyId ?? ''}` }
        ]
      };

      return await this.findOne({ filter: query });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async checkIfCategoryPriceOfferExiste(priceOffer: CreatePriceDto) {
    const categorieOffer = await this.findOne({
      filter: {
        "store._id": new ObjectId(priceOffer.store),
        "product._id": new ObjectId(priceOffer.product),
        "packaging._id": new ObjectId(priceOffer.packaging),
        "commercialRegion": priceOffer.commercialRegion,
        "category": priceOffer.category,
        "userId": { $exists: false },
        "companyId": { $exists: false }
      }
    });

    if (categorieOffer instanceof NotFoundException) return false

    return true;
  }

  // disableOrderProcess(orderProcessState: boolean) {
  //   config.set('disableOrderProcess', orderProcessState);
  //   return setResponse(200, t('CREATED'), orderProcessState);
  // }

  disableOrderProcess(state: boolean) {
    const env = config.get('env'); // Get current environment
    const configPath = path.join(process.cwd(), 'src', 'env', `${env}.json`);

    if (!fs.existsSync(configPath)) {
      console.error(`Configuration file ${configPath} not found.`);
      return;
    }
    try {
      // Load the existing config file
      const currentConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'));

      currentConfig.isDisableOrderProcess = state;

      // Write back to the file
      fs.writeFileSync(configPath, JSON.stringify(currentConfig, null, 2), 'utf-8');

      console.log(`Configuration updated successfully in ${env}.json`);

      return { isDisableOrderProcess: currentConfig.isDisableOrderProcess };
    } catch (error) {
      console.error(`Error updating configuration: ${error.message}`);
    }
  }

  getOrderProcessState() {
    const env = config.get('env'); // Get current environment
    const configPath = path.join(process.cwd(), 'src', 'env', `${env}.json`);
    const currentConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
    return { isDisableOrderProcess: currentConfig?.isDisableOrderProcess };

  }

  private async getStore(storeId: string) {
    return await this.storeService.findOne({ filter: { _id: storeId }, projection: { enable: 0, created_at: 0 } }) as unknown as Partial<Store>;
  }

  private async getProduct(productId: string) {
    return await this.productService.findOne({ filter: { _id: productId }, projection: { label: 1, image: 1, normLabel: 1, erpRef: 1, category: 1 } }) as unknown as Partial<Product>;
  }

  private async getPackaging(packagingId: string) {
    return await this.packagingService.findPackage({ filter: { _id: packagingId }, projection: { enable: 0, created_at: 0 } }) as unknown as Partial<Packaging>;
  }

  private async getShipping(shippingId: string) {
    return await this.shippingService.findOne({ filter: { _id: shippingId } }) as unknown as GlobalShipping;
  }


}
