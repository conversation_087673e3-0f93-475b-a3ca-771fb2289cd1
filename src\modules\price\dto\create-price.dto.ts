import { <PERSON><PERSON><PERSON>, <PERSON>Int, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsString, ValidateIf } from "class-validator";
import { RenderType } from "@la-pasta-module/cart";
import { CompanyShipping, GlobalShipping, ParticularShipping } from "../shipping";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreatePriceDto {
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
  store: string;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
  category: number;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
  product: string;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
  packaging: string;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsEnum(RenderType)
  renderType: number;

  @ValidateIf(o => o.renderType == RenderType.PICKUP)
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  amount?: number;

  @IsOptional()
  userId?: string;

  @IsOptional()
  companyId?: string;

  @ValidateIf(o => o.renderType == RenderType.RENDU)
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  shippingPrice: CompanyShipping | ParticularShipping;

  @IsOptional()
  @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
  commercialRegion?: string;

  constructor(createPriceDto: Partial<CreatePriceDto>) {
    Object.assign(this, createPriceDto);
  }
}
