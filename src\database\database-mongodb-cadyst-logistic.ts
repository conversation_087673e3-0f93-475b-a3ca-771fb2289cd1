import { Lo<PERSON> } from "@nestjs/common";
import { Db, MongoClient, MongoClientOptions } from "mongodb";
import { config } from "../../convict-config";
import { DatabaseMongoDB } from "./database-mongodb-images";

export class MongoDatabaseCadystLogistic {

    private static instance: MongoDatabaseCadystLogistic;

    private options!: MongoClientOptions;
    private db!: Db;

    private constructor(private readonly logger?: Logger) { }

    public static getInstance(): MongoDatabaseCadystLogistic {
        return MongoDatabaseCadystLogistic.instance ??= new MongoDatabaseCadystLogistic();
    }

    async getDatabase(): Promise<Db> {
        return this.db ??= await this.setDatabase();
    }

    private async setDatabase(): Promise<Db> {
        if (config.get(`db_cadyst_logistic.auth.user`) && config.get('db_cadyst_logistic.auth.password')) {
            this.options.auth = {
                username: config.get('db_cadyst_logistic.auth.user'),
                password: config.get('db_cadyst_logistic.auth.password')
            }
        }

        try {
            const connection = await MongoClient.connect(this.getMongoDbURL(), this.options);
            return connection.db();
        } catch (error: unknown) {
            this.logger?.error(error);
        }
    }

    private getMongoDbURL(): string {
        return (config.get('db_cadyst_logistic.auth.user') && config.get('db_cadyst_logistic.auth.password'))
            ? `mongodb://${config.get('db_cadyst_logistic.auth.user')}:${config.get('db_cadyst_logistic.auth.password')}@${config.get('db_cadyst_logistic.host')}/${config.get('db_cadyst_logistic.name')}?retryWrites=true&w=majority`
            : `mongodb://${config.get('db_cadyst_logistic.host')}/${config.get('db_cadyst_logistic.name')}?retryWrites=true&w=majority`;
    }

 
}