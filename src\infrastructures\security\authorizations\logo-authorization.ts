import { LogoAction } from '@la-pasta-module/logo/actions';
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';

class LogoAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      LogoAction.CREATE,
      LogoAction.DELETE,
      LogoAction.UPDATE,
      LogoAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == LogoAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const LogoAuthorizationInstance = new LogoAuthorization();