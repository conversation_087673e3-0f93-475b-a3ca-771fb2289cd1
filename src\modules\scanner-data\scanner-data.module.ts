import { Modu<PERSON> } from '@nestjs/common';
import { config } from 'convict-config';
import { ScannerDataRepository } from './repository';
import { ScannerDataService } from './scanner-data.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ScannerDataController } from './scanner-data.controller';
import { LoyaltyProgramModule } from '@la-pasta-module/loyalty-program/loyalty-program.module';

@Module({
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } }
    ]),
    LoyaltyProgramModule
  ],
  controllers: [ScannerDataController],
  providers: [ScannerDataService,ScannerDataRepository]
})
export class ScannerDataModule {}
