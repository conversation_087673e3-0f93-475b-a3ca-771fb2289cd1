import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { TechnicalSheetService } from './technical-sheet.service';
import { CreateTechnicalSheetDto } from './dto/create-technical-sheet.dto';
import { TechnicalSheetAction } from './actions';
import { JwtGuard } from '@la-pasta-module/auth';
import { Permission } from '@la-pasta/infrastructures/security';
import { getUser } from '@la-pasta/common';

@Controller('technical-sheet')
export class TechnicalSheetController {
  constructor(private readonly technicalSheetService: TechnicalSheetService) { }

  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createTechnicalSheetDto: CreateTechnicalSheetDto) {
    Permission.technicalAuthorization(getUser(), TechnicalSheetAction.CREATE);
    return this.technicalSheetService.createTechnical(createTechnicalSheetDto);
  }


  @UseGuards(JwtGuard)
  @Get()
  findAll(@Query() query: QueryFilter) {
    Permission.technicalAuthorization(getUser(), TechnicalSheetAction.VIEW);
    return this.technicalSheetService.findAll({ filter: query });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.technicalSheetService.findOne({ filter: { _id: id } });
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateTechnicalSheetDto: any) {
    Permission.technicalAuthorization(getUser(), TechnicalSheetAction.UPDATE);
    return this.technicalSheetService.updateTechnical(updateTechnicalSheetDto);
  }


}
