import { AmqpConnectionManager, ChannelWrapper, connect, Options } from 'amqp-connection-manager';
import { I18nContext } from 'nestjs-i18n/dist/i18n.context';
import { getI18nContextFromRequest } from "nestjs-i18n";
import { RequestContext } from 'nestjs-request-context';
import { User } from '@la-pasta-module/users';
import { HttpException, HttpStatus } from "@nestjs/common";
import { config } from 'convict-config';
import { readFileSync } from 'fs';
import fetch from 'node-fetch';
import path, { join } from 'path';
import moment from 'moment';
import { OrderStatus } from '@la-pasta-module/order/entities/order.entity';
import handlebars from 'handlebars';
import * as XLSX from 'xlsx';
const URLCrypt = require('url-crypt')(config.get('exportSalt'));


const isProdOrStag = ['production', 'staging'].includes(config.get('env'));

const { protocol, hostname, port, username, password } = config.get('rabbitmq');
// const urlRabbitmq = `${protocol}://${username}:${password}@${hostname}:${port}`;
const urlRabbitmq: Options.Connect | string = isProdOrStag
  ? { protocol, hostname, port, username, password }
  : `${protocol}://${hostname}:${port}`;

const rabbitmqTLSopts = isProdOrStag ? {
  connectionOptions: {
    cert: readFileSync(path.resolve(__dirname, `ssl/${config.get('env')}/client_certificate.crt`)),
    key: readFileSync(path.resolve(__dirname, `ssl/${config.get('env')}/client_key.crt`)),
    ca: [readFileSync(path.resolve(__dirname, `ssl/${config.get('env')}/ca-bundle.crt`))],
  }
} : {};

export const extractPaginationData = (query: QueryOptions): QueryOptions => {
  setParam('offset', query);
  setParam('limit', query);

  return query
}

export const extractSortingData = (query: QueryOptions): QueryOptions => {
  setParam('sort', query);
  setParam('way', query);

  return query
}

export const extractProjectionData = (query: QueryOptions): QueryOptions => {
  setParam('projection', query);

  if (typeof query.projection == 'string')
    query.projection = setProjection(query.projection as unknown as string);

  return query;
}

function setParam(param: FilterParam, query: QueryOptions) {
  if (query?.filter && `${param}` in query?.filter) {
    query[`${param}` as string] = query.filter[param];
    delete query.filter[param];
  }
}

function setProjection(field: string) {
  return field.split(',').reduce((o, key) => ({ ...o, [key]: 1 }), {});
}

export const setResponse = (status: number, message: string, data?: any): QueryResult => {
  return (data) ? { status, message, data } : { status, message };
}

export const setResponseController = (data?: any): QueryResult => {
  if (data?.error || data instanceof Error) {
    throw new HttpException(data?.message || data?.name, data?.statusCode || data?.status || 500);
  }
  return data;
}

export const convertParams = (query: QueryOptions): QueryOptions => {
  if (query.filter) {
    for (const key in query.filter) {
      if ('except' in query.filter && query.filter.except.includes(key)) { continue; }
      if (['true'].includes(query.filter[key])) { query.filter[key] = true; }
      if (query.filter[key] === 'false') { query.filter[key] = false; }
      if (typeof query?.filter[key] == 'string' && query?.filter[key].includes('$') 
        && (query?.filter[key].includes('{') || query?.filter[key].includes('[')))
        query.filter[key] = JSON.parse(`${query.filter[key]}`);
      if (RegExp(/[a-z]/i).test(query.filter[key])) { continue; }
      query.filter[key] = !isNaN(query.filter[key]) ? +query.filter[key] : query.filter[key];
    }

    delete query.filter.except;
  }

  if (query.limit) { query.limit = +query.limit; }
  if (query.offset) { query.offset = +query.offset; }

  return query;
}

export function convertFilter(fields: QueryFilter, keyToDineds = []): QueryFilter {
  const { filter } = convertParams({ filter: fields });

  const year = parseInt(filter.startDate?.split('-')[0]);
  const month = parseInt(filter.startDate?.split("-")[1]);
  const startDay = parseInt(filter.startDate?.split("-")[2]);
  const endDay = parseInt(filter.endDate?.split("-")[2]);

  if (month == 2 && ((0 == year % 4) && (0 != year % 100) || (0 == year % 400)) && (startDay > 29 || endDay > 29)) {
    throw new HttpException(`The Query date format is invalid; The number of days is incorrect based on the month.`, HttpStatus.BAD_REQUEST);
  }

  if (moment(filter.startDate).valueOf() > moment(filter?.endDate).valueOf()) {
    throw new HttpException(`The start date should not be greater than end date.`, HttpStatus.BAD_REQUEST);
  }

  filter['created_at'] = {
    $gte: moment((filter?.startDate || new Date()), 'YYYY-MM-DD').startOf(filter?.startDate ? 'day' : 'year').valueOf(),
    $lte: moment(filter?.endDate || new Date(), 'YYYY-MM-DD').endOf(filter?.startDate ? 'day' : 'year').valueOf()
  }


  delete filter?.startDate;
  delete filter?.endDate;
  filter.status ??= { "$in": [OrderStatus.VALIDATED] }

  keyToDineds.forEach(key => delete filter[key]);

  return filter;
}

export function converDateFilter(fields: QueryFilter): QueryFilter {
  const { filter } = convertParams({ filter: fields });
  filter['created_at'] = {
    $gte: moment((filter?.startDate || new Date()), 'YYYY-MM-DD').startOf(filter?.startDate ? 'day' : 'year').valueOf(),
    $lte: moment(filter?.endDate || new Date(), 'YYYY-MM-DD').endOf(filter?.startDate ? 'day' : 'year').valueOf()
  }
  delete filter.filter;
  return filter;
}

export const setDateFilter = (query: QueryOptions): QueryOptions => {
  query.filter['created_at'] = {
    $gte: formatStartDate(query.filter['startDate']),
    $lte: formatEndDate(query.filter['endDate']),
  };

  delete query.filter['startDate'];
  delete query.filter['endDate'];

  return query;
}

function formatStartDate(date: string) {
  return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
}

export function generateRandomString(length: number) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
}

function formatEndDate(date: string) {
  return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
}

export const t = (key: string) => {
  const i18n: I18nContext = getI18nContextFromRequest(RequestContext.currentContext.req);
  return i18n.t('lang.' + key);
}

export const getUser = () => {
  return RequestContext.currentContext.req.user as User;
}

export const subscriberMigrateData = async () => {
  try {
    console.log('getting data in queue...');

    const queue = 'seesaw_migration';
    const conn: AmqpConnectionManager = connect(urlRabbitmq, rabbitmqTLSopts);

    const channel: ChannelWrapper = conn.createChannel();
    await channel.assertQueue(queue, { durable: true });


    // Listener
    await channel.consume(queue, async (msg: any) => {
      if (msg !== null) {
        const data = JSON.parse(msg.content.toString());
        const response = await fetch(`http://${config.get('host')}:${config.get('port')}/${config.get('basePath')}/scripts-migration/seesaw-data`,
          {
            method: 'post',
            body: JSON.stringify(data),
            headers: { 'Content-Type': 'application/json' }
          });
        const body = await response.text();
        if (!body) return;

        const res = JSON.parse((body));
        if (res?.status === 400) return;

        return await channel.ack(msg);
      } else {
        console.log('Consumer cancelled by server');
      }
    });
  } catch (error) {
    console.error('Error in subscriber event', error);
  }
}

export function capitalizeFirstLetter(string: string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export async function getTemplateBody(fileName: string, data: any) {
  const filePath = join(__dirname, `./${fileName}.template.html`);
  const templateContent = readFileSync(filePath, 'utf8');

  // const html = fs.readFileSync(path.join(__dirname, 'templates', fileName + '.template.html'), { encoding: 'utf8', flag: 'r' });

  // const bodyTemplate = readFileSync('../templates/purchase-order.template.html', { encoding: 'utf8', flag: 'r' });
  const template = handlebars.compile(templateContent);
  return template(data);
}

export async function encodePdfCode(option: any) {
  return await URLCrypt.cryptObj({ format: 'pdf', ...option });
}

export async function decodePdfCode(code: any) {
  try {
    const options = URLCrypt.decryptObj(code);
    return options
  } catch (error) {
    return error;
  }
}

export function getDataInExcelFile(sheetName: string) {
  const filePath = join(__dirname, './file-data-la-pasta.xlsx');

  const workbook = XLSX.readFile(filePath);
  const sheetIndex = workbook.SheetNames.findIndex(name => name === sheetName);

  if (sheetIndex === -1) {
    throw new Error(`La feuille "${sheetName}" n'a pas été trouvée dans le fichier Excel.`);
  }

  const worksheet = workbook.Sheets[workbook.SheetNames[sheetIndex]];

  return XLSX.utils.sheet_to_json(worksheet);
}

export function splitArray(arrays: ObjectType<any>[], key: string): string {
  return arrays?.map(array => array[key])?.join(',');
}




