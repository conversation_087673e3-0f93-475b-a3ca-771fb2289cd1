import { Controller, Get, Post, Body, Param, Delete, Query, Patch, UseGuards } from '@nestjs/common';
import { PromoCodeService } from './promo-code.service';
import { JwtGuard } from '@la-pasta-module/auth';
import { setResponseController } from '@la-pasta/common';
import { CreatePromoCodeDto, UpdatePromoCodeDto } from './dto';
import { Cart } from '@la-pasta-module/price/compute_price';

@Controller('promo-codes')
export class PromoCodeController {
  constructor(private readonly promoCodeService: PromoCodeService) { }

  @Post()
  @UseGuards(JwtGuard)
  create(@Body() createPromoCodeDto: CreatePromoCodeDto) {
    const data = this.promoCodeService.insertPromoCode(createPromoCodeDto);
    return setResponseController(data);
  }

  @Post('apply/:reference')
  @UseGuards(JwtGuard)
  applyPromoCode(@Param('reference') reference: string, @Body() cart: Partial<Cart>) {
    const res = this.promoCodeService.applyPromoCode(reference, cart);
    return setResponseController(res);
  }

  @Get()
  @UseGuards(JwtGuard)
  findAll(@Query() query: QueryFilter) {
    const data = this.promoCodeService.findAll({ filter: query });
    return setResponseController(data);
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.promoCodeService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  findOne(@Param('id') id: string) {
    const data = this.promoCodeService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  update(@Param('id') id: string, @Body() updatePromoCodeDto: UpdatePromoCodeDto) {
    const res = this.promoCodeService.updatePromoCode(id, updatePromoCodeDto);
    return setResponseController(res);
  }
}
