import { MongoDatabase } from '@la-pasta/database';
import { RenderType } from '@la-pasta-module/cart';
import { Price } from '@la-pasta-module/price';
import { UserCategory } from '@la-pasta-module/users';
import { getCompaniesCategories } from 'scripts/company';
import { getPackagings } from 'scripts/packaging';
import { getProducts } from 'scripts/products';
import { getStores } from 'scripts/stores';
import { getUsesrCategories } from 'scripts/users';
import { faker } from '@faker-js/faker';
import { dropCollection, stopLoader, setLoader } from 'scripts/common';
import moment from 'moment';

const database = MongoDatabase.getInstance();
const collectionName = 'prices';

const priceOffers: Omit<Price, '_id'>[] = [];

export async function insertPriceOffers() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des offres de prix\n');

    const stores = await getStores();
    const userCategories = getUsesrCategories();
    const companyCategories = getCompaniesCategories();
    const products = await getProducts();
    const packagings = await getPackagings();

    for await (const store of stores) {
      for await (const product of products) {
        for await (const packaging of packagings) {
          for await (const userCategory of userCategories) {
            if (userCategory == UserCategory.Retailer || userCategory == UserCategory.CompanyUser) continue;

            priceOffers.push({
              store,
              product,
              packaging,
              category: userCategory,
              renderType: RenderType.PICKUP,
              amount: Number(faker.finance.amount(10500, 15500, 0)),
            });
          }

          for await (const companyCategory of companyCategories) {
            priceOffers.push({
              store,
              product,
              packaging,
              category: companyCategory,
              renderType: RenderType.PICKUP,
              amount: Number(faker.finance.amount(10500, 15500, 0)),
            });
          }
        }
      }
    }

    priceOffers.forEach(priceOffer => { priceOffer.enable = true; priceOffer.create_at = moment().valueOf() });
    const insertedPriceOffers = await (await database.getDatabase()).collection(collectionName).insertMany(priceOffers);
    stopLoader(insertedPriceOffers);
  } catch (error) {
    console.error(error);
  }
}