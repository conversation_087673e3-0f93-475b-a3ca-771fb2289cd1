import { Option } from '@la-pasta-module/options/entities';
import { Injectable } from '@nestjs/common';
import { RenderType } from '@la-pasta-module/cart';
import { CompanyEmployee, BaseUser, UserCategory } from '@la-pasta-module/users';
import { CreateComputePriceDto } from './dto';
import { Cart, CartItem, OrderPrice } from './entities';
import { OptionsService } from '@la-pasta-module/options/options.service';
import { ObjectId } from 'bson';
import { LoyaltyProgramService } from '@la-pasta-module/loyalty-program/loyalty-program.service';

@Injectable()
export class ComputePriceService {

  VAT = 0.1925;
  bagDefaultValue = 50;
  taxByBag = 70;
  user: CompanyEmployee;

  constructor(
    private optionSrv: OptionsService,
    private loyaltySrv: LoyaltyProgramService,
  ) { }

  async compute(user: BaseUser | CompanyEmployee, orderCart: CreateComputePriceDto) {
    this.user = user;

    // Company pickup or delivered in company address
    if (orderCart.renderType == RenderType.PICKUP || 'erpShipToId' in orderCart.shipping) return await this.computePickupPrice(orderCart);
    // if (orderCart.renderType == RenderType.PICKUP) return this.computePickupPrice(orderCart);

    // Delivered for particular and employee && Delivered for company in another address
    return this.computeShippingPrice(orderCart);
  }

  async computePickupPrice(orderCart: CreateComputePriceDto): Promise<Cart> {
    const cartAmount = this.getTotalAmount(orderCart.items);
    const VAT = this.getTotalTaxe(orderCart.items);
    const amountHT = cartAmount - VAT;
    const totalQuantity = this.getTotalQuantityOrder(orderCart?.items);
    const totalNumberOfBags = this.getTotalBags(totalQuantity);

    const discount = await this.getDisctountOrder(orderCart, totalNumberOfBags);

    orderCart.amount = {
      HT: this.roundUp(amountHT),
      VAT: VAT,
      shipping: 0,
      TTC: 0,
      cartAmount,
      shippingInfo: {
        totalNumberOfBags
      },
      discount,
    };

    orderCart.amount.VAT = this.adjustVAT(orderCart.amount, cartAmount);
    orderCart.amount.TTC = this.setTotal(orderCart.amount);

    return orderCart as Cart
  }

  async getDisctountOrder(orderCart: CreateComputePriceDto, totalBags: number): Promise<Discount> {
    let discount: Discount = {};

    if (this.user.category === UserCategory.CompanyUser && this.user?.company?.options) {
      const query = this.user?.company?.options.map(option => new ObjectId(option));
      const options = (((await this.optionSrv.findAll({ filter: { _id: { $in: query }, type: 'discount' } }))?.data) || []) as Option[];

      for (const option of options) {
        const value = eval(option?.formula);
        if (!value) continue;

        discount[`${option?.name}`] = value;
      }
    }

    if (orderCart?.optionsDiscount?.points) {
      const entity = await this.loyaltySrv.getEntity(this.user);
      if (entity?.points?.validate <= 0) return discount;
      discount['points'] = entity?.points?.validate ?? 0;
    }

    return discount;
  }


  async computeShippingPrice(orderCart: CreateComputePriceDto): Promise<Cart> {
    // const totalNumberOfBags = orderCart.items.reduce((total, cartElement) => total += cartElement.quantity, 0);

    const totalQuantity = this.getTotalQuantityOrder(orderCart?.items);
    const totalNumberOfBags = this.getTotalBags(totalQuantity);

    orderCart = await this.computePickupPrice(orderCart);
    orderCart.amount.shipping = totalNumberOfBags * orderCart.shipping.amount;
    orderCart.amount.shippingInfo = {
      totalNumberOfBags,
      amount: orderCart?.shipping?.amount,
      totalShippingPrice: orderCart?.amount?.shipping
    }
    // orderCart.amount.VAT += this.roundUp(orderCart.amount.shipping * this.VAT); //TODO: verify why do this
    orderCart.amount.TTC = this.setTotal(orderCart.amount);
    return orderCart as Cart;
  }

  private getTotalTaxe(cartItem: CartItem[]): number {
    const totalQuantity = this.getTotalQuantityOrder(cartItem);
    return this.getTotalBags(totalQuantity) * this.taxByBag;
    // const taxeAmount = 1 + this.VAT;
    // return ('company' in this.user) ? taxeAmount + (this.user.company.precompteRate / 100) : taxeAmount;
  }

  private getPrecompte(amount: number): number {
    return ('company' in this.user) ? amount * (this.user.company.precompteRate / 100) : 0;
  }

  private adjustVAT(amount: OrderPrice, targetAmount: number): number {
    const difference = (amount.HT + amount?.VAT) - targetAmount;
    return (difference != 0) ? amount?.VAT - difference : amount?.VAT;
  }

  private roundUp(value: number, precision = 0): number {
    return +(Math.round(Number(value + `e+${precision}`)) + `e-${precision}`);
  }

  private setTotal(amount: OrderPrice): number {
    // return amount?.HT + amount?.VAT + amount?.shipping - amount?.discountCash - amount?.deliveryDiscount;
    return amount?.HT + amount?.VAT + amount?.shipping - this.calculateTotalDiscount(amount?.discount);
  }

  private getTotalAmount(items: CartItem[]): number {
    return items.reduce((total, cartElement) => total += cartElement.quantity * cartElement.unitPrice, 0);
  }

  private getTotalQuantityOrder(items: CartItem[]): number {
    return items.reduce((total, cartElement) => total += (cartElement?.quantity * cartElement.packaging?.unit?.value), 0)
  }

  private getTotalBags(totalQuantiy: number) {
    return (totalQuantiy / this.bagDefaultValue);
  }

  private calculateTotalDiscount(discount: { [key: string]: number }): number {
    return Object.values(discount).reduce((total, value) => total + value, 0);
  }

  private getTotalBagsInOptions(items: CartItem[], associatedProductsId: string[]): number {
    const matchedItems = items?.filter(item => associatedProductsId?.some(id => item?.product?._id === id));
    const totalQuantity = this.getTotalQuantityOrder(matchedItems);
    const totalNumberOfBags = this.getTotalBags(totalQuantity);
    return totalNumberOfBags;
  }
}
