import { Test, TestingModule } from '@nestjs/testing';
import { SnitchsController } from './snitchs.controller';
import { SnitchsService } from './snitchs.service';

describe('SnitchsController', () => {
  let controller: SnitchsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SnitchsController],
      providers: [SnitchsService],
    }).compile();

    controller = module.get<SnitchsController>(SnitchsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
