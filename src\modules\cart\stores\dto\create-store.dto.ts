import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateStoreDto {
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    storeRef: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    label: string;

    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    @IsEmail({}, { message: i18nValidationMessage('validation.INVALID_EMAIL') })
    @IsOptional()
    email?: string;
    
    @IsOptional()
    address: Address;
}
