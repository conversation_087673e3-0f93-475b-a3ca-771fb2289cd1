import { getUser } from '@la-pasta/common';
import { OrderSupplierAction } from './actions';
import { BaseUser } from '@la-pasta-module/users';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { OrderSupplierService } from './order-supplier.service';
import { Permission } from '@la-pasta/infrastructures/security';
import { CreateOrderSupplierDto } from './dto/create-order-supplier.dto';
import { UpdateOrderSupplierDto } from './dto/update-order-supplier.dto';
import { Controller, Get, Post, Body, Patch, Param, UseGuards, Query } from '@nestjs/common';
import { CartItem } from '@la-pasta-module/price/compute_price';
import { query } from 'express';
import { filter } from 'lodash';
import { OrderSupplier } from './entities/order-supplier.entity';

@Controller('order-supplier')
export class OrderSupplierController {
  constructor(private readonly orderSupplierService: OrderSupplierService) { }

  @Post()
  @UseGuards(JwtGuard)
  async createOderSupplier(@GetUser() user: BaseUser, @Body() createOrderSupplierDto: CreateOrderSupplierDto) {
    Permission.orderSupplierAuthorization(user, OrderSupplierAction.CREATE);
    return await this.orderSupplierService.createOrder(user, createOrderSupplierDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  async findAllOderSupplier(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    // Permission.orderSupplierAuthorization(user, OrderSupplierAction.VIEW);
    return await this.orderSupplierService.findAll({ filter: query });
  }

  @Get('particulars')
  @UseGuards(JwtGuard)
  getOrderParticular(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    // Permission.oderSupplierAuthorization(user, OderSupplierAction.VIEW);
    return this.orderSupplierService.orderSupplierParticular({ filter: query }, user);
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  async findOneOderSupplier(@Param('id') id: string, @GetUser() user: BaseUser) {
    // Permission.oderSupplierAuthorization(user, OderSupplierAction.VIEW);
    return await this.orderSupplierService.findOne({ filter: { _id: id } });
  }

  @Get('volume-order-by-particular-client')
  @UseGuards(JwtGuard)
  async getVolumeOrderByParticularClient(@Query() query: QueryFilter) {
    return await this.orderSupplierService.getVolumeOrderByParticularClient({ filter: query });
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async updateOderSupplier(@Param('id') id: string, @Body() body) {
    // Permission.orderSupplierAuthorization(getUser(), OrderSupplierAction.UPDATE);
    return await this.orderSupplierService.updateOrderSupplier(id, body);
  }

  @Patch(':id/rejectOder')
  @UseGuards(JwtGuard)
  async updateReject(@Param('id') id: string, @Body() body) {
    Permission.orderSupplierAuthorization(getUser(), OrderSupplierAction.UPDATE);
    return await this.orderSupplierService.RejectOrder(id, body, getUser());
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.orderSupplierService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }


}
