import { Packaging, Product, RenderType, Store } from "@la-pasta-module/cart";
import { Shipping } from "@la-pasta-module/price/shipping";
import { QrCodeData } from "@la-pasta-module/qr-code-management/entities/qr-code.entity";

export class Cart {
    store: Partial<Store>;
    items: CartItem[];
    renderType: RenderType;
    shipping?: Shipping;
    amount: OrderPrice;
}

export declare type CartItem = {
    quantity: number;
    unitPrice: number;
    product: Partial<Product>;
    packaging: Partial<Packaging>;
    quantityShipped?: number,
    code?: string;
}

export declare type OrderPrice = {
    HT: number,
    subTotal?: number, //TODO: add value in this feild
    precompte?: number,
    VAT: number,
    shipping: number,
    TTC: number,
    cartAmount: number;
    shippingInfo?: ShippingInfo
    discount?: Discount;
}

export declare type ShippingInfo = {
    totalNumberOfBags?: number,
    amount?: number,
    totalShippingPrice?: number
}