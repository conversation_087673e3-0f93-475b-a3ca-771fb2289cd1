import { JwtGuard } from '@la-pasta-module/auth';
import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { getUser, setResponseController } from '@la-pasta/common';
import { CreateShippingDto, UpdateShippingDto } from './dto';
import { ShippingService } from './shipping.service';
import { Permission } from '@la-pasta/infrastructures/security';
import { ShippingAction } from './actions';

@ApiTags('Shippings')
@Controller('shippings')
export class ShippingController {
  constructor(private readonly shippingService: ShippingService) { }

  @UseGuards(JwtGuard)
  @Post()
  async create(@Body() createShippingDto: CreateShippingDto) {
    Permission.shippingAuthorization(getUser(), ShippingAction.CREATE);
    const data = await this.shippingService.createShipping(createShippingDto);
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.shippingAuthorization(getUser(), ShippingAction.VIEW);
    const data = await this.shippingService.getShippings(query);
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get('default-shipping')
  async findAllDefaultShippingsForClientSide(@Query() query: QueryFilter) {
    Permission.shippingAuthorization(getUser(), ShippingAction.VIEW);
    const data = await this.shippingService.getDefaultShippingsForClientSide(query);
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get('categories')
  async findAllWithCategory(@Query() query: QueryFilter) {
    Permission.shippingAuthorization(getUser(), ShippingAction.VIEW);
    const data = await this.shippingService.getCategoryShippings(query);
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.shippingAuthorization(getUser(), ShippingAction.VIEW);
    const data = await this.shippingService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateShippingDto: UpdateShippingDto) {
    Permission.shippingAuthorization(getUser(), ShippingAction.UPDATE);
    const data = await this.shippingService.updateShipping(id, updateShippingDto);
    return setResponseController(data);
  }
}
