import { Module, forwardRef } from '@nestjs/common';
import { LoyaltyProgramService } from './loyalty-program.service';
import { LoyaltyProgramController } from './loyalty-program.controller';
import { LoyaltyProgramRepository } from './repository/loyalty-program.repository';
import { AdvantageRepository } from './repository';
import { CompaniesModule } from '@la-pasta-module/companies';
import { UsersModule } from '@la-pasta-module/users';
import { AdvantagesModule } from '@la-pasta-module/advantages/advantages.module';
import { InvitationModule } from '@la-pasta-module/invitation/invitation.module';
import { OrderSupplierModule } from '@la-pasta-module/order-supplier/order-supplier.module';
import { OrderSupplierService } from '@la-pasta-module/order-supplier/order-supplier.service';
import { OrderSupplierRepository } from '@la-pasta-module/order-supplier/repository';
import { QrCodeModule } from '@la-pasta-module/qr-code-management/qr-code.module';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';


@Module({
  controllers: [LoyaltyProgramController],
  providers: [LoyaltyProgramService, LoyaltyProgramRepository, AdvantageRepository],
  exports: [LoyaltyProgramService, LoyaltyProgramRepository],
  imports: [
     ClientsModule.register([
          { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
        ]),
    forwardRef(() => OrderSupplierModule),
    forwardRef(() => CompaniesModule),
    forwardRef(() => UsersModule),
    forwardRef(() => AdvantagesModule),
    forwardRef(() => InvitationModule),
    forwardRef(() => QrCodeModule)
  ],
})
export class LoyaltyProgramModule { }
