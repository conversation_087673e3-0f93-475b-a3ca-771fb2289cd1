import { Test, TestingModule } from '@nestjs/testing';
import { QrCodeFileManagementService } from './qr-code-file-management.service';

describe('QrCodeFileManagementService', () => {
  let service: QrCodeFileManagementService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [QrCodeFileManagementService],
    }).compile();

    service = module.get<QrCodeFileManagementService>(QrCodeFileManagementService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
