import { BaseService, setResponse, t } from '@la-pasta/common';
import { HttpStatus, Injectable } from '@nestjs/common';
import { InternalServerErrorException } from '@nestjs/common/exceptions/internal-server-error.exception';
import moment from 'moment';
import { ImageValidateOrder } from './entities/image.entity';
import { ImageRepository } from './repository/images.repository';

@Injectable()
export class ImagesService extends BaseService {

    constructor(
        private readonly imageRepository: ImageRepository
    ) {
        super()
    }

    async createImage(image: ImageValidateOrder) {
        image.dateCreated = moment().valueOf();

        const { appRef } = image;
        const document = await this.imageRepository.findOne({ filter: { appRef: appRef } });
        if (document) {
            const res = await this.updateDataUrls(appRef, image);

            return res;

        }

        const imageCreated = await this.imageRepository.create(image);
        return setResponse(
            HttpStatus.CREATED,
            t('IMAGE_CREATED'),
            imageCreated.insertedId);
    }

    async getImage(query: QueryOptions) {
        if ('data.loadNumber' in query.filter) {
            const data = Number(query.filter['data.loadNumber']);
            query.filter = {
                'data.loadNumber': data
            }
        }
        return await this.imageRepository.findOne(query);
    }



    async updateDataUrls(appRef: string, updateImage: any) {
        const updatedImage = await this.imageRepository.update(appRef, updateImage);
        if (!updatedImage.acknowledged) throw new InternalServerErrorException(t('error.ON_UPDATE'));
        return setResponse(200, t('IMAGE_UPDATED'));
    }
}
