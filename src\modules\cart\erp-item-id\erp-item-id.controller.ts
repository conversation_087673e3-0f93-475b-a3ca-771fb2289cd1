import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ErpItemIdService } from './erp-item-id.service';
import { CreateErpItemIdDto } from './dto/create-erp-item-id.dto';
import { UpdateErpItemIdDto } from './dto/update-erp-item-id.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '@la-pasta-module/auth';
import { Permission } from '@la-pasta/infrastructures/security';
import { ErpItemIdAction } from './actions';
import { getUser } from '@la-pasta/common';

@ApiTags('ErpItemId')
@Controller('erp-item-id')
export class ErpItemIdController {
  constructor(private readonly erpItemIdService: ErpItemIdService) { }

  @HttpCode(201)
  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createErpItemIdDto: CreateErpItemIdDto) {
    Permission.erpItemIdAuthorization(getUser(), ErpItemIdAction.CREATE);
    return this.erpItemIdService.create(createErpItemIdDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  findAll(@Query() query: QueryFilter) {
    Permission.erpItemIdAuthorization(getUser(), ErpItemIdAction.VIEW);
    return this.erpItemIdService.findAll({ filter: query });
  }

  @UseGuards(JwtGuard)
  @Get('filters-elements')
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.erpItemIdService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  // @UseGuards(JwtGuard)
  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.erpItemIdService.findOne(+id);
  // }

  @UseGuards(JwtGuard)
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateErpItemIdDto: UpdateErpItemIdDto,
  ) {
    Permission.erpItemIdAuthorization(getUser(), ErpItemIdAction.UPDATE);
    return this.erpItemIdService.updateErpItemId(id, updateErpItemIdDto);
  }

  // @UseGuards(JwtGuard)
  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.erpItemIdService.remove(+id);
  // }
}
