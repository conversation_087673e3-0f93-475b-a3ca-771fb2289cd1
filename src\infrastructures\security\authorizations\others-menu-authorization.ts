import { User } from "@la-pasta-module/users";
import { AuthorizationInterface } from "./authorization.interface";
import { MenuOthersAction } from "@la-pasta-module/jde/actions/menuOthers.action";


export class OthersMenuAuthorization implements AuthorizationInterface {

    support(user: User, permission: string): boolean {
        return ([
            MenuOthersAction.ACCOUNT_EXTRACT,
            MenuOthersAction.EDIT_BILLS,
            MenuOthersAction.LOAD_DISPATCHS] as string[])
            .includes(permission) && user.authorizations.includes(permission);
    }

    authorise(user: User, permission: string, subject: any): boolean {
        if (permission == MenuOthersAction.ACCOUNT_EXTRACT) return ('company' in user);
        if (permission == MenuOthersAction.EDIT_BILLS) return ('company' in user);
        if (permission == MenuOthersAction.LOAD_DISPATCHS) return ('company' in user);

        return user.enable === true;
    }
}

export const OthersMenuAuthorizationInstance = new OthersMenuAuthorization();