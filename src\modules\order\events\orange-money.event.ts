import { User } from '@la-pasta-module/users';
import { Order, Payment } from '../entities';

export class OrangeMoneyEvent {
  order: { _id: string; appReference: string; user: User };
  paymentInfo: Payment;
  amount: number;

  constructor(order: Order) {
    this.order = {
      _id: order._id,
      appReference: order.appReference,
      user: order.user,
    };
    this.paymentInfo = order.payment;
    this.amount = order.cart.amount.TTC;
  }
}
