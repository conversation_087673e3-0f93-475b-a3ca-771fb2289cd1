import { lastValueFrom } from 'rxjs';
import { BaseService, setResponse, t } from '@la-pasta/common';
import { Injectable, HttpStatus, NotFoundException, Inject, forwardRef, HttpException } from '@nestjs/common';
import { Profile, ProfileService } from '@la-pasta-module/authorizations';
import { CompanyEmployee, Particular, User, UsersService } from '@la-pasta-module/users';
import { CreateCompanyDto, CreateCompanyUserDto, UpdateCompanyDto } from './dto';
import { Company, CompanyCategory } from './entities';
import { CompanyRepository } from './repository';
import { ClientProxy } from '@nestjs/microservices';
import { keyEventJDE } from '@la-pasta-module/order/events/jde-event';
import { LogoService } from '@la-pasta-module/logo/logo.service';
import { config } from 'convict-config';
import { BalanceService } from '@la-pasta-module/balance/balance.service';
import { Logo } from '@la-pasta-module/logo/entities/logo.entity';
import { ObjectId } from 'bson';
import { UserEntity } from '@la-pasta-module/loyalty-program/entities';

@Injectable()
export class CompaniesService extends BaseService {

  constructor(
    private logoService: LogoService,
    private readonly companyRepository: CompanyRepository,
    @Inject(forwardRef(() => UsersService))
    private readonly userService: UsersService,
    private readonly profileService: ProfileService,
    @Inject('JDE') private readonly jdeService: ClientProxy,
    private balanceSrv: BalanceService

  ) {
    super(companyRepository);
  }

  async createCompany(createCompany: CreateCompanyDto) {

    const { users, ...companyData } = createCompany;

    //check if this companyData ailready exit before creating 
    await this.verifyDataCompanyExist(companyData);

    const insertionResult = await this.create(companyData);
    const usersIds = [];
    let res;
    if (insertionResult?.status == HttpStatus.OK && users?.length) {
      users?.forEach(async user => {
        !user?._id
          ? (
            await this.assignCompanyDataToUser(user, insertionResult?.data?.toString(), createCompany),
            res = await this.userService.createUser({ ...user })
          )
          : usersIds?.push(user?._id?.toString());
      });

      if (usersIds?.length > 0)
        await this.update({ _id: insertionResult?.data?.toString() }, { users: usersIds })

      return setResponse(HttpStatus.CREATED, t('SUCCESS_OPERATION'))
    }
  }

  async verifyDataCompanyExist(company: Partial<Company>): Promise<any> {
    const existingCompany = await this.findOne({
      filter: {
        $or: [
          { erpSoldToId: company?.erpSoldToId },
          { name: company.name, category: company.category }]
      }
    });
    if (existingCompany && !(existingCompany instanceof NotFoundException)) {
      throw new HttpException(t('error.COMPANY_ALREADY_EXISTS'), HttpStatus.CONFLICT, );
    }
  }

  async assignCompanyDataToUser(user: CompanyEmployee, companyId: string, companyData: CreateCompanyDto) {
    user.company = {
      _id: companyId,
      name: companyData.name,
      category: companyData.category,
    };
    user.authorizations = await this.getProfileAuthorizations(companyData.category);
  }

  async findUsers(companyId: string, query: QueryFilter) {

    if (!['undefined', null, undefined, 'null', '', "", NaN, '', 'NaN'].includes(companyId)) {
      const company = await this.findOne({ filter: { _id: new ObjectId(companyId) } });

      const userIds = company?.users || [];

      query['$or'] = [{ "company._id": companyId }, { _id: { $in: userIds?.map(objId => new ObjectId(objId?._id)) } }]
    }


    return await this.userService.findAll({ filter: { ...query }, projection: { password: 0 } });

  }


  async getUsersCompanies(user: Particular | User, query: QueryOptions) {

    const suppliersIds = user['associatedSuppliers']?.map(objId => new ObjectId(objId?._id)) || [];
    const commericalId = user['associatedCommercial']?._id?.toString();

    const particularCommercial = await this.userService.findOne({ filter: { _id: commericalId }, projection: {} });
    const commercialIds = particularCommercial?.associatedCompanies?.map(objId => new ObjectId(objId?._id)) || [];

    query.filter['$or'] = [
      { _id: { $in: suppliersIds } },
      { _id: { $in: commercialIds } },
      { "associatedCommercial": { _id: commericalId ?? '' } },
      { "address.commercialRegion": user?.address?.commercialRegion ?? '' }
    ]

    return await this.findAll(query);
  }

  async addUser(id: string, userData: CreateCompanyUserDto | CompanyEmployee) {
    if (!userData?.['_id']) {
      userData.authorizations = await this.getProfileAuthorizations(userData?.company?.category);
      return await this.userService.createUser(userData);
    }

    const company = await this.findOne({ filter: { _id: new ObjectId(id) } });
    if (!company || company instanceof NotFoundException) {
      throw new HttpException(t("error.USER_NOT_FOUND"), HttpStatus.NOT_FOUND);
    }
    // Initialize `users` array if it doesn't exist, and add user if not already present
    company.users = company.users || [];
    if (!company.users.includes(userData?.['_id']?.toString())) {
      company.users.push(userData?.['_id']?.toString());
    }

    return await this.update({ _id: id }, { users: company?.users });
  }

  async getBalanceCompany(query: QueryFilter) {
    // if('_id' in query) query['_id'] = new ObjectId(query['_id']);
    const company = await this.findOne({ filter: query }) as unknown as Company;

    if (!company?.erpSoldToId) throw new NotFoundException(t('error.NO_COMPANY'));

    if (config.get('activeMock')) return {
      "availableBalance": "955652",
      "invoicedDelayed": "445558",
      "invoiceInProgress": "68461",
      "creditLimit": "5684616",
      "openOrderAmount": "55565",
      "date": "2024-06-14T13:24:16.694Z",
      "enable": true,
      "created_at": 1718371471099
    }
    return await this.balanceSrv.findOne({ filter: { 'company.erpSoldToId': company.erpSoldToId } })

    // return await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.GET_BALANCE }, { ...company }));
  }

  async getUserCompany(query: QueryFilter, companyId: string): Promise<User> {
    const user = await this.userService.findOne({
      filter: { "company._id": companyId, ...query?.filter }, projection: {
        password: 0,
        roles: 0,
        authorizations: 0,
      }
    });
    if (!user || user instanceof NotFoundException)
      throw new HttpException(t("error.USER_NOT_FOUND"), HttpStatus.NOT_FOUND);

    delete query?.filter
    const company = await this.findOne({ filter: { _id: companyId }, projection: { category: 1, name: 1, associatedCommercial: 1, address: 1 } });
    if (!company || company instanceof NotFoundException || !company?._id)
      throw new HttpException(t("error.USER_NOT_FOUND"), HttpStatus.NOT_FOUND);

    return { ...user, company: company as unknown as Company } as unknown as User
  }
  private async getProfileAuthorizations(category: number) {
    const profile = await this.profileService.findOne({ filter: { label: CompanyCategory[category].toLowerCase() } }) as unknown as Profile;

    if (profile instanceof Error) throw new NotFoundException(t('error.NO_CATEGORY_AUTHORIZATION'))

    return profile.authorizations;
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async getDataLoyaltyProgram(filter: QueryFilter): Promise<UserEntity[]> {
    const aggregate = [
      {
        $match: {
          ...filter
        },
      },
      {
        $lookup:
        {
          from: "rewards",
          localField: "rewardId",
          foreignField: "_id",
          as: "advantages"
        }
      }
    ]
    return await this.findAllAggregate(aggregate) as UserEntity[];
  }

  async updateCompanyInUser(companyId: string, UpdateCompanyDto: UpdateCompanyDto) {
    const filterQuery = {
      $or: [
        { "company._id": companyId }, // Match when company is a single object
        { "associatedCompanies": { $elemMatch: { _id: companyId } } } // Match when company is an array
      ]
    };
    const companyUsers = (await this.userService.findAll({ filter: filterQuery }))?.data as CompanyEmployee[];

    const { users } = UpdateCompanyDto;


    // const res = await this.update({ _id: companyId }, companyData);

    const usersIds = [];

    if (users?.length) {
      users?.forEach(async user => {
        !user?._id
          ? (
            user.company = {
              _id: companyId.toString(),
              name: UpdateCompanyDto?.name,
              category: UpdateCompanyDto?.category
            },
            user.authorizations = await this.getProfileAuthorizations(UpdateCompanyDto?.category),
            await this.userService.createUser({ ...user })
          )
          : usersIds?.push(user?._id?.toString());
      });
    }

    UpdateCompanyDto.users = usersIds;

    const res = await this.update({ _id: companyId }, UpdateCompanyDto)


    await Promise.all(companyUsers?.map(async (user: any) => {
      if (user?.company && user?.company?._id) {
        const updatedUserData = { ...user };
        Object.keys(UpdateCompanyDto).forEach(key => {

          if (updatedUserData.company.hasOwnProperty(key)) {
            updatedUserData.company[key] = UpdateCompanyDto[key];
          }
        });
        await this.userService.update({ _id: user?._id }, updatedUserData);
      }
      if (user?.associatedCompanies && user?.associatedCompanies?.length > 0) {
        const updatedAssociatedCompanies = user?.associatedCompanies.map((associatedCompany: { [x: string]: any; _id: string; hasOwnProperty: (arg0: string) => any; }) => {
          if (associatedCompany?._id === companyId) {
            Object.keys(UpdateCompanyDto).forEach(key => {

              if (associatedCompany?.hasOwnProperty(key)) {
                associatedCompany[key] = UpdateCompanyDto[key];
              }
            });
          }
          return associatedCompany;
        });
        await this.userService.update({ _id: user?._id }, { associatedCompanies: updatedAssociatedCompanies });
      }
    }));
    return res;
  }

  async saveLogoCompany(id: string, logo: Partial<Logo>) {
    const company = await this.findOne({ filter: { _id: id }, projection: { name: 1, erpSoldToId: 1 } }) as unknown as Company;
    return await this.logoService.createLogo({ ...logo, company });
  }
}
