import { Test, TestingModule } from '@nestjs/testing';
import { WholeSaleController } from './whole-sale.controller';
import { WholeSaleService } from './whole-sale.service';

describe('WholeSaleController', () => {
  let controller: WholeSaleController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WholeSaleController],
      providers: [WholeSaleService],
    }).compile();

    controller = module.get<WholeSaleController>(WholeSaleController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
