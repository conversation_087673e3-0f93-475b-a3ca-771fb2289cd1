import { Test, TestingModule } from '@nestjs/testing';
import { RenderTypesController } from './render-types.controller';
import { RenderTypesService } from './render-types.service';

describe('RenderTypesController', () => {
  let controller: RenderTypesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RenderTypesController],
      providers: [RenderTypesService],
    }).compile();

    controller = module.get<RenderTypesController>(RenderTypesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
