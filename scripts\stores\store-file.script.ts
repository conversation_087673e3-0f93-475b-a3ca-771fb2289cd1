import { MongoDatabase } from '@la-pasta/database';
import { Store } from '@la-pasta-module/cart';
import { setLoader, stopLoader } from 'scripts/common';
import { getDataInExcelFile } from '@la-pasta/common';
import moment from 'moment';

const database = MongoDatabase.getInstance();
const collectionName = 'stores';

export async function insertStoreDataFile() {
    try {
        setLoader('Chargement des stores depuis le fichier Excel\n');
        const storesCollection = (await database.getDatabase()).collection(collectionName)
        const datas = getDataInExcelFile('la-pasta.stores') as any[];

        let stores: Store[] = [];
        for (const data of datas) {
            const store: Store = {
                label: data?.label,
                email: data?.email,
                storeRef: data?.storeRef,
                address: {
                    agency: data?.address_agency,
                    region: data?.address_region,
                    commercialRegion: data?.address_commercialRegion,
                    city: data?.address_city,
                    district: data?.address_district,
                },
                enable: true,
                create_at: moment().valueOf()
            };

            const query = {
                storeRef: data?.storeRef,
            }

            const res = await storesCollection.updateOne(query, { $set: { ...store } }, { upsert: true });
            if (res?.matchedCount == 0 && !res?.upsertedCount)
                stores.push(store);
        }

        if (stores?.length)
            await storesCollection.insertMany(stores as unknown[]);

        stopLoader(true);
    } catch (error) {
        console.error('Erreur lors de l\'insertion des stores :', error);
    }
}