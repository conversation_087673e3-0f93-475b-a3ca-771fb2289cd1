import { Company } from "@la-pasta-module/companies";
import { OrderStatus } from "@la-pasta-module/order";
import { Cart } from "@la-pasta-module/price/compute_price";
import { Particular, User } from "@la-pasta-module/users";

export class ScannerDatum {

  
    _id: string;
    supplier: Partial<Company>;
    cart: Cart;
    user: Particular;
    status: OrderStatus;
    appReference: string;
    validation?: {
      user?: Partial<User>;
      date: number;
      raison: string;
    }
    created_at: number
}