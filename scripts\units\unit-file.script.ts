import { MongoDatabase } from '@la-pasta/database';
import { Unit } from '@la-pasta-module/cart';
import { setLoader, stopLoader } from 'scripts/common';
import { getDataInExcelFile } from '@la-pasta/common';
import moment from 'moment';

const database = MongoDatabase.getInstance();
const collectionName = 'units';


export async function insertUnitsDataFile() {
  try {
    setLoader('Chargement des unités depuis le fichier Excel\n');
    const unitsCollection = (await database.getDatabase()).collection(collectionName)
    const datas = getDataInExcelFile('la-pasta.units') as Unit[];

    let units: Unit[] = [];

    for (const data of datas) {
      if (!data?.value) continue;

      const unit: Unit = {
        label: data?.label,
        ratioToTone: data?.ratioToTone,
        symbol: data?.symbol,
        value: data?.value,
        enable: true,
        create_at: moment().valueOf(),
      }

      const query = {
        label: data?.label,
        ratioToTone: data?.ratioToTone,
        symbol: data?.symbol,
        value: data?.value,
      }
      const res = await unitsCollection.updateOne(query, { $set: { ...unit } }, { upsert: true });
      if (res?.matchedCount == 0 && !res?.upsertedCount)
        units.push(unit);
    }

    if (units.length) await (await database.getDatabase()).collection(collectionName).insertMany(units as unknown[]);

    stopLoader(true);
    console.log('unités insérées');

  } catch (error) {
    console.error('Erreur lors de l\'insertion des unités :', error);
  }
}