import { Company } from "@la-pasta-module/companies";
import { UserExists } from "@la-pasta-module/users";
import { ApiProperty } from "@nestjs/swagger";
import { IsBase64, IsNotEmpty, IsNumber, IsString, ValidateIf } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateLogoDto {
  @ValidateIf(o => o?.company)
  @ApiProperty({ type: String })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  company: Partial<Company>

  @ValidateIf(o => o?.user)
  @ApiProperty({ type: String })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  user: Partial<Company>

  @ApiProperty({ type: String })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsString()
  value: string;

  @ApiProperty({ type: String })
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsNumber()
  logoType: number;

}
