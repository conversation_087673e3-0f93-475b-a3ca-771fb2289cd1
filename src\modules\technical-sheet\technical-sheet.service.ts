import { Injectable } from '@nestjs/common';
import { CreateTechnicalSheetDto } from './dto/create-technical-sheet.dto';
import { TechnicalSheetRepository } from './repository';
import { FirebaseCloud } from '@la-pasta/infrastructures/image';
import { BaseService } from '@la-pasta/common';

@Injectable()
export class TechnicalSheetService extends BaseService {


  constructor(
    private readonly technicalRepository: TechnicalSheetRepository,
    private firebaseCloud: FirebaseCloud
  ) {
    super(technicalRepository);
  }


  async createTechnical(createTechnicalSheetDto: CreateTechnicalSheetDto) {
    // createTechnicalSheetDto.product.image = await this.firebaseCloud.getImageUrl(createTechnicalSheetDto.product.image, createTechnicalSheetDto.product.erpRef);
    createTechnicalSheetDto.imgPdfUrl = await this.firebaseCloud.getImageUrl(createTechnicalSheetDto.imgPdfUrl, createTechnicalSheetDto.product.erpRef);
    return this.create(createTechnicalSheetDto);
  }


  async updateTechnical(updateTechnicalSheetDto: any) {
    const firebaseStorageUrl = 'https://firebasestorage.googleapis.com/v0/b/monchantier-4b302.appspot.com/o/';
    const imageUrl = updateTechnicalSheetDto.product.image;
    const imgPdfUrl = updateTechnicalSheetDto.imgPdfUrl;

    if (imageUrl && imageUrl.startsWith(firebaseStorageUrl)) {
      updateTechnicalSheetDto.product.image = imageUrl;
    } else {
      updateTechnicalSheetDto.product.image = await this.firebaseCloud.getImageUrl(imageUrl, updateTechnicalSheetDto.product.erpRef);
    }

    if (imgPdfUrl && imgPdfUrl.startsWith(firebaseStorageUrl)) {
      updateTechnicalSheetDto.imgPdfUrl = imgPdfUrl;
    } else {
      updateTechnicalSheetDto.imgPdfUrl = await this.firebaseCloud.getImageUrl(imgPdfUrl, updateTechnicalSheetDto.product.erpRef);
    }

    return this.update({ _id: updateTechnicalSheetDto?._id }, updateTechnicalSheetDto);
  }

}
