import { Module } from '@nestjs/common';
import { PromoCodeService } from './promo-code.service';
import { PromoCodeController } from './promo-code.controller';
import { PromoCodeRepository } from './repository/promo-code.repository';
import { PromoCodeValidation } from './validations';

@Module({
  controllers: [PromoCodeController],
  providers: [PromoCodeService, PromoCodeRepository, PromoCodeValidation],
  exports: [PromoCodeService, PromoCodeRepository]
})
export class PromoCodeModule { }
