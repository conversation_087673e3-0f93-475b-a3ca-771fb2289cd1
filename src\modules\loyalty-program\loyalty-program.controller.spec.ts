import { Test, TestingModule } from '@nestjs/testing';
import { LoyaltyProgramController } from './loyalty-program.controller';
import { LoyaltyProgramService } from './loyalty-program.service';

describe('LoyaltyProgramController', () => {
  let controller: LoyaltyProgramController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LoyaltyProgramController],
      providers: [LoyaltyProgramService],
    }).compile();

    controller = module.get<LoyaltyProgramController>(LoyaltyProgramController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
