import { Test, TestingModule } from '@nestjs/testing';
import { AuthorizationRemovalService } from './authorization-removal.service';

describe('AuthorizationRemovalService', () => {
  let service: AuthorizationRemovalService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AuthorizationRemovalService],
    }).compile();

    service = module.get<AuthorizationRemovalService>(AuthorizationRemovalService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
