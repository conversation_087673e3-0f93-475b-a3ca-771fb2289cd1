import { MongoDatabase } from "@la-pasta/database";
import { Document, InsertManyResult } from "mongodb";
import ora from 'ora-classic';
import { Console } from 'console';
import { Transform } from 'stream';

const database = MongoDatabase.getInstance();
let loader: ora.Ora;

export async function dropCollection(collectionName: string) {
  if ((await verifyIfCollectionExist(collectionName)) == 1) {
    setLoader(`Suppression de la collection ${collectionName}`)
    const result = await (await database.getDatabase()).dropCollection(collectionName);
    stopLoader(result);
  }
}

export async function verifyIfCollectionExist(collectionName: string) {
  const collection = (await database.getDatabase()).listCollections({ name: collectionName }, { nameOnly: true }).toArray();
  return (await collection).length
}

export function table(input: { [key: string]: any }[]) {
  // @see https://stackoverflow.com/a/67859384
  const ts = new Transform({ transform(chunk, enc, cb) { cb(null, chunk) } })
  const logger = new Console({ stdout: ts })
  logger.table(input)
  const table = (ts.read() || '').toString()
  let result = '';
  for (const row of table.split(/[\r\n]+/)) {
    let r = row.replace(/[^┬]*┬/, '┌');
    r = r.replace(/^├─*┼/, '├');
    r = r.replace(/│[^│]*/, '');
    r = r.replace(/^└─*┴/, '└');
    r = r.replace(/'/g, ' ');
    result += `${r}\n`;
  }
  console.log(result);
}

export function setLoader(text?: string) {
  loader = ora(text).start();
}

export const stopLoader = (result: InsertManyResult<Document> | boolean) => {
  if (typeof result == 'boolean' && result === true) loader?.succeed()
  if (typeof result != 'boolean' && 'insertedCount' in result && result.insertedCount > 0) loader?.succeed();
}