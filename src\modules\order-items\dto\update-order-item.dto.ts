import { PartialType } from '@nestjs/swagger';
import { CreateOrderItemDto } from './create-order-item.dto';
import { IsNumber, IsOptional, IsString} from "class-validator";
import { CancellationStatus, CommercialComment, OrderValidation } from "@la-pasta-module/order/entities";
import { User } from "@la-pasta-module/users/entities";


export class UpdateOrderItemDto extends PartialType(CreateOrderItemDto) {

    @IsOptional()
    points:Point

    @IsOptional()
    user:User

    @IsOptional()
    @IsNumber()
    created_at: number;

    @IsOptional()
    @IsString()
    rejectReason: string;

    @IsOptional()
    @IsNumber()
    nberModif: number;

    @IsOptional()
    @IsString()
    messageCancellation?: string;

    @IsOptional()
    cancellationStatus?: CancellationStatus;

    @IsOptional()
    commercialComment?: CommercialComment;

    @IsOptional()
    @IsString()
    reference?: string;

    @IsOptional()
    comments?: CommercialComment[];

    @IsOptional()
    actionType?: string;

    @IsOptional()
    reason?:string;

    @IsOptional()
    isMobile?: boolean;
}
