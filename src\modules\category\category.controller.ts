import { Controller, Get, Post, Body, Patch, Param, UseGuards, Query } from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { JwtGuard } from '@la-pasta-module/auth';
import { ApiTags } from '@nestjs/swagger';
import { Permission } from '@la-pasta/infrastructures/security';
import { getUser, setResponseController } from '@la-pasta/common';
import { CategoryAction } from './actions';

@ApiTags('Category')
@Controller('category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) { }

  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createCategoryDto: CreateCategoryDto) {
    Permission.categoryAuthorization(getUser(), CategoryAction.CREATE);

    return this.categoryService.createCategory(createCategoryDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    // Permission.categoryAuthorization(getUser(), CategoryAction.VIEW);

    const data = await this.categoryService.getAllCategories({ filter: query });
    return setResponseController(data);
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.categoryService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.categoryAuthorization(getUser(), CategoryAction.VIEW);

    const data = await this.categoryService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateCategoryDto: UpdateCategoryDto) {
    Permission.categoryAuthorization(getUser(), CategoryAction.UPDATE);

    return this.categoryService.updateCategory({ _id: id }, updateCategoryDto);
  }

}
