import { IsInt, <PERSON>NotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString, ValidateIf } from "class-validator";
import { CompanyCategory } from "@la-pasta-module/companies";
import { UserCategory } from "@la-pasta-module/users";
import { Store } from "@la-pasta-module/cart";


export class CreateShippingDto {
    @IsNotEmpty()
    startRef: Partial<Store>;

    @IsString()
    @IsNotEmpty()
    label: string;

    @ValidateIf(o => o.erpShipToId == undefined && o.erpShipToDesc == undefined)
    @IsNumber()
    @IsNotEmpty()
    amount: number;

    @IsOptional()
    category: UserCategory | CompanyCategory;

    @ValidateIf(o => o.erpShipToId != undefined || o.erpShipToDesc != undefined)
    @IsNotEmpty()
    @IsString()
    companyId: string;

    @ValidateIf(o => o.erpShipToDesc != undefined)
    @IsNotEmpty()
    @IsInt()
    erpShipToId: number;

    @ValidateIf(o => o.erpShipToId != undefined)
    @IsNotEmpty()
    @IsString()
    erpShipToDesc: string;

    @IsOptional()
    endRef?: string;

    @IsOptional()
    address?: Address;
}
