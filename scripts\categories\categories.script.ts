import { Category } from '@la-pasta-module/category/entities';
import { MongoDatabase } from '@la-pasta/database/database-mongodb';
import moment from 'moment';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';

const database = MongoDatabase.getInstance();
const collectionCategory = 'categories';

export async function insertCategories() {
    try {
        setLoader('Insertion des categories dans les produits\n');

        const db = await database.getDatabase();

        await dropCollection(collectionCategory);

        const categories = [
            { code: 'FA-00', label: 'Farine', description: 'Produit de type Farine', enable: true, created_at: moment().valueOf() },
            { code: 'BL-00', label: 'Blé', description: 'Produit de type Blé', enable: true, created_at: moment().valueOf() }
        ];
        const insertedCategories = await (await db.collection(collectionCategory)).insertMany(categories);

        stopLoader(insertedCategories);
    } catch (error) {
        console.error(error);
    }

}

export async function getCategories() {
    try {
        return await (await database.getDatabase()).collection(collectionCategory).find().toArray() as unknown as Category[];
    } catch (error) {
        console.error(error);
    }
}

export async function getCategory(label: string): Promise<Partial<Category>> {
    try {
        const db = await database.getDatabase();

        const category = await db.collection(collectionCategory).findOne({ label: label }) as unknown as Category;

        delete category.description;
        delete category.created_at;

        return category
    } catch (error) {
        console.log(error.message);
    }
}

