import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { BaseUser, User } from '@la-pasta-module/users';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { PriceService } from './price.service';
import { CreatePriceDto } from './dto/create-price.dto';
import { UpdatePriceDto } from './dto/update-price.dto';
import { Permission } from '@la-pasta/infrastructures/security';
import { PriceAction } from './actions';
import { getUser } from '@la-pasta/common';
import { OrderAction } from '@la-pasta-module/order';

@ApiTags('Prices')
@Controller('prices')
export class PriceController {
  constructor(private readonly priceService: PriceService) { }

  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createPriceDto: CreatePriceDto) {
    Permission.priceAuthorization(getUser(), PriceAction.CREATE);

    return this.priceService.createPrice(createPriceDto);
  }

  @UseGuards(JwtGuard)
  @Post('disable-order-process')
  disableOrderProcess(@Body() data: { isDisableOrderProcess: boolean }) {
    Permission.priceAuthorization(getUser(), PriceAction.UPDATE);
    return this.priceService.disableOrderProcess(data?.isDisableOrderProcess)
  }

  @Get()
  @UseGuards(JwtGuard)
  findAll(@Query() query: QueryFilter) {
    Permission.priceAuthorization(getUser(), PriceAction.VIEW);

    return this.priceService.getPrices({ filter: query });
  }

  @Get('categories')
  @UseGuards(JwtGuard)
  findCategoriesPrices(@Query() query: QueryFilter) {
    Permission.priceAuthorization(getUser(), PriceAction.VIEW);

    return this.priceService.getCategoriesPrices({ filter: query })
  }

  @Get('stores')
  @UseGuards(JwtGuard)
  findStores(@Query() query: QueryFilter) {
    Permission.orderAuthorization(getUser(), OrderAction.INIT);

    return this.priceService.getStores(getUser(), query);
  }

  @Get('stores-sales')
  @UseGuards(JwtGuard)
  findStoresByCommercialSales(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    Permission.orderAuthorization(user, OrderAction.INIT);

    return this.priceService.findStoresByCommercialSales(query);
  }

  @Get('products-sales')
  @UseGuards(JwtGuard)
  findStoreProductsBySales(@GetUser() user: User, @Query() query: QueryFilter) {
    Permission.orderAuthorization(user, OrderAction.INIT);

    return this.priceService.getStoreProductsForchangeByCommercialSales(query);
  }
  @Get('products')
  @UseGuards(JwtGuard)
  findStoreProducts(@GetUser() user: User, @Query('store') store: string, @Query('packaging') packaging: string) {
    Permission.orderAuthorization(getUser(), OrderAction.INIT);

    return this.priceService.getStoreProducts(user, store, packaging);
  }

  @Get('products-change')
  @UseGuards(JwtGuard)
  findStoreProductsForChange(@GetUser() user: User, @Query('store') store: string,
    @Query('packaging') packaging: string, @Query('user') userId: string) {
    // Permission.orderAuthorization(getUser(), OrderAction.EDIT);

    return this.priceService.getStoreProductsForchange(userId, store, packaging);
  }

  @UseGuards(JwtGuard)
  @Get('order-process-disable-state')
  getOrderProceesDisableStatus() {
    return this.priceService.getOrderProcessState();
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  findOne(@Param('id') id: string) {
    Permission.priceAuthorization(getUser(), PriceAction.VIEW);

    return this.priceService.findOne({ filter: { _id: id } });
  }

  @Patch(':id/shipping')
  @UseGuards(JwtGuard)
  updateShippingPirce(@Param('id') id: string, @Body() shipping: { _id: string;[data: string]: any }) {
    Permission.priceAuthorization(getUser(), PriceAction.UPDATE);

    return this.priceService.updateShippingPrice(id, shipping);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  update(@Param('id') id: string, @Body() updatePriceDto: UpdatePriceDto) {
    Permission.priceAuthorization(getUser(), PriceAction.UPDATE);

    return this.priceService.update({ _id: id }, updatePriceDto);
  }

}
