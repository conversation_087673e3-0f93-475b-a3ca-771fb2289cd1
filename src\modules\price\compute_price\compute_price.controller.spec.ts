import { Test, TestingModule } from '@nestjs/testing';
import { ComputePriceController } from './compute_price.controller';
import { ComputePriceService } from './compute_price.service';

describe('ComputePriceController', () => {
  let controller: ComputePriceController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ComputePriceController],
      providers: [ComputePriceService],
    }).compile();

    controller = module.get<ComputePriceController>(ComputePriceController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
