import { Module } from '@nestjs/common';
import { RenderTypesService } from './render-types.service';
import { RenderTypesController } from './render-types.controller';
import { RenderTypeRepository } from './repository';

@Module({
  controllers: [RenderTypesController],
  providers: [RenderTypesService, RenderTypeRepository],
  exports: [RenderTypesService, RenderTypeRepository]
})
export class RenderTypesModule {}
