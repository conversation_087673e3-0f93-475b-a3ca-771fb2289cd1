import { PartialType } from '@nestjs/mapped-types';
import { BaseUser } from '../entities';
import { IsOptional } from 'class-validator';

export class UpdateUserDto extends PartialType(BaseUser) {

    // @IsOptional()
    // enable: boolean;
    // @IsOptional()
    // isRetired: boolean;

    // @IsOptional()
    // Direction: string;

    // @IsOptional()
    // service: string;

    // @IsOptional()
    // position: string;

    // @IsOptional()
    // matricule: string;

    // @IsOptional()
    // capacityTonnage: number;

    // @IsOptional()
    // capacityTonnageYear: number;

    // @IsOptional()
    // socialReason: string;

    @IsOptional()
    points: Point;

    // @IsOptional()
    // profession: string;

    // @IsOptional()
    // company: any;
}
