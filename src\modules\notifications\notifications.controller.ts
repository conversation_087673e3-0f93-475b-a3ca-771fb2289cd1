import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, BadRequestException } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { BaseUser } from '@la-pasta-module/users';

@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) { }

  @Post()
  @UseGuards(JwtGuard)
  async create(@GetUser() user: BaseUser, @Body() createNotificationDto: CreateNotificationDto) {
    return await this.notificationsService.createNotification(createNotificationDto, user);
  }

  @Get()
  findAll(@Query() query: QueryFilter) {
    return this.notificationsService.findAllnNotifications({ filter: query });
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  update(@Param('id') id: string, @Body() updateNotificationDto: UpdateNotificationDto, @GetUser() user: BaseUser) {
    return this.notificationsService.updateNotification(id, updateNotificationDto, user);
  }
  @Post('users')
  @UseGuards(JwtGuard)
  updateUsers(@Body() data: any[]) {
    return this.notificationsService.sendNotificationToCustomers(data);
  }
  @Patch(':id/delete')
  @UseGuards(JwtGuard)
  async deleteNotification( @Param('id') id:string,  @GetUser() user: BaseUser,  @Body() data:{ ids: string[] }) {

    const ids = data?.ids;

    if (!ids || ids.length === 0) {
      throw new BadRequestException('The array of IDs is empty or not provided.');
    }
  
    if (ids.length === 1) {
      return await this.notificationsService.deleteNotification(ids[0], user);
    }
  
    return await this.notificationsService.deleteMultipleNotifications(ids, user); 
   }

}
 