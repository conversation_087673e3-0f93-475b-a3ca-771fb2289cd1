import { Test, TestingModule } from '@nestjs/testing';
import { OrderSupplierController } from './order-supplier.controller';
import { OrderSupplierService } from './order-supplier.service';

describe('OrderSupplierController', () => {
  let controller: OrderSupplierController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrderSupplierController],
      providers: [OrderSupplierService],
    }).compile();

    controller = module.get<OrderSupplierController>(OrderSupplierController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
