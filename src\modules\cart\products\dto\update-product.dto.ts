import { PartialType } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsOptional, IsString } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';
import { CreateProductDto } from './create-product.dto';

export class UpdateProductDto extends PartialType(CreateProductDto) {
  @IsOptional()
  _id: string;

  @IsOptional()
  @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
  image: string;

  @IsOptional()
  @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
  enable: boolean;

  @IsOptional()
  @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
  created_at:number;

  @IsOptional()
  @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
  updated_at: number;
}
