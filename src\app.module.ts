import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import { CommonModule } from './common';
import { UsersModule } from './modules/users/users.module';
import { CompaniesModule } from './modules/companies/companies.module';
import { AuthModule } from './modules/auth/auth.module';
import { winstonLoggerConfig } from './infrastructures/logger';
import { AuthorizationsModule } from './modules/authorizations/authorizations.module';
import { PackagingsModule } from './modules/cart/packagings/packagings.module';
import { RenderTypesModule } from './modules/cart/render-types/render-types.module';
import { StoresModule } from './modules/cart/stores/stores.module';
import { ProductsModule } from './modules/cart/products/products.module';
import { UnitModule } from './modules/cart/unit/unit.module';
import { PriceModule } from './modules/price/price.module';
import { OrderModule } from './modules/order/order.module';
import { CallbackModule } from './modules/callback/callback.module';
import { RequestContextModule } from 'nestjs-request-context';
import { AcceptLanguageResolver, I18nModule } from 'nestjs-i18n';
import path from 'path';
import { PlanificationModule } from '@la-pasta-module/planification/planification.module';
import { DirectionModule } from './modules/direction/direction.module';
import { ErpItemIdModule } from './modules/cart/erp-item-id/erp-item-id.module';
import { LocationsModule } from './modules/locations/locations.module';
import { OrderRetaillModule } from './modules/order-retaill/order-retaill.module';
import { ImagesModule } from '@la-pasta-module/images/images.module';
import { JdeModule } from '@la-pasta-module/jde/jde.module';
import { RetrievementsModule } from './modules/retrievements/retrievements.module';
import { ReportingOrdersModule } from '@la-pasta-module/reporting/reporting-orders/reporting-orders.module';
import { EexAuthMiddleware } from '@la-pasta-module/express-exchange/middleware/eex-auth.middleware';
import { ExpressExchangeController } from '@la-pasta-module/express-exchange/express-exchange.controller';
import { AppController } from './app.controller';
import { RetrievementReportingService } from './modules/reporting/retrievement-reporting/retrievement-reporting/retrievement-reporting.service';
import { RetrievementReportingController } from './modules/reporting/retrievement-reporting/retrievement-reporting/retrievement-reporting.controller';
import { PromoCodeModule } from './modules/promo-code/promo-code.module';
import { FeedbacksModule } from '@la-pasta-module/feedbacks/feedbacks.module';
import { ExpressExchangeModule } from '@la-pasta-module/express-exchange/express-exchange.module';
import { AuthorizationRemovalModule } from '@la-pasta-module/authorization-removal/authorization-removal.module';
import { ReloadBalanceModule } from './modules/reload-balance/reload-balance.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { SnitchsModule } from './modules/snitchs/snitchs.module';
import { PaymentsModule } from './modules/payments/payments.module';
import { ReportingReloadBalanceModule } from './modules/reporting/reporting-reload-balance/reporting-reload-balance.module';
import { CategoryModule } from '@la-pasta-module/category/category.module';
import { FaqModule } from '@la-pasta-module/faq/faq.module';
import { TechnicalSheetModule } from '@la-pasta-module/technical-sheet/technical-sheet.module';
import { LogoModule } from './modules/logo/logo.module';
import { BalanceModule } from './modules/balance/balance.module';
import { ImageBannerModule } from './modules/image-banner/image-banner.module';
import { OptionsModule } from '@la-pasta-module/options/options.module';
import { InvitationModule } from './modules/invitation/invitation.module';
import { LoyaltyProgramModule } from './modules/loyalty-program/loyalty-program.module';
import { ItemsModule } from './modules/items/items.module';
import { OrderItemsModule } from './modules/order-items/order-items.module';
import { OrderSupplierModule } from './modules/order-supplier/order-supplier.module';
import { AdvantagesModule } from '@la-pasta-module/advantages/advantages.module';
import { ScannerDataModule } from './modules/scanner-data/scanner-data.module';
import { QrCodeModule } from './modules/qr-code-management/qr-code.module';
import { ManualOrderModule } from './modules/manual-order/manual-order.module';
import { QrCodeFileManagementModule } from '@la-pasta-module/qr-code-file-management/qr-code-file-management.module';
import { WholeSaleModule } from './whole-sale/whole-sale.module';

@Module({
  imports: [
    RequestContextModule,
    WinstonModule.forRoot(winstonLoggerConfig),
    UsersModule,
    CommonModule,
    AuthorizationsModule,
    CompaniesModule,
    PackagingsModule,
    AuthModule,
    RenderTypesModule,
    StoresModule,
    ProductsModule,
    UnitModule,
    PriceModule,
    OrderModule,
    CallbackModule,
    PlanificationModule,
    I18nModule.forRoot({
      fallbackLanguage: 'fr',
      // fallbacks: {
      //   'en-CA': 'fr',
      //   'en-*': 'en',
      //   'fr-*': 'fr',
      // },
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [AcceptLanguageResolver],
    }),
    DirectionModule,
    ReportingOrdersModule,
    //   RMQModule.forRoot({
    //     exchangeName: 'seesaw_data',
    //     serviceName: 'seesaw_data',
    //     connections: [
    //         {
    //             login:'admin',
    //             password: 'admin',
    //             host: 'localhost',
    //         },
    //     ],
    // }),
    ErpItemIdModule,
    LocationsModule,
    OrderRetaillModule,
    ImagesModule,
    JdeModule,
    RetrievementsModule,
    ExpressExchangeModule,
    AuthorizationRemovalModule,
    PromoCodeModule,
    FeedbacksModule,
    ReloadBalanceModule,
    NotificationsModule,
    SnitchsModule,
    PaymentsModule,
    ReportingReloadBalanceModule,
    CategoryModule,
    FaqModule,
    TechnicalSheetModule,
    LogoModule,
    BalanceModule,
    ImageBannerModule,
    OptionsModule,
    InvitationModule,
    LoyaltyProgramModule,
    OptionsModule,
    ItemsModule,
    OrderItemsModule,
    OrderSupplierModule,
    AdvantagesModule,
    ScannerDataModule,
    QrCodeModule,
    ManualOrderModule,
    QrCodeFileManagementModule,
    WholeSaleModule,
  ],
  controllers: [AppController, RetrievementReportingController],
  providers: [RetrievementReportingService],

})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(EexAuthMiddleware)
      .exclude({ path: 'eex-transaction/get-token', method: RequestMethod.GET })
      .forRoutes(ExpressExchangeController)
  }
}

