import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService, t } from '@la-pasta/common';
import { ShippingRepository } from './repository';
import { CreateShippingDto, UpdateShippingDto } from './dto';
import { Shipping } from './entities/shipping.entity';

@Injectable()
export class ShippingService extends BaseService {
  constructor(
    private readonly shippingRepository: ShippingRepository
  ) {
    super(shippingRepository);
  }

  async createShipping(shippingDto: CreateShippingDto): Promise<QueryResult | Error> {
    // shippingDto.endRef = shippingDto.label.replace(/[\s]/gi, '_').toUpperCase();
    const shippingExist = await this.checkIfShippingExists(shippingDto as CreateShippingDto);

    if (!(shippingExist instanceof NotFoundException)) return new Error(t('error.SHIPPING_EXIST'));

    await this.checkIfShippingExists(shippingDto);
    return await this.create(shippingDto);
  }

  private async checkIfShippingExists(shippingDto: CreateShippingDto) {
    try {
      const query = {
        "endRef": shippingDto.endRef,
        "erpShipToId": shippingDto.erpShipToId,
        "$or": [
          { "startRef._id": `${shippingDto?.startRef?._id ?? ''}` },
          { "startRef.storeRef": `${shippingDto.startRef?.storeRef ?? ''}` }
        ]
      };
      if ('companyId' in shippingDto) query["companyId"] = shippingDto?.companyId;
      if ('category' in shippingDto) query["category"] = shippingDto.category;

      return await this.findOne({ filter: query });
    } catch (error) {
      console.log(error);
      this.logger.error(error)
    }
  }

  async updateShipping(id: string, shippingDto: UpdateShippingDto) {
    // if ('label' in shippingDto) {
    //   shippingDto.endRef = shippingDto.label.replace(/[\s]/gi, '_').toUpperCase()
    // }
    return await this.update({ _id: id }, shippingDto);
  }


  async getShippings(query: QueryFilter) {
    query = JSON.stringify(query) == JSON.stringify({}) ? { companyId: { $exists: false }, category: { $exists: false } } : query;
    return await this.findAll({ filter: query });
  }

  async getDefaultShippingsForClientSide(query: QueryFilter) {
    query['companyId'] = { $exists: false };
    query['category'] = { $exists: false };

    const shippings = await this.findAll({ filter: query });
    shippings?.data.sort((a, b) => {
      return a.label.localeCompare(b.label);
    });
    return shippings;
  }

  async getCategoryShippings(query: QueryFilter) {
    if (!('category' in query)) query['category'] = { $exists: true };

    query['companyId'] = { $exists: false };

    return await this.findAll({ filter: query });
  }
}
