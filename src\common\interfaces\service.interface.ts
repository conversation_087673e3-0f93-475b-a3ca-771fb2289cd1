import { Document, WithId } from "mongodb";

export interface ServiceInterface {

    create(data: Document): Promise<QueryResult>

    createMany(data: Document[]): Promise<QueryResult>

    findAll(query?: QueryOptions): Promise<getAllResult>

    findOne(query: QueryOptions): Promise<WithId<Document> | QueryResult>

    findAndModify(query: QueryOptions, data: Document): Promise<WithId<Document> | QueryResult>;

    count(query: QueryFilter): Promise<number | QueryResult>;

    update(filter: QueryFilter, data: Document): Promise<QueryResult>;

}