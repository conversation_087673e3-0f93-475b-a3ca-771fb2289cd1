import { Injectable } from '@nestjs/common';
import { PaymentMode, PaymentModeLabel } from '@la-pasta-module/order';
import { BaseService, converDateFilter, convertFilter } from '@la-pasta/common';
import { ReloadBalanceRepository } from '@la-pasta-module/reload-balance/repository';
import { ReloadBalance } from '@la-pasta-module/reload-balance/entities/reload-balance.entity';
import { CompanyCategory } from '@la-pasta-module/companies';

@Injectable()
export class ReportingReloadBalanceService extends BaseService {

    constructor(
        private reloadBalanceRepository: ReloadBalanceRepository

    ) {
        super(reloadBalanceRepository)
    }

    async getEvolutionPayementReloadBalance(query: QueryFilter) {
        query = converDateFilter(query);
        const data = await this.reloadBalanceRepository.getEvolutionPaymentForReloadBalance(query) as ReloadBalance[];
        const dataChart = {};
        const paymentMode = Object.values(PaymentMode).filter(key => typeof key === 'string');

        paymentMode.forEach(key => {
            dataChart[key] = data.filter(reload => reload?.payment?.mode?.id === PaymentMode[key]);
        })

        const filteredData = Object.fromEntries(
            Object.entries(dataChart).filter(([key, value]) => {
                return !['CREDIT', 'LOW_BALANCE', 'MY_ACCOUNT'].includes(key);
            })
        );


        return this.generateChartYearData(filteredData);
    }

    async getAmountEvolutionPerOperatorReloadBalance(query: QueryFilter) {
        query = convertFilter(query);
        const data = await this.reloadBalanceRepository.getEvolutionPaymentForReloadBalance(query) as ReloadBalance[];
        const dataChart = {};
        const paymentMode = Object.values(PaymentMode).filter(key => typeof key === 'string');

        paymentMode.forEach(key => {
            dataChart[key] = data.filter(reload => reload?.payment?.mode?.id === PaymentMode[key]);
        })
        const filteredData = Object.fromEntries(
            Object.entries(dataChart).filter(([key, value]) => {
                return !['CREDIT', 'LOW_BALANCE', 'MY_ACCOUNT'].includes(key);
            })
        );


        return this.generateAmountChartYearData(filteredData);
    }

    async getTotalAmountByPaymentMode(query: QueryFilter) {
        query = convertFilter(query);
        const data = await this.reloadBalanceRepository.getTotalAmountByPaymentMode(query);

        const mappedData = data.map(item => {
            const paymentMode = this.mapIdToPaymentMode(item._id);
            return {
                mode: paymentMode,
                total: item.total,
            };
        });
        return mappedData;
    }

    mapIdToPaymentMode(id) {
        switch (id) {
            case 0:
                return PaymentModeLabel[id];
            case 1:
                return PaymentModeLabel[id];
            case 2:
                return PaymentModeLabel[id];
            case 3:
                return PaymentModeLabel[id];
            case 5:
                return PaymentModeLabel[id];
            case 7:
                return PaymentModeLabel[id];
            case 9:
                return PaymentModeLabel[id];
            default:
                return '';
        }
    }

    async getSalesEvolutionByCompanies(filter: QueryFilter) {
        filter = converDateFilter(filter);
        filter['company'] = { $exists: 1 };
        const data = await this.reloadBalanceRepository.getEvolutionByCompaniesReloadBalances(filter) as any[];
        const dataChart = {};
        const array = Object.values(CompanyCategory).filter(key => typeof key === 'string');
        array.forEach(key => dataChart[key] = data.filter(company => company.category == CompanyCategory[key]));

        return this.generateChartYearData(dataChart);
    }

    async getSalesAmountEvolutionByCompanies(filter: QueryFilter) {
        filter = convertFilter(filter);
        filter['company'] = { $exists: 1 };
        const data = await this.reloadBalanceRepository.getEvolutionByCompaniesReloadBalances(filter) as any[];
        const dataChart = {};
        const array = Object.values(CompanyCategory).filter(key => typeof key === 'string');
        array.forEach(key => dataChart[key] = data.filter(company => company.category == CompanyCategory[key]));

        return this.generateAmountChartYearData(dataChart);
    }

    async getTotalReloadByStatusJde(query: QueryFilter) {
        query = convertFilter(query);
        const res = await this.reloadBalanceRepository.getTotalReloadByStatusJde(query) as any[];
        return res.sort((a, b) => a?.status - b?.status);
    }

    async getTotalReloadByStatusOperator(query: QueryFilter) {
        query = convertFilter(query);
        const res = await this.reloadBalanceRepository.getTotalReloadByStatusOperator(query) as any[];
        return res;
    }

    async getTotalAmountReloadByCategory(query: QueryFilter) {
        query = convertFilter(query);
        const res = await this.reloadBalanceRepository.getTotalAmountReloadByCategory(query) as any[];
        return res;

    }

    async getAllReloadTotals(query: QueryFilter) {
        query = convertFilter(query);
        const res = await this.reloadBalanceRepository.getAllReloadTotals(query) as any[];
        delete query.status;
        const res2 = await this.reloadBalanceRepository.getAllReloadTotals(query) as any[];
        res[0].totalReload = res2[0]['totalReload'];
        return res;

    }

    generateChartYearData(data: any) {
        const res = {};
        for (const key in data) {
            const Jan = (data[key].filter(elt => elt?.monthOfYear == 1)).length;

            const Fev = (data[key].filter(elt => elt?.monthOfYear == 2)).length;

            const Mar = (data[key].filter(elt => elt?.monthOfYear == 3)).length;

            const Avr = (data[key].filter(elt => elt?.monthOfYear == 4)).length;

            const Mai = (data[key].filter(elt => elt?.monthOfYear == 5)).length;

            const Jun = (data[key].filter(elt => elt?.monthOfYear == 6)).length;

            const Jul = (data[key].filter(elt => elt?.monthOfYear == 7)).length;

            const Aou = (data[key].filter(elt => elt?.monthOfYear == 8)).length;

            const Sep = (data[key].filter(elt => elt?.monthOfYear == 9)).length;

            const Oct = (data[key].filter(elt => elt?.monthOfYear == 10)).length;

            const Nov = (data[key].filter(elt => elt?.monthOfYear == 11)).length;

            const Dec = (data[key].filter(elt => elt?.monthOfYear == 12)).length;
            res[key] = [Jan, Fev, Mar, Avr, Mai, Jun, Jul, Aou, Sep, Oct, Nov, Dec];
        }
        return res;
    }

    generateAmountChartYearData(data: any) {
        const res = {};
        for (const key in data) {
            const Jan = (data[key].filter(elt => elt?.monthOfYear == 1)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Fev = (data[key].filter(elt => elt?.monthOfYear == 2)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Mar = (data[key].filter(elt => elt?.monthOfYear == 3)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Avr = (data[key].filter(elt => elt?.monthOfYear == 4)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Mai = (data[key].filter(elt => elt?.monthOfYear == 5)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Jun = (data[key].filter(elt => elt?.monthOfYear == 6)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Jul = (data[key].filter(elt => elt?.monthOfYear == 7)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Aou = (data[key].filter(elt => elt?.monthOfYear == 8)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Sep = (data[key].filter(elt => elt?.monthOfYear == 9)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Oct = (data[key].filter(elt => elt?.monthOfYear == 10)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Nov = (data[key].filter(elt => elt?.monthOfYear == 11)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);

            const Dec = (data[key].filter(elt => elt?.monthOfYear == 12)).reduce((acc, curr) => acc + (curr?.payment?.amount || curr?.amount || 0), 0);
            res[key] = [Jan, Fev, Mar, Avr, Mai, Jun, Jul, Aou, Sep, Oct, Nov, Dec];
        }
        return res;
    }
}
