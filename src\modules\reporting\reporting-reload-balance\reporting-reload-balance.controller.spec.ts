import { Test, TestingModule } from '@nestjs/testing';
import { ReportingReloadBalanceController } from './reporting-reload-balance.controller';
import { ReportingReloadBalanceService } from './reporting-reload-balance.service';

describe('ReportingReloadBalanceController', () => {
  let controller: ReportingReloadBalanceController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReportingReloadBalanceController],
      providers: [ReportingReloadBalanceService],
    }).compile();

    controller = module.get<ReportingReloadBalanceController>(ReportingReloadBalanceController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
