import { Category } from '@la-pasta-module/category/entities';
import { getDataInExcelFile } from '@la-pasta/common';
import { MongoDatabase } from '@la-pasta/database';
import moment from 'moment';
import { setLoader, stopLoader } from 'scripts/common';

const database = MongoDatabase.getInstance();
const collectionName = 'categories';


export async function insertCategoriesDataFile() {
  try {
    setLoader('Chargement des categories depuis le fichier Excel\n');
    const CategoryCollection = (await database.getDatabase()).collection(collectionName)

    const datas = getDataInExcelFile('la-pasta.categories') as Category[];

    let categories: Category[] = [];

    for (const data of datas) {
      const category: Category = {
        label: data?.label,
        code: data?.code,
        description: data?.description,
        enable: true,
        created_at: moment().valueOf(),
      }

      const query: Category = {
        label: data?.label,
        code: data?.code,
        enable: true,
      }

      const res = await CategoryCollection.updateOne({ query }, { $set: { ...category } }, { upsert: true });
      if (res?.matchedCount == 0 && !res?.upsertedCount)
        categories.push(category);
    }

    if (categories.length)
      await CategoryCollection.insertMany(categories as unknown[]);

    stopLoader(true);
    console.log('catégories insérées')

  } catch (error) {
    console.error('Erreur lors de l\'insertion des catégories :', error);
  }
}
