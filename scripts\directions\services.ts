import { MongoDatabase } from "@la-pasta/database";
import { ObjectId } from "mongodb";
import moment from "moment";
import { dropCollection, setLoader, stopLoader } from "../common";

const services: any[] = [{
  "_id": "621dcee2cb0c0f3f641c55bd",
  "DIRECTIONS": "BPE",
  "SERVICES": "BPE",
  "directionId": "621dcda5aa44e43d68e77315"
}, {
  "_id": "621dcee2cb0c0f3f641c55be",
  "DIRECTIONS": "BPE",
  "SERVICES": "Béton",
  "directionId": "621dcda5aa44e43d68e77315"
}, {
  "_id": "621dcee2cb0c0f3f641c55c6",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55c8",
  "DIRECTIONS": "DFC",
  "SERVICES": "Comptabilité Client",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55cb",
  "DIRECTIONS": "DFC",
  "SERVICES": "Contrôle de Gestion",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55d6",
  "DIRECTIONS": "DG",
  "SERVICES": "Sécurité",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55d7",
  "DIRECTIONS": "DG",
  "SERVICES": "Projets",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55da",
  "DIRECTIONS": "DG",
  "SERVICES": "Missions",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55db",
  "DIRECTIONS": "DG",
  "SERVICES": "Mortier",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55de",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "directionId": "621dcda5aa44e43d68e7731a"
}, {
  "_id": "621dcee2cb0c0f3f641c55e3",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55e4",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55e8",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Méthodes",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55ea",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Procédés",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55eb",
  "DIRECTIONS": "DUB",
  "SERVICES": "Ensachage",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55ec",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Méthodes",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c5604",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "directionId": "621dcda5aa44e43d68e77320"
}, {
  "_id": "621dcee2cb0c0f3f641c55c1",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55c2",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55c3",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55c4",
  "DIRECTIONS": "DFC",
  "SERVICES": "Contrôle de gestion",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55cc",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Service",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55cd",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55cf",
  "DIRECTIONS": "DFC",
  "SERVICES": "Contrôle de Gestion",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55d0",
  "DIRECTIONS": "DG",
  "SERVICES": "Audit Interne",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55d1",
  "DIRECTIONS": "DG",
  "SERVICES": "Digital",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55d5",
  "DIRECTIONS": "DG",
  "SERVICES": "Audit Interne",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55d9",
  "DIRECTIONS": "DG",
  "SERVICES": "Mortier",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55e0",
  "DIRECTIONS": "DM",
  "SERVICES": "Marketing",
  "directionId": "621dcda5aa44e43d68e7731b"
}, {
  "_id": "621dcee2cb0c0f3f641c55e6",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55e7",
  "DIRECTIONS": "DUB",
  "SERVICES": "Laboratoire",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55bb",
  "DIRECTIONS": "BPE",
  "SERVICES": "Laborantin",
  "directionId": "621dcda5aa44e43d68e77315"
}, {
  "_id": "621dcee2cb0c0f3f641c55c7",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55ce",
  "DIRECTIONS": "DFC",
  "SERVICES": "Comptabilité Client",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55d3",
  "DIRECTIONS": "DG",
  "SERVICES": "Laboratoire",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55e2",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "directionId": "621dcda5aa44e43d68e7731c"
}, {
  "_id": "621dcee2cb0c0f3f641c55e9",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "directionId": "621dcda5aa44e43d68e7731d"
}, {
  "_id": "621dcee2cb0c0f3f641c55bc",
  "DIRECTIONS": "BPE",
  "SERVICES": "Texte",
  "directionId": "621dcda5aa44e43d68e77315"
}, {
  "_id": "621dcee2cb0c0f3f641c55c0",
  "DIRECTIONS": "DFC",
  "SERVICES": "Service IT",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55c5",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55c9",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55ca",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "directionId": "621dcda5aa44e43d68e77317"
}, {
  "_id": "621dcee2cb0c0f3f641c55d2",
  "DIRECTIONS": "DG",
  "SERVICES": "DG",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55d4",
  "DIRECTIONS": "DG",
  "SERVICES": "Communication",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55d8",
  "DIRECTIONS": "DG",
  "SERVICES": "Juridique",
  "directionId": "621dcda5aa44e43d68e77318"
}, {
  "_id": "621dcee2cb0c0f3f641c55df",
  "DIRECTIONS": "DL",
  "SERVICES": "Service Clients",
  "directionId": "621dcda5aa44e43d68e7731a"
}, {
  "_id": "621dcee2cb0c0f3f641c55e1",
  "DIRECTIONS": "DM",
  "SERVICES": "Ventes",
  "directionId": "621dcda5aa44e43d68e7731b"
}, {
  "_id": "621dcee2cb0c0f3f641c55e5",
  "DIRECTIONS": "DUB",
  "SERVICES": "Port",
  "directionId": "621dcda5aa44e43d68e7731d"
}]

const database = MongoDatabase.getInstance();
const collectionName = 'services';

export async function insertServices() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des services\n');

    services.forEach(service => { service._id = new ObjectId(service._id), service.enable = true; service.create_at = moment().valueOf() });
    const insertedServices = await (await database.getDatabase()).collection(collectionName).insertMany(services);
    stopLoader(insertedServices);
  } catch (error) {
    console.error(error)
  }
}