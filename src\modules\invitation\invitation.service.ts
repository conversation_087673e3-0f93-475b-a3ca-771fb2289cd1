import { ConflictException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { CreateInvitationDto } from './dto/create-invitation.dto';
import { Invitation, InvitationStatus } from './entities/invitation.entity';
import { InvitationRepository } from './repository';
import { BaseService, converDateFilter } from '@la-pasta/common';

import { NotificationsService } from '@la-pasta-module/notifications/notifications.service';
import { ClientProxy } from '@nestjs/microservices';
import { BaseUser, ReferralStatus, User, UsersService } from '@la-pasta-module/users';
import moment from 'moment';
@Injectable()
export class InvitationService extends BaseService {
  constructor(
    private readonly invitationRepository: InvitationRepository,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,
    private readonly notificationService: NotificationsService,
    @Inject(forwardRef(() => UsersService)) private readonly userService: UsersService,

  ) {
    super(invitationRepository);
  }

  async createInvitation(referrerId: string, createInvitationDto: CreateInvitationDto) {
    const { prospectTel } = createInvitationDto;
  
    const existingUser = await this.checkIfUserExists(prospectTel);
    const existingInvitation = await this.checkIfInvitationExists(prospectTel);
  
    if (existingInvitation?.prospectTel) {
      throw new ConflictException('Ce numéro de téléphone a déjà accepté une invitation.');
    }
  
    // Récupérer les informations du parrain
    const referrer = await this.userService.findOne({ filter: { _id: referrerId } }) as unknown as BaseUser;
    
    const invitation = await this.create({
      referrerId,
      prospectTel,
      status: InvitationStatus.PENDING,
      referredUserId: existingUser?._id,
    });
  
    await this.sendReferralSms(prospectTel, existingUser, referrer);
  
    return invitation;
  }
  
  async sendReferralSms(prospectTel: number, existingUser: any, referrer: BaseUser) {
    const referrerName = referrer.firstName && referrer.lastName 
      ? `${referrer.firstName} ${referrer.lastName}`
      : "Un client La Pasta";
  
    if (existingUser) {
      this.queueClient.emit('sms_received', {
        receiver: prospectTel,
        message: `🌟 Bonjour ! ${referrerName} vous a choisi(e) comme filleul(e) sur Click Cadyst ! En acceptant son invitation de parrainage, vous bénéficierez d'avantages exclusifs. Rejoignez la famille CADYST GRAIN ! 🎁`,
      });
    } else {
      this.queueClient.emit('sms_received', {
        receiver: prospectTel,
        message: `🌟 ${referrerName} pense à vous ! Votre parrain vous invite à rejoindre Click CADYST, la plateforme de commande qui vous simplifie la vie. Inscrivez-vous maintenant et profitez d'avantages exclusifs ! 🎁 Bienvenue dans la famille CADYST GRAIN !`,
      });
      this.queueClient.emit('sms_received', {
        receiver: prospectTel,
        message: `Un commercial ce raprocheras de vous pour vous enrengistrer ! Veuillez telecharger l'application sur play store: \n https://play.google.com/store/apps/details?id=com.clickcadyst.mobile&hl=en&pli=1`,
      });
    }
  }

  private async checkIfInvitationExists(prospectTel: number) {
    return await this.findOne({ filter: { prospectTel } });
  }

  async checkIfUserExists(prospectTel: number) {
    return await this.userService.findOne({
      filter: { tel: prospectTel },
      projection: { _id: 1 }
    });

  }

  async acceptInvitation(id: string, referredUserId: string) {
    const invitation = await this.findOne({ filter: { _id: id } });

    if (!invitation) {
      throw new Error('Invitation not found');
    }

    await this.update(
      { _id: id },
      {
        status: InvitationStatus.VALIDATED,
        referredUserId,
        acceptedAt: moment().valueOf(),
      }
    );

    await this.userService.update(invitation.referrerId, {
      $set: {
        'referrals.$[elem].status': ReferralStatus.Accepted,
        'referrals.$[elem].updated_at': new Date(),
      },
      arrayFilters: [{ 'elem.referralId': id }],
    });

    return { message: 'Invitation accepted successfully' };
  }

  async expireOldInvitations() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const expiredInvitation = await this.findOne({
      filter: { status: InvitationStatus.PENDING, created_at: { $lte: thirtyDaysAgo } },
    });

    if (expiredInvitation) {
      await this.update(
        { _id: expiredInvitation._id },
        { status: InvitationStatus.COMPLETED, updated_at: new Date() }
      );

      // Mettre à jour le statut du filleul dans le tableau des filleuls du parrain
      await this.userService.update(expiredInvitation.referrerId, {
        $set: {
          'referrals.$[elem].status': ReferralStatus.Expired,
          'referrals.$[elem].updated_at': new Date(),
        },
        arrayFilters: [{ 'elem.referralId': expiredInvitation._id }]
      });
    }

    return { message: 'Old invitations have been expired' };
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    query = converDateFilter({ filter: query });
    // query['status'] = OrderStatus.PAID;

    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async getInvitation(query: QueryFilter) {
    const data = await this.findOne({ filter: query }) as unknown as Invitation;

    if (data && data?.referrerId)
      data.referrer = await this.userService.findOne({
        filter: { _id: data?.referrer },
        projection: { firstName: 1, lastName: 1, tel: 1, email: 1 }
      }) as unknown as User;

    return data;
  }

}