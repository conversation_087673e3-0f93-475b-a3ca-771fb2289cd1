import { ImagesService } from "./images.service";
import { Body, Controller, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { setResponseController } from "@la-pasta/common";

@Controller('images')
export class ImagesController {
    constructor(
        private imageService: ImagesService,
    ) { }

    @Post()
    async createImage(@Body() image: any) {
        return await this.imageService.createImage(image);
    }

    @Get()
    async getImage(@Query() param: any) {
        return await this.imageService.getImage({ filter: param });
    }

    @Patch(':id')
    update(@Param('id') appRef: string, @Body() updateImage: any) {
        console.log(updateImage)
        const data = this.imageService.updateDataUrls(appRef, updateImage);
        return setResponseController(data);
    }

}