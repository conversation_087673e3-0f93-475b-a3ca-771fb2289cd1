import { config } from "convict-config";
import { JwtGuard } from "@la-pasta-module/auth";
import { Controller, Get, Query, UseGuards } from "@nestjs/common";

@Controller('mobile')
export class AppController {
    constructor() { }

    @UseGuards(JwtGuard)
    @Get('minimal-version')
    async findVersion(@Query('platform') platform: "android" | "ios") {
        platform ??= 'android';

        const minimalVersion = config.get(`minimalMobileBuildVersion.${platform}`);

        return { minimalVersion };
    }
}