import readline from 'readline';
import { insertUsersDataFile } from 'scripts/users/user-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données des utilisateurs de Click CADYST, ce processus effacera la collection utsers.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertUsersDataFile();
      console.log('Données des utilisateurs insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();