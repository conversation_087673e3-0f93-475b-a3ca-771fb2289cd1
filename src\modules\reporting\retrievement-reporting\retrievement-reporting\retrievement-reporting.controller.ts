import { Controller, Get, Query } from '@nestjs/common';
import { RetrievementReportingService } from './retrievement-reporting.service';

@Controller('retrievement-reporting')
export class RetrievementReportingController {
    constructor(private retrievReportingService: RetrievementReportingService) {
        
    }

    @Get('retrievement-evolution')
    async getEvolutionAes(@Query() query: QueryFilter) {
         return await this.retrievReportingService.getEvolution(query);
    }

    @Get('retrievement-repartition')
    async getRepartitionAes(@Query() query: QueryFilter) {
         return await this.retrievReportingService.getAeRepartition(query);
    }
    
    
}
