import { Injectable, NotFoundException } from "@nestjs/common";
import { PromoCode } from "../entities";
import { getUser, t } from "@la-pasta/common";
import { Cart } from "@la-pasta-module/price/compute_price";

@Injectable()
export class PromoCodeValidation {

    private promoCode: PromoCode;

    validate(codePromo: PromoCode, cart: Partial<Cart>) {
        this.promoCode = codePromo;
        this.validatePromoCode(codePromo);
        this.validateDates(codePromo);
        this.validateUserAttributes(codePromo, ['userCategoriesAssociated', 'regions']);
        this.validatePackaging(codePromo, cart);
        this.validateProduct(codePromo, cart);
    }

    private validatePromoCode(codePromo: PromoCode) {
        if (!codePromo) throw new NotFoundException(t('error.NO_PROMO_CODE'));
    }

    private validateDates({ dates }: PromoCode): void {
        const currentDateTimestamp = new Date().getTime();
        if (currentDateTimestamp < dates?.start || currentDateTimestamp > dates?.end) {
            throw new NotFoundException(t('error.PROMO_CODE_DATE_NOT_VALID'));
        }
    }

    private validateUserAttributes(codePromo: PromoCode, attributes: string[]) {
        attributes.forEach(attribute => this.validateAttribute(codePromo, attribute));
    }

    private validateAttribute(codePromo: PromoCode, attribute: string) {
        const attributesMap = {
            'userCategoriesAssociated': 'category',
            'regions': 'address.region',
        };

        if (!this.isAttributePresent(attribute)) return;

        const userAttributeValue = this.getUserAttribute(attributesMap[attribute]);
        if (!codePromo[attribute].includes(userAttributeValue)) throw new NotFoundException(t('error.PROMO_CODE_NOT_VALID'));

    }

    private getUserAttribute(attribute: string): string {
        const user = getUser();
        const targetObject = 'company' in user ? user.company : user;

        return attribute.split('.').reduce((obj, part) => (obj && obj[part] != null) ? obj[part] : '', targetObject);
    }

    private validatePackaging(codePromo: PromoCode, cart: Partial<Cart>) {
        if (!this.isAttributePresent('packagings')) return;

        const isValidPackaging = codePromo?.packagings?.findIndex(packaging => packaging?._id === cart.items[0]?.packaging?._id) >= 0;
        if (!isValidPackaging) throw new NotFoundException(t('error.PROMO_CODE_NOT_VALID'));
    }

    private validateProduct(codePromo: PromoCode, { items }: Partial<Cart>) {
        if (!this.isAttributePresent('products')) return;

        const isValidProduct = codePromo?.products?.some(product => items?.findIndex(item => item?.product?._id === product?._id) >= 0);
        if (!isValidProduct) throw new NotFoundException(t('error.PROMO_CODE_NOT_VALID'));
    }

    private isAttributePresent(attribute: string): boolean {
        return this.promoCode[attribute] && this.promoCode[attribute].length;
    }
}