import { ReloadBalance } from "@la-pasta-module/reload-balance/entities/reload-balance.entity";
import { MobileMoneyEventData } from "./mobile-money.event";
import { User } from "@la-pasta-module/users";

export class MobileMoneyReloadBalanceEvent extends MobileMoneyEventData {
    reloadBalance: {
        _id: string;
        user: Partial<User>
    };

    constructor(reloadBalance: ReloadBalance) {
        super({ payment: reloadBalance.payment, amount: reloadBalance?.payment?.amount });
        this.reloadBalance = {
            _id: reloadBalance?._id.toString(),
            user: reloadBalance?.user
        };
    }

}