import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON>de<PERSON><PERSON>roll<PERSON> } from './jde.controller';
import { JdeService } from './jde.service';

describe('JdeController', () => {
  let controller: JdeController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [JdeController],
      providers: [JdeService],
    }).compile();

    controller = module.get<JdeController>(JdeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
