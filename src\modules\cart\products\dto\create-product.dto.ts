import { Category } from "@la-pasta-module/category/entities";
import { IsDataURI, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateProductDto {
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    label: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsDataURI({ message: i18nValidationMessage('validation.BAD_DATA_URI') })
    image: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    normLabel: Address;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    erpRef: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    description: string;

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    category: Partial<Category>;

    @IsOptional()
    specifications: string[];

    @IsOptional()
    advantages?: string[];

    @IsOptional()
    cautions?: string[];

    @IsOptional()
    created_at: number;

    constructor(createPriceDto: Partial<CreateProductDto>) {
        Object.assign(this, createPriceDto);
    }
}
