import { Test, TestingModule } from '@nestjs/testing';
import { RemovalReportingController } from './removal-reporting.controller';

describe('RemovalReportingController', () => {
  let controller: RemovalReportingController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RemovalReportingController],
    }).compile();

    controller = module.get<RemovalReportingController>(RemovalReportingController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
