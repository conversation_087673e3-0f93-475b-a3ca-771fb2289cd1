import { PlanificationAction } from '@la-pasta-module/planification';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class PlanificationAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      PlanificationAction.CREATE,
      PlanificationAction.DELETE,
      PlanificationAction.UPDATE,
      PlanificationAction.VIEW,
      PlanificationAction.CUSTOM] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == PlanificationAction.CREATE) return user.roles.includes(UserRole.BACKOFFICE);

    if (permission == PlanificationAction.CUSTOM)
      return user.enable === true && user.roles.includes(UserRole.CLIENT)

    return user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const PlanificationAuthorizationInstance = new PlanificationAuthorization();