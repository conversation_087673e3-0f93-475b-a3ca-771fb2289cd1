import { Payment } from './../payments/entities/payment.entity';
import moment from 'moment';
import { ObjectId } from 'mongodb';
import { lastValueFrom } from 'rxjs';
import { BaseService, setResponse } from '@la-pasta/common';
import { ClientProxy } from '@nestjs/microservices';
import { JdeService } from '@la-pasta-module/jde';
import { ReloadBalanceRepository } from './repository';
import { Permission } from '@la-pasta/infrastructures/security';
import { generateRandomString, getUser } from '@la-pasta/common';
import { PaymentAction, PaymentMode } from '@la-pasta-module/order';
import { CreateReloadBalanceDto } from './dto/create-reload-balance.dto';
import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { OperatorTransactionStatus, ReloadBalance, TransactionStatus } from './entities/reload-balance.entity';
import {
  AfrilandPaymentEvent,
  ExpressExchangePaymentEvent,
  M2UEventReloadBalance,
  MobileMoneyReloadBalanceEvent,
  OrangeMoneyReloadBalanceEvent
} from '@la-pasta-module/order/events';
import { CompaniesService } from '@la-pasta-module/companies';
import { PaymentsRepository } from '@la-pasta-module/payments/repository';

@Injectable()
export class ReloadBalanceService extends BaseService {

  constructor(
    @Inject('PAYMENT') private readonly paymentClient: ClientProxy,
    @Inject('CRON') private readonly cronClient: ClientProxy,
    private JdeSrv: JdeService,
    private CompanySrv: CompaniesService,
    private paymentRepo: PaymentsRepository,
    private ReloadBalanceRepository: ReloadBalanceRepository,
  ) { super(ReloadBalanceRepository); }

  async initiateReloadBalance(reloadBalanceData: CreateReloadBalanceDto) {
    Permission.paymentAuthorization(
      getUser(),
      PaymentAction[PaymentMode[reloadBalanceData?.payment?.mode?.id]]
    );
    const currentBalance = await this.JdeSrv.getAccountBalance(reloadBalanceData?.company?._id);
    const { category } = await this.CompanySrv.findOne({ filter: { _id: new ObjectId(reloadBalanceData?.company?._id) } });

    let reloadBalance: ReloadBalance = {
      _id: new ObjectId(),
      nbrTries: 0,
      ...reloadBalanceData,
      currentBalance,
      company: {
        ...reloadBalanceData.company,
        category: category
      },
      status: TransactionStatus.INITIATED,
      payment: {
        ...reloadBalanceData.payment,
        reference: `Recharge du Compte via ${reloadBalanceData?.payment?.mode?.label}`
      }
    };

    const paymentResponse = await lastValueFrom(this.paymentClient.send(
      { cmd: `${reloadBalanceData.payment.mode.id}_payment_reload_balance` },
      this.loadReloadBalanceEventData(reloadBalance)
    ));
    if (paymentResponse instanceof Error || paymentResponse?.name === 'HttpException' || !paymentResponse)
      throw new HttpException(paymentResponse, HttpStatus.INTERNAL_SERVER_ERROR);

    if (reloadBalanceData?.payment?.mode?.id === PaymentMode.EXPRESS_EXCHANGE) reloadBalance.payment['amount'] = paymentResponse?.amount;

    reloadBalance.payment['jdeTransactionId'] = this.generateUniqueReferenceForReloadBalance({ ...reloadBalance, paymentInfo: { ...paymentResponse } });

    reloadBalance = {
      ...reloadBalance,
      currentBalance,
      paymentInfo: { ...paymentResponse as unknown as PaymentInfo<'status'> },
      uniqueReference: generateRandomString(2) + moment().valueOf().toString(),
    };

    const newInsertedReloadBalance = await this.create(reloadBalance);
    if (reloadBalance.paymentInfo.status === TransactionStatus.SUCCESSFUL)
      await lastValueFrom(await this.JdeSrv.paymentRequestToJde((newInsertedReloadBalance?.data).toString()));

    return { message: 'La recharge de votre compte a été initiée', id: newInsertedReloadBalance.data };
  }

  async getReloadBalance(query: QueryOptions) {
    return await this.findAll(query);
  }

  loadReloadBalanceEventData(reloadBalance: ReloadBalance) {
    const events = {
      [PaymentMode.AFRILAND]: AfrilandPaymentEvent,
      [PaymentMode.ORANGE_MONEY]: OrangeMoneyReloadBalanceEvent,
      [PaymentMode.MOBILE_MONEY]: MobileMoneyReloadBalanceEvent,
      [PaymentMode.EXPRESS_EXCHANGE]: ExpressExchangePaymentEvent,
      [PaymentMode.M2U]: M2UEventReloadBalance,
    };

    const data = new events[reloadBalance?.payment?.mode?.id](reloadBalance);
    return data;
  }

  private generateUniqueReferenceForReloadBalance(_paymentData) {
    if (_paymentData?.payment?.mode?.id === PaymentMode.ORANGE_MONEY) {
      const values = _paymentData?.paymentInfo?.txnid.split('.');
      const value = values[values?.length - 1];
      return `${_paymentData?.payment?.mode?.label} /${value || _paymentData?.paymentInfo?.transactionId.slice(0, 10)}`;
    }
    if (_paymentData?.payment?.mode?.id === PaymentMode.MOBILE_MONEY)
      return `${_paymentData?.payment?.mode?.label} N°${_paymentData?.paymentInfo?.financialTransactionId.slice(0, 10)
        ?? _paymentData?.paymentInfo?.transactionId.slice(0, 10)}`;

    if (_paymentData?.payment?.mode.id === PaymentMode.EXPRESS_EXCHANGE)
      return `EXCH N°${`${_paymentData?.paymentInfo?.transactionId}`.slice(0, 10)}`;

    return `${_paymentData?.payment?.mode?.label} N°${`${_paymentData?.paymentInfo?.transactionId}`.slice(0, 10)}`;
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async verifyPaymentStatusToOperator(data: any) {
    const { reloadBalanceId, mode } = data;
    try {
      const query = {
        "reloadBalance._id": reloadBalanceId,
        $or: [{ "transactionId": { $exists: true } }, { 'paymentResponse.data.payToken': { $exists: true } }],

      };
      const payment = await this.paymentRepo.findOne({ filter: query }) as unknown as Payment;

      this.logger.log(`Call ${mode} API to get payment status for this reload Balance ${payment?.reloadBalance?._id} with transactionID: ${payment?.transactionId}`);

      const res = await lastValueFrom(this.paymentClient.send({ cmd: `${mode}_payment_verification` }, payment?.transactionId ?? payment?.paymentResponse?.data?.payToken));

      if ('reloadBalance' in payment && [OperatorTransactionStatus.SUCCESSFUL, 'SUCCESSFULL'].includes(res?.status)) {
        await this.update({ _id: payment?.reloadBalance?._id }, {
          "paymentInfo": { ...payment.reloadBalance?.paymentInfo, ...res }
        });
      }
      return setResponse(HttpStatus.OK, res?.status)

    } catch (error) {
      return setResponse(error.code ?? HttpStatus.INTERNAL_SERVER_ERROR, error?.message)
    }
    // return mode === PaymentMode.MOBILE_MONEY ? await this.verificationMTNPaymentForReloadBalance(reloadBalanceId) : await this.PaymentOrangeVerificationForReloadBalance(reloadBalanceId)

  }

  async verificationMTNPaymentForReloadBalance(reloadBalanceId: string) {
    const query = {
      "reloadBalance._id": reloadBalanceId,
      $or: [{ nbrTries: { $lte: 6 } }, { nbrTries: { $exists: 0 } }],
    };
    const paymentMtn = await this.paymentRepo.findOne({ filter: query }) as unknown as Payment;

    const nbrTries = paymentMtn?.nbrTries ? paymentMtn?.nbrTries + 1 : 1;
    await this.paymentRepo.update({ _id: paymentMtn?._id }, { "nbrTries": nbrTries });

    this.logger.log(`Call mtn API to get payment status for this reload Balance ${paymentMtn?.reloadBalance?._id} with transactionID: ${paymentMtn?.transactionId}`);

    const res = await lastValueFrom(this.paymentClient.send({ cmd: `${PaymentMode.MOBILE_MONEY}_payment_verification` }, paymentMtn?.transactionId));

    if ('reloadBalance' in paymentMtn && [OperatorTransactionStatus.SUCCESSFUL].includes(res?.status)) {
      await this.update({ _id: paymentMtn?.reloadBalance?._id }, {
        "paymentInfo": { ...paymentMtn.reloadBalance?.paymentInfo, ...res }
      });
    }
  }
}
