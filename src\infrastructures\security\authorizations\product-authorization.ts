import { ProductAction } from '@la-pasta-module/cart/products';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class ProductAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      ProductAction.CREATE,
      ProductAction.DELETE,
      ProductAction.UPDATE,
      ProductAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == ProductAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const ProductAuthorizationInstance = new ProductAuthorization();