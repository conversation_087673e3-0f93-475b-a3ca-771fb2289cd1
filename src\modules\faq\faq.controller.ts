import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { FaqService } from './faq.service';
import { JwtGuard } from '@la-pasta-module/auth';
import { getUser } from '@la-pasta/common';
import { CreateFaqDto } from './dto/create-faq.dto';
import { Permission } from '@la-pasta/infrastructures/security';
import { FaqAction } from './actions/faq.action';

@Controller('faq')
export class FaqController {
  constructor(private readonly faqService: FaqService) { }
  @UseGuards(JwtGuard)
  @Post()
  async create(@Body() createFaqDto: CreateFaqDto) {
    Permission.faqAuthorization(getUser(), FaqAction.CREATE);
    return await this.faqService.createFaq(createFaqDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.faqAuthorization(getUser(), FaqAction.VIEW);
    return await this.faqService.findAllFaq({ filter: query });
  }
  @UseGuards(JwtGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    Permission.faqAuthorization(getUser(), FaqAction.VIEW);
    return this.faqService.findOneFaq(id);
  }
  @UseGuards(JwtGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateFaqDto) {
    Permission.faqAuthorization(getUser(), FaqAction.CREATE);
    return this.faqService.updateFaq(id, updateFaqDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.faqService.remove(+id);
  }
}
