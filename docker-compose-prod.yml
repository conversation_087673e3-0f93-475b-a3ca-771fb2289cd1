version: "3"
services:
  mongodb:
    image: 'mongo:5.0.12'
    restart: unless-stopped
    volumes:
      - /home/<USER>/production-v3/mongo-volume:/data/db

  backoffice:
    image: 'londotech/mycimencam-backoffice-v3:1.0.0'
    restart: unless-stopped
    depends_on:
      - api
    ports:
      - '3005:4000'

  website:
    image: 'londotech/mycimencam-website-v3:1.0.0'
    restart: unless-stopped
    depends_on:
      - api
    ports:
      - '3006:4000'

  api:
    image: "londotech/mycimencam-api-v3:1.0.0"
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - HOST=api
      - PORT=3000
      - ORANGE_CHANNELUSER=691301143
      - ORANGE_PIN=2222
      - DB_V2_MONGO_HOST=mongodb
      - DB_V2_MONGO_NAME=mycimencam
      - DB_MYCIMENCAM_USERNAME_V2=logistic
      - DB_MYCIMENCAM_PASSWORD_V2=_6FeRzrHG2wMVBTj
      - MINIMAL_MOBILE_BUILD_VERSION=1.6.10
      - FLUENTD_HOST=**************
    volumes:
      - /home/<USER>/production-v3/logs/api:/usr/src/cimencam/logs
    entrypoint: npm run start:prod
    #entrypoint: node src/main.js
    depends_on:
      - mongodb
    ports:
      - '3001:3000'

  notif-api:
    image: "londotech/mycimencam-notif-api:1.0.0"
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - HOST=notif-api
      - DB_MONGO_NAME=mycimencam-v3
      - COLLECTION_MONGO_NAME=queue
      - DB_MONGO_HOST=mongodb
      - CLIENT_ID=9544980c-2e81-4625-9102-9b5774b31a2d
      - SENDER_ADDRESS=<EMAIL>
    volumes:
      - /home/<USER>/production-v3/logs/notif-api:/usr/src/cimencam/logs
    entrypoint: npm run start:prod
    depends_on:
      - mongodb

  payment-api:
    image: "londotech/mycimencam-payment:1.0.0"
    restart: unless-stopped
    environment:
      - HOST=payment-api
      - NODE_ENV=production
    volumes:
      - /home/<USER>/production-v3/logs/payment:/usr/src/cimencam/logs
    entrypoint: npm run start:prod
    depends_on:
      - mongodb

  queue-api:
    image: "londotech/mycimencam-queue:1.0.0"
    restart: unless-stopped
    environment:
      - HOST=queue-api
      - NODE_ENV=production
    volumes:
      - /home/<USER>/production-v3/logs/queue:/usr/src/cimencam/logs
    entrypoint: npm run start:prod
    depends_on:
      - mongodb

  jde-api:
    image: "londotech/mycimencam-jde:1.0.0"
    restart: unless-stopped
    environment:
      - HOST=jde-api
      - NODE_ENV=production
    volumes:
      - /home/<USER>/production-v3/logs/jde:/usr/src/cimencam/logs
    entrypoint: npm run start:prod
    depends_on:
      - mongodb

  cron-api:
    image: "londotech/mycimencam-cron-v2:1.0.0"
    restart: unless-stopped
    environment:
      - HOST=cron-api
      - NODE_ENV=production
      - DB_MONGO_HOST=mongodb
    volumes:
      - /home/<USER>/production-v3/logs/cron:/usr/src/cimencam/logs
    entrypoint: npm run start:prod
    depends_on:
      - mongodb

  gotenberg:
    image: 'gotenberg/gotenberg:7'
    restart: unless-stopped
    
  nginx:
   image: 'londotech/cimencam-nginx-v3:latest'
   restart: unless-stopped
  #  ports:
  #    - '3003:443'
   depends_on:
     - website
     - api






















# version: "3"
# services:
#   mongodb:
#     image: 'mongo:5.0.12'
#     restart: unless-stopped
#     volumes:
#       - /home/<USER>/production-v3/mongo-volume:/data/db

#   backoffice:
#     image: 'londotech/mycimencam-backoffice-v3:1.0.1'
#     restart: unless-stopped
#     depends_on:
#       - api
#     ports:
#       - '3005:4000'

#   website:
#     image: 'londotech/mycimencam-website-v3:1.0.0'
#     restart: unless-stopped
#     depends_on:
#       - api
#     ports:
#       - '3006:4000'

#   api:
#     image: "londotech/mycimencam-api-v3:test-1.0.0"
#     restart: unless-stopped
#     environment:
#       - NODE_ENV=production
#       - HOST=api
#       - PORT=3000
#       - ORANGE_CHANNELUSER=691301143
#       - ORANGE_PIN=2222
#       - DB_V2_MONGO_HOST=mongodb
#       - DB_V2_MONGO_NAME=mycimencam
#       - DB_MYCIMENCAM_USERNAME_V2=logistic
#       - DB_MYCIMENCAM_PASSWORD_V2=_6FeRzrHG2wMVBTj
#       - MINIMAL_MOBILE_BUILD_VERSION=1.6.10
#       - FLUENTD_HOST=**************
#     volumes:
#       - /home/<USER>/production-v3/logs/api:/usr/src/cimencam/logs
#     entrypoint: npm run start:prod
#     #entrypoint: node src/main.js
#     depends_on:
#       - mongodb
#     ports:
#       - '3001:3000'