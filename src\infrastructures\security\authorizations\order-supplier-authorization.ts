import { OrderSupplierAction } from '@la-pasta-module/order-supplier/actions';
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';


class OrderSupplierAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      OrderSupplierAction.DELETE,
      OrderSupplierAction.CREATE,
      OrderSupplierAction.UPDATE,
      OrderSupplierAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == OrderSupplierAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const orderSupplierAuthorizationInstance = new OrderSupplierAuthorization();