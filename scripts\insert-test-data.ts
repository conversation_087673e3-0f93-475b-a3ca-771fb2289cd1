import readline from 'readline';
import { insertCompanies } from './company';
import { insertPackaging } from './packaging';
import { insertPriceOffers } from './price-offers';
import { insertProducts } from './products';
import { insertShippings } from './shippings';
import { insertStores } from './stores';
import { insertUnits } from './units';
import { insertUsers } from './users';
import { updateImagesPath } from './reload-balances';
import { InsertCompanyCommercial } from './commercials';
import { insertCategories } from './categories/categories.script';

(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point inséré des données de teste, ce processus effacera des collections existantes.')

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {
    if (answer == 'y') {
      await insertStores();

      await insertUnits();

      await insertPackaging();

      await insertCategories();

      // await updateImagesPath();

      await insertProducts();

      await insertShippings();

      await insertCompanies();

      await insertUsers();

      await InsertCompanyCommercial();

      await insertPriceOffers();
    }

    prompt.close();
  })

  prompt.on('close', () => {
    process.exit()
  })
})()