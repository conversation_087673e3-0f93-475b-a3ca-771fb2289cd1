import { HttpException, HttpStatus } from '@nestjs/common';
import { Order, Payment } from "../entities";

export class AfrilandPaymentEvent {
  order: { _id: string; appReference: string };
  paymentInfo: Payment & { afrilandKey: string };
  amount: number;

  constructor(order: Order) {
    this.order = {
      _id: order._id,
      appReference: order.appReference,
    };
    this.paymentInfo = {...order.payment, afrilandKey: this.getAfrilandKey(order)};
    this.amount = order.cart.amount.TTC;
  }

  private getAfrilandKey(order: Order): string {
    const afrilandKey = ('company' in order && order?.company?.afrilandKey) 
            ? order.company.afrilandKey 
            : order.user.afrilandKey;

    if (!afrilandKey || afrilandKey == '') {
      throw new HttpException("Cet utilisateur n'as pas de clé Afriland", HttpStatus.BAD_REQUEST);
    }

    return afriland<PERSON>ey;
  }
}