import { Module } from '@nestjs/common';
import { OrderRetaillService } from './order-retaill.service';
import { OrderRetaillController } from './order-retaill.controller';
import { OrderRetailRepository } from './repository/order-retaill.repository';
import { UsersModule } from '@la-pasta-module/users';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { ImagesModule } from '@la-pasta-module/images/images.module';
import { LoyaltyProgramRepository } from './repository/loyalty-program.repository';

@Module({
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
    UsersModule,
    ImagesModule
  ],
  controllers: [OrderRetaillController],
  providers: [OrderRetaillService, OrderRetailRepository, LoyaltyProgramRepository],
  exports: [OrderRetaillService, OrderRetailRepository],
})
export class OrderRetaillModule { }
