
import { Packaging, Product } from "@la-pasta-module/cart";
import { Category } from "@la-pasta-module/category/entities";
import { Company } from "@la-pasta-module/companies/entities";
import { OrderStatus } from "@la-pasta-module/order/entities";
import { Cart, CartItem } from "@la-pasta-module/price/compute_price/entities";
import { QrCodeData } from "@la-pasta-module/qr-code-management/entities/qr-code.entity";
import { Particular, User } from "@la-pasta-module/users";

export class OrderSupplier {
  _id?: string;
  cart: CartSupplier;
  user: Particular;
  status: OrderStatus;
  qrCodeData: QrCodeData[];
  appReference: string;
  supplier?: Partial<Company>;
  validation?: {
    user?: Partial<User>;
    date: number;
    raison: string;
  }
  created_at: number;
  dates?: {
    created?: number;
    paid?: number;
    validated?: number;
  }

}

export class CartSupplier {
    items: CartItemQRCode[];
}

export declare type CartItemQRCode = {
  product: Partial<Product>;
  quantity: number;
  category?: Partial<Category>;
  packaging?: Partial<Packaging>;
  quantityShipped?: number;
  code?: string;
};