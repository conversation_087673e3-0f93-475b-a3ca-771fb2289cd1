import { Store } from '@la-pasta-module/cart';
import { Company } from '@la-pasta-module/companies';
import { Points } from '@la-pasta-module/loyalty-program/entities';
import { ObjectId } from 'bson';

export class BaseUser {
  _id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  tel?: number;
  address?: Address;
  category?: UserCategory;
  roles?: string[];
  authorizations?: string[];
  profiles?: string;
  password?: string;
  profilePicture?: string
  created_at?: number;
  updateAt?: number;
  afrilandKey?: string;
  enable?: boolean;
  cni?: string;
  nui?: string;
  logo?: string;
  signature?: string;
  userOtp?: {
    value: number;
    expiredAt: number;
  };
  notifications?: any[];

  constructor() {
    this.address = {
      region: '',
      city: '',
      district: '',
    };
    this.userOtp = {
      value: 0,
      expiredAt: 0,
    };
  }
}

export class AuthUser extends BaseUser {
  accessToken: string;
}

export class EmployeeEntity extends BaseUser {
  isValidated?: boolean;
  isRetired?: boolean;
  direction?: string;
  service?: string;
  position?: string;
  employeeType: EmployeeType;
  matricule?: string;
  tonnage: Tonnage;
  store: Partial<Store>;
  associatedCompanies?: Partial<Company[]>
}

export class Retailer extends BaseUser {
  socialReason?: string;
  points?: number;
}

export class Particular extends BaseUser {
  points?: Points;
  defaultStore?: Store;
  associatedSuppliers?: Partial<Company[]>;
  associatedCommercial: Partial<EmployeeEntity>;
  socialReason?: string;
  profession?: string;
  rewardId?: string | ObjectId;
  referrals?: Referral[];
}

export class Referral {
  referralId: string;
  status: ReferralStatus;
  created_at: Date;
  updated_at?: Date;
}

export enum ReferralStatus {
  Pending = 100,
  Accepted = 200,
  Expired = 300,
}

export class CompanyEmployee extends BaseUser {
  company?: Company;
}

export enum UserCategory {
  Particular = 0,
  Retailer = 1,
  EmployeeEntity = 2,
  CompanyUser = 3,

  Commercial = 200,
  DonutAnimator = 250,
  Administrator = 300,
}

export const UserCategoryScripts = {
  [UserCategory.Particular]: 'particular',
  [UserCategory.Retailer]: 'retailer',
  [UserCategory.EmployeeEntity]: 'employeeentity',
  [UserCategory.CompanyUser]: 'companyuser',

  [UserCategory.Commercial]: 'commercial',
  [UserCategory.DonutAnimator]: 'donutanimator',
  [UserCategory.Administrator]: 'administrator',
};

export enum EmployeeType {
  NORMAL = 100,
  CORDO_RH = 101,
  DRH = 102,
}

export type User =
  | BaseUser
  | CompanyEmployee
  | EmployeeEntity
  | Retailer
  | Particular;
