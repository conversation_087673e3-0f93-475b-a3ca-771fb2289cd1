import { Module } from '@nestjs/common';
import { OrderSupplierService } from './order-supplier.service';
import { OrderSupplierController } from './order-supplier.controller';
import { OrderSupplierRepository } from './repository';
import { forwardRef } from '@nestjs/common';
import { NotificationsModule } from '@la-pasta-module/notifications/notifications.module';
import { LoyaltyProgramModule } from '@la-pasta-module/loyalty-program/loyalty-program.module';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';

@Module({

  providers: [OrderSupplierService, OrderSupplierRepository],
  exports: [OrderSupplierService, OrderSupplierRepository],
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
    forwardRef(() => NotificationsModule),
    forwardRef(() => LoyaltyProgramModule),
  ],
  controllers: [OrderSupplierController],

})
export class OrderSupplierModule { }
