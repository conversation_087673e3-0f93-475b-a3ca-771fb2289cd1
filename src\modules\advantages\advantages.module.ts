import { Module } from '@nestjs/common';
import { AdvantagesService } from './advantages.service';
import { AdvantagesController } from './advantages.controller';
import { AdvantageRepository } from './repository/advantage.repository';
import { LoyaltyRewardRepository } from './repository/loyalty-reward.repository';

@Module({
  controllers: [AdvantagesController],
  providers: [AdvantagesService, AdvantageRepository, LoyaltyRewardRepository],
  exports: [AdvantagesService, AdvantageRepository, LoyaltyRewardRepository]
})
export class AdvantagesModule { }
