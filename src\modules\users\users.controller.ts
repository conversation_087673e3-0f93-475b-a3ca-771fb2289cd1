import { User } from '@la-pasta-module/users';
import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBody, ApiHeader, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '@la-pasta-module/auth';
import { Permission } from '@la-pasta/infrastructures/security';
import { UsersService } from './users.service';
import { UserAction } from './actions';
import { CreateUserDto } from './dto';
import { getUser } from '@la-pasta/common';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  @ApiHeader({ name: 'Authorization', description: 'Bearer token to authenticate login user' })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({ status: 201, description: 'The user has been successfully created.' })
  @UseGuards(JwtGuard)
  @Post()
  async create(@Body() createUserDto: CreateUserDto) {
    Permission.userAuthorization(getUser(), UserAction.CREATE);
    return await this.usersService.createUser(createUserDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.userAuthorization(getUser(), UserAction.VIEW);
    return await this.usersService.findAll({ filter: query, projection: { password: 0 } });
  }

  @UseGuards(JwtGuard)
  @Get('filters-elements')
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.usersService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @UseGuards(JwtGuard)
  @Get('particular-users')
  async getParticularUserByCommercialId(@Query() query: QueryFilter) {
    Permission.userAuthorization(getUser(), UserAction.VIEW);
    return await this.usersService.getParticularUserByCommercialId({ filter: query});
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.userAuthorization(getUser(), UserAction.VIEW, { _id: id });
    return await this.usersService.findOne({ filter: { _id: id }, projection: { password: 0 } });
  }

  @UseGuards(JwtGuard)
  @Patch(':id/reset-password')
  async resetUserPassword(@Param('id') id: string, @Body('password') password: string) {
    Permission.userAuthorization(getUser(), UserAction.CHANGE_PASSWORD);
    return this.usersService.resetPassword(id, password);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateUserDto: unknown) {
    Permission.userAuthorization(getUser(), UserAction.UPDATE, { _id: id });
    return await this.usersService.updateUser(id, updateUserDto);
  }

  @UseGuards(JwtGuard)
  @Patch('validate/:id')
  async validateUser(@Param('id') id: string, @Body() data: any) {
    Permission.userAuthorization(getUser(), UserAction.VALIDATE_USER, { _id: id });
    return this.usersService.update({ _id: id }, data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id/migrate-user')
  async moveUser(@Param('id') id: string, @Body() updateUserDto: { companyId: string }) {
    Permission.userAuthorization(getUser(), UserAction.UPDATE, { _id: id });
    return await this.usersService.migrateUserToOtherCompany(id, updateUserDto.companyId);
  }
}
