import { UpdatePlanificationDto } from './dto/update-planification.dto';
import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { PlanificationService } from './planification.service';
import { CreatePlanificationDto } from './dto/create-planification.dto';
import { JwtGuard } from '@la-pasta-module/auth';
import { Permission } from '@la-pasta/infrastructures/security';
import { getUser } from '@la-pasta/common';
import { PlanificationAction } from './actions';
import { OrderAction } from '@la-pasta-module/order';

@Controller('planifications')
export class PlanificationController {
  constructor(private readonly planificationService: PlanificationService) { }

  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createPlanificationDto: CreatePlanificationDto) {
    Permission.planificationAuthorization(getUser(), PlanificationAction.CREATE);

    return this.planificationService.create(createPlanificationDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  findAll(@Query() query?: QueryFilter) {
    Permission.planificationAuthorization(getUser(), PlanificationAction.VIEW);

    return this.planificationService.findAll({ filter: query });
  }

  @UseGuards(JwtGuard)
  @Get('custom')
  findCustomPlanifications(@Query() query: QueryFilter) {
    Permission.orderAuthorization(getUser(), OrderAction.PLAN);

    return this.planificationService.getCustomPlanifications({ filter: query });
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  upadte(@Param() id: string, @Body() updatePlanificationDto: UpdatePlanificationDto) {
    Permission.planificationAuthorization(getUser(), PlanificationAction.UPDATE);

    return this.planificationService.update({ _id: id }, updatePlanificationDto);
  }
}
