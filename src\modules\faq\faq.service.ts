import { HttpStatus, Injectable } from '@nestjs/common';
import { CreateFaqDto } from './dto/create-faq.dto';
import { UpdateFaqDto } from './dto/update-faq.dto';
import { BaseService, setResponse, t } from '@la-pasta/common';
import moment from 'moment';
import { FaqRepository } from './repository';

@Injectable()
export class FaqService extends BaseService {


  constructor(private readonly faqRepository: FaqRepository,


  ) {
    super(faqRepository)

  }
  async createFaq(createFaqDto: CreateFaqDto) {
    const res = await this.create(createFaqDto);

    return setResponse(HttpStatus.CREATED, t('CREATED'), await res.data);

  }


  async findAllFaq(query: QueryOptions) {
    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }

    return await this.findAll(query);
  }

  findOneFaq(id: string) {
    return `This action returns a #${id} faq`;
  }

  async updateFaq(id: string, updateFaqDto: UpdateFaqDto) {

    const res = await this.update(
      { _id: id },
      updateFaqDto
    );

    return res;
  }

  remove(id: number) {
    return `This action removes a #${id} faq`;
  }



  private setDateFilter(query: QueryFilter) {
    query['created_at'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }
}
