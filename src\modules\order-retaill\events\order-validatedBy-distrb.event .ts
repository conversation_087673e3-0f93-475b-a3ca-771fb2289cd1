import moment from 'moment';
import { CreateOrderRetaillDto } from '../dto';
import { CartItemReseller } from '../entities';

export class OrderValidatedByDistrbEvent {
  client_name: string;
  order_retrievement_point: string;
  order_retrievement_city: string;
  order_ref: string;
  order_createAt: string;
  order_total_point: number;
  email: string;
  order_products: { item: string; quantity: number }[];

  constructor(
    order: CreateOrderRetaillDto,
    user: { fullName: string; email: string },
  ) {
    this.order_retrievement_point = order?.distributors?.name;
    this.order_retrievement_city = order?.distributors?.address?.city;
    this.order_ref = order?.appRef;
    this.order_createAt = moment().format('DD-MM-YYYY');
    this.order_total_point = order?.points?.ordered;
    this.order_products = this.formatItems(
      order?.items,
      order?.packaging?.unit?.value,
    );
    this.client_name = user?.fullName;
    this.email = user?.email;
  }

  private formatItems(cart: CartItemReseller[], unit: number) {
    return cart?.map((item) => {
      return {
        item: item?.product?.label,
        quantity: (item?.quantity * unit) / 1000,
      };
    });
  }

}
