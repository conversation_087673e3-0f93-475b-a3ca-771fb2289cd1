import { <PERSON>, Get, Post, Body, Patch, Param, Query } from '@nestjs/common';
import { RenderTypesService } from './render-types.service';
import { CreateRenderTypeDto } from './dto/create-render-type.dto';
import { UpdateRenderTypeDto } from './dto/update-render-type.dto';
import { setResponseController } from 'src/common/helpers';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Render Types')
@Controller('render-types')
export class RenderTypesController {
  constructor(private readonly renderTypesService: RenderTypesService) {}

  @Post()
  async create(@Body() createRenderTypeDto: CreateRenderTypeDto) {
    return await this.renderTypesService.create(createRenderTypeDto);
  }

  @Get()
  async findAll(@Query() query: QueryFilter) {
    const data = this.renderTypesService.findAll({ filter: query });
    return setResponseController(data);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.renderTypesService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateRenderTypeDto: UpdateRenderTypeDto) {
    const data = await this.renderTypesService.update({ _id: id }, updateRenderTypeDto);
    return setResponseController(data);
  }
}
