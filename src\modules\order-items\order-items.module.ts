import { LoyaltyProgramModule } from '@la-pasta-module/loyalty-program/loyalty-program.module';
import { NotificationsModule } from '@la-pasta-module/notifications/notifications.module';
import { OrderItemsController } from './order-items.controller';
import { OrderItemsService } from './order-items.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { UsersModule } from '@la-pasta-module/users';
import { forwardRef, Module } from '@nestjs/common';
import { OrderItemRepository } from './respository';
import { config } from 'convict-config';

@Module({
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
    forwardRef(() => NotificationsModule),
    forwardRef(() => LoyaltyProgramModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [OrderItemsController],
  providers: [OrderItemsService, OrderItemRepository]
})
export class OrderItemsModule { }
