import { Injectable } from '@nestjs/common';
import { PromoCodeRepository } from './repository/promo-code.repository';
import { BaseService } from '@la-pasta/common';
import moment from 'moment';
import _ from 'lodash';
import { Cart } from '@la-pasta-module/price/compute_price';
import { CreatePromoCodeDto } from './dto/create-promo-code.dto';
import { UpdatePromoCodeDto } from './dto/update-promo-code.dto';
import { PromoCode } from './entities';
import { PromoCodeValidation } from './validations';

@Injectable()
export class PromoCodeService extends BaseService {

  constructor(
    private readonly promoCodeRepository: PromoCodeRepository,
    private readonly promoCodeValidation: PromoCodeValidation,
  ) {
    super(promoCodeRepository);
  }

  async insertPromoCode(code: CreatePromoCodeDto) {
    try {
      return await this.create(code);
    } catch (error) {
      this.logger.error(` Insert promo code failed. \n${error.message} \n ${error.stack}`);
      return error;
    }
  }

  async applyPromoCode(reference: string, cart: Partial<Cart>) {
    try {
      const codePromo = await this.getPromoCodeByReference(reference);

      this.promoCodeValidation.validate(codePromo, cart);

      return this.computeDiscount(codePromo, cart);
    } catch (error) {
      this.logger.error(` An error occur during the processing of the promo code. \n${error.message} \n ${error.stack}`);
      return error;
    }
  }

  private async getPromoCodeByReference(reference: string): Promise<PromoCode> {
    return await this.promoCodeRepository.findOne({ filter: { reference } }) as unknown as PromoCode;
  }

  async computeDiscount(codePromo: PromoCode, cart: Partial<Cart>) {
    const appliedOnCalculations = {
      'Globale': this.calculateGlobalDiscount,
      'Produit': this.calculateProductDiscount,
    };

    return appliedOnCalculations[codePromo.discount.appliedOn](codePromo, cart);
  }

  private calculateGlobalDiscount(codePromo: PromoCode, cart: Partial<Cart>): number {
    const { type, value } = codePromo?.discount;
    return type === 1 ? cart?.amount?.TTC * (value / 100) : value;
  }

  private calculateProductDiscount(codePromo: PromoCode, cart: Partial<Cart>): number {
    const discountTypeCalculations = {
      0: (value: number, quantity: number, unitPrice: number) => quantity * value,
      1: (value: number, quantity: number, unitPrice: number) => (quantity * unitPrice) * (value / 100)
    };

    const { type, value } = codePromo?.discount;
    const items = cart?.items?.filter(item => codePromo?.products?.findIndex(product => product?._id === item?.product?._id) >= 0);
    return items.reduce((total, { quantity, unitPrice }) => total + discountTypeCalculations[type](value, quantity, unitPrice), 0);
  }

  async updatePromoCode(id: string, code: UpdatePromoCodeDto) {
    try {
      code.enable ? (
        code.dates.start = moment(Date.parse(`${code?.dates?.start}`)).startOf('day').valueOf(),
        code.dates.end = moment(Date.parse(`${code?.dates?.end}`)).endOf('day').valueOf()
      ) : null;
      return await this.update({ _id: id }, code);
    } catch (error) {
      this.logger.error(` update promo code failed. \n${error.message} \n ${error.stack}`);
      return error;
    }
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

}
