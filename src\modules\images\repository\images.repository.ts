import { DatabaseMongoDB } from "@la-pasta/database/database-mongodb-images";
import { Injectable } from "@nestjs/common";
import { Collection, InsertOneResult, WithId, UpdateResult, Document, ObjectId } from "mongodb";

@Injectable()
export class ImageRepository {
    public database: DatabaseMongoDB;

    constructor() {
        this.database = DatabaseMongoDB.getInstance();
    }

    async create(document: Document): Promise<InsertOneResult<Document>> {
        return (await this.getCollection()).insertOne(document);
    }

    async findAll(query?: QueryOptions): Promise<Document[]> {
        return (await this.getCollection()).find(query?.filter ?? {})
            .project(query?.projection ?? {})
            .sort(query?.sort ?? '_id', query?.way ?? -1)
            .skip(query?.offset ?? 0)
            .limit(query?.limit ?? 0)
            .toArray();
    }

    async findOne(query: QueryOptions): Promise<WithId<Document>> {
        this.setMongoId(query.filter);

        return (await this.getCollection())
            .findOne(query.filter, { projection: query.projection ?? {} });
    }

    protected async getCollection(): Promise<Collection<Document>> {
        return (await this.database.getDatabase()).collection('images');
    }

    async update(appRef: string, updateImage: { dataUrls: string[] }): Promise<UpdateResult> {
        return (await this.getCollection()).updateOne(
            { appRef: appRef },
            { $addToSet: { "dataUrls": { $each: updateImage.dataUrls } } }
        );
    }

    private setMongoId(filter: QueryFilter): void {
        if ('_id' in filter) { filter._id = new ObjectId(filter._id) }
    }
}