import { Test, TestingModule } from '@nestjs/testing';
import { QrCodeFileManagementController } from './qr-code-file-management.controller';
import { QrCodeFileManagementService } from './qr-code-file-management.service';

describe('QrCodeFileManagementController', () => {
  let controller: QrCodeFileManagementController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QrCodeFileManagementController],
      providers: [QrCodeFileManagementService],
    }).compile();

    controller = module.get<QrCodeFileManagementController>(QrCodeFileManagementController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
