import {
  ErpItemIdAction,
  PackagingAction,
  ProductAction,
  StoreAction,
  UnitAction,
} from '@la-pasta-module/cart';
import { CategoryAction } from '@la-pasta-module/category/actions';
import { CompanyAction, CompanyCategory } from '@la-pasta-module/companies';
import { FaqAction } from '@la-pasta-module/faq/actions';
import { FeedbackAction } from '@la-pasta-module/feedbacks/actions/feedback.action';
import { LocationAction } from '@la-pasta-module/locations/actions';
import { OrderAction, PaymentAction } from '@la-pasta-module/order';
import { PlanificationAction } from '@la-pasta-module/planification';
import { PriceAction } from '@la-pasta-module/price';
import { ShippingAction } from '@la-pasta-module/price/shipping';
import { TechnicalSheetAction } from '@la-pasta-module/technical-sheet/actions';
import { UserCategory } from '@la-pasta-module/users';
import { UserAction } from '@la-pasta-module/users/actions/user.action';
import { MongoDatabase } from '@la-pasta/database';
import readline from 'readline';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { BalanceAction } from '@la-pasta-module/balance/actions';
import { OrderItemsAction } from '@la-pasta-module/order-items/actions';
import { ItemsAction } from '@la-pasta-module/items/actions';
import { OrderSupplierAction } from '@la-pasta-module/order-supplier/actions';
import { ScannerDataAction } from '@la-pasta-module/scanner-data/actions';
import { loyaltyProgramAction } from '@la-pasta-module/loyalty-program/actions';
import { WholeSaleAction } from '@la-pasta/whole-sale/action';
import { QrCodeAction } from '@la-pasta-module/qr-code-management/actions';

const database = MongoDatabase.getInstance();
const collectionName = 'profiles';

const authorizations = [
  {
    label: CompanyCategory[CompanyCategory.Baker].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      UserAction.UPDATE,
      UserAction.CHANGE_PASSWORD,
      UserAction.VIEW,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      PackagingAction.VIEW,
      OrderAction.INIT,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      FaqAction.VIEW,
      StoreAction.VIEW,
      OrderAction.UPDATE,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      PlanificationAction.CUSTOM,
      PaymentAction.MY_ACCOUNT,
      OrderAction.VIEW_STORE,
      BalanceAction.VIEW,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.LOW_BALANCE,
      // PaymentAction.M2U,
      // ReloadBalanceAction.CAN_RELOAD,
      // ReloadBalanceAction.VIEW_RELOAD_HISTORY,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      ItemsAction.VIEW,
      CategoryAction.VIEW

    ],
  },
  {
    label: CompanyCategory[CompanyCategory.Group].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      UserAction.UPDATE,
      UserAction.CHANGE_PASSWORD,
      UserAction.VIEW,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      PackagingAction.VIEW,
      OrderAction.INIT,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      FaqAction.VIEW,
      StoreAction.VIEW,
      OrderAction.UPDATE,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      PlanificationAction.CUSTOM,
      PaymentAction.MY_ACCOUNT,
      OrderAction.VIEW_STORE,
      BalanceAction.VIEW,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.LOW_BALANCE,
      // PaymentAction.M2U,
      // ReloadBalanceAction.CAN_RELOAD,
      // ReloadBalanceAction.VIEW_RELOAD_HISTORY,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      ItemsAction.VIEW,
      CategoryAction.VIEW
    ],
  },
  {
    label: CompanyCategory[CompanyCategory.EXPORT].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      UserAction.UPDATE,
      UserAction.CHANGE_PASSWORD,
      UserAction.VIEW,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      PackagingAction.VIEW,
      OrderAction.INIT,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      FaqAction.VIEW,
      StoreAction.VIEW,
      OrderAction.UPDATE,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      PlanificationAction.CUSTOM,
      PaymentAction.MY_ACCOUNT,
      OrderAction.VIEW_STORE,
      BalanceAction.VIEW,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.LOW_BALANCE,
      // PaymentAction.M2U,
      // ReloadBalanceAction.CAN_RELOAD,
      // ReloadBalanceAction.VIEW_RELOAD_HISTORY,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      ItemsAction.VIEW,
      CategoryAction.VIEW
    ],
  },
  {
    label: CompanyCategory[CompanyCategory.Industry].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      UserAction.UPDATE,
      UserAction.CHANGE_PASSWORD,
      UserAction.VIEW,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      PackagingAction.VIEW,
      OrderAction.INIT,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      FaqAction.VIEW,
      StoreAction.VIEW,
      OrderAction.UPDATE,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      PlanificationAction.CUSTOM,
      PaymentAction.MY_ACCOUNT,
      OrderAction.VIEW_STORE,
      BalanceAction.VIEW,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.LOW_BALANCE,
      // PaymentAction.M2U,
      // ReloadBalanceAction.CAN_RELOAD,
      // ReloadBalanceAction.VIEW_RELOAD_HISTORY,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      ItemsAction.VIEW,
      CategoryAction.VIEW

    ],
  },
  {
    label: CompanyCategory[CompanyCategory.GMS].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      UserAction.UPDATE,
      UserAction.CHANGE_PASSWORD,
      UserAction.VIEW,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      PackagingAction.VIEW,
      OrderAction.INIT,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW,
      OrderAction.UPDATE,
      FaqAction.VIEW,
      StoreAction.VIEW,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      PlanificationAction.CUSTOM,
      PaymentAction.MY_ACCOUNT,
      OrderAction.VIEW_STORE,
      BalanceAction.VIEW,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.LOW_BALANCE,
      // PaymentAction.M2U,
      // ReloadBalanceAction.CAN_RELOAD,
      // ReloadBalanceAction.VIEW_RELOAD_HISTORY,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      ItemsAction.VIEW,
      CategoryAction.VIEW
    ],
  },
  {
    label: CompanyCategory[CompanyCategory.WholeSaler].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      UserAction.UPDATE,
      UserAction.CHANGE_PASSWORD,
      UserAction.VIEW,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      PackagingAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      OrderAction.INIT,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW_STORE,
      OrderAction.UPDATE,
      OrderAction.VIEW,
      FaqAction.VIEW,
      StoreAction.VIEW,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      OrderAction.UPDATE,
      PlanificationAction.CUSTOM,
      PaymentAction.MY_ACCOUNT,
      BalanceAction.VIEW,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.LOW_BALANCE,
      // PaymentAction.M2U,
      // ReloadBalanceAction.CAN_RELOAD,
      // ReloadBalanceAction.VIEW_RELOAD_HISTORY,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      ItemsAction.VIEW,
      CategoryAction.VIEW

    ],
  },
  {
    label: UserCategory[UserCategory.Particular].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      UserAction.UPDATE,
      UserAction.CHANGE_PASSWORD,
      UserAction.VIEW,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      PackagingAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW_STORE,
      OrderAction.UPDATE,
      OrderAction.VIEW,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.INIT,
      OrderAction.VIEW,
      FaqAction.VIEW,
      StoreAction.VIEW,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      OrderSupplierAction.CREATE,
      OrderSupplierAction.VIEW,
      PlanificationAction.CUSTOM,
      PaymentAction.MY_ACCOUNT,
      PaymentAction.CREDIT,
      ScannerDataAction.CREATE,
      ScannerDataAction.UPDATE,
      // BalanceAction.VIEW,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.M2U,
      TechnicalSheetAction.VIEW,
      TechnicalSheetAction.CREATE,
      TechnicalSheetAction.UPDATE,
      TechnicalSheetAction.DELETE,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      ItemsAction.VIEW,
      CategoryAction.VIEW,
      loyaltyProgramAction.VIEW,
    ],
  },
  {
    label: UserCategory[UserCategory.EmployeeEntity].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      CompanyAction.VIEW,
      UserAction.UPDATE,
      UserAction.VIEW,
      LocationAction.VIEW,
      OrderAction.INIT,
      OrderAction.PLAN,
      FaqAction.VIEW,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      OrderAction.CREATE,
      OrderAction.UPDATE,
      PaymentAction.CREDIT,
      PriceAction.VIEW,
      StoreAction.VIEW,
      UnitAction.VIEW,
      ShippingAction.VIEW,
      PlanificationAction.CUSTOM,
      // PaymentAction.ORANGE_MONEY,
      // PaymentAction.MOBILE_MONEY,
      // PaymentAction.VISA,
      // PaymentAction.AFRILAND,
      // PaymentAction.M2U,
      TechnicalSheetAction.VIEW

    ],
  },
  {
    label: UserCategory[UserCategory.Retailer].toLowerCase(),
    authorizations: [
      ProductAction.VIEW,
      CompanyAction.VIEW,
      PackagingAction.VIEW,
      UserAction.UPDATE,
      UserAction.VIEW,
      FaqAction.VIEW,
      OrderAction.INIT,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      OrderAction.PLAN,
      OrderAction.CREATE,
      OrderAction.VIEW,
      TechnicalSheetAction.VIEW
    ],

  },
  {
    label: UserCategory[UserCategory.Commercial].toLowerCase(),
    authorizations: [
      OrderAction.VIEW_EMPLOYEES,
      OrderAction.VIEW_CUSTOMERS,
      OrderAction.VIEW,
      OrderAction.VALIDATE,
      CompanyAction.VIEW,
      FaqAction.VIEW,
      FaqAction.CREATE,
      FaqAction.UPDATE,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      UserAction.UPDATE,
      UserAction.VIEW,
      UserAction.CREATE,

      CompanyAction.VIEW_USERS,
      OrderAction.COMMERCIAL_VALIDATION,
      OrderAction.UPDATE,
      // OrderAction.VALIDATE_LOW_BALANCE,
      ProductAction.VIEW,
      LocationAction.CREATE,
      LocationAction.DELETE,
      LocationAction.UPDATE,
      LocationAction.VIEW,
      ShippingAction.VIEW,
      TechnicalSheetAction.CREATE,
      TechnicalSheetAction.DELETE,
      TechnicalSheetAction.UPDATE,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      OrderItemsAction.UPDATE,
      OrderItemsAction.DELETE,
      ItemsAction.VIEW,
      CategoryAction.VIEW
    ]
  },
  {
    label: UserCategory[UserCategory.DonutAnimator].toLowerCase(),
    authorizations: [
      OrderAction.VIEW_CUSTOMERS,
      OrderAction.VALIDATE,
      FaqAction.VIEW,
      FaqAction.CREATE,
      FaqAction.UPDATE,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      UserAction.UPDATE,
      UserAction.VIEW,
      UserAction.CREATE,
      UserAction.VALIDATE_USER,
      WholeSaleAction.CREATE,
      WholeSaleAction.VIEW,
      WholeSaleAction.UPDATE,
      WholeSaleAction.DELETE,

      // OrderAction.VALIDATE_LOW_BALANCE,
      ProductAction.VIEW,
      LocationAction.CREATE,
      LocationAction.DELETE,
      LocationAction.UPDATE,
      LocationAction.VIEW,
      ShippingAction.VIEW,
      TechnicalSheetAction.CREATE,
      TechnicalSheetAction.DELETE,
      TechnicalSheetAction.UPDATE,
      TechnicalSheetAction.VIEW,
      OrderItemsAction.VIEW,
      OrderItemsAction.CREATE,
      OrderItemsAction.UPDATE,
      OrderItemsAction.DELETE,
      ItemsAction.VIEW,
      CategoryAction.VIEW,
      QrCodeAction.VIEW,
      CompanyAction.VIEW,
    ]
  },
  {
    label: UserCategory[UserCategory.Administrator].toLowerCase(),
    authorizations: [
      UserAction.CREATE,
      UserAction.VIEW,
      UserAction.DELETE,
      UserAction.CHANGE_PASSWORD,
      UserAction.UPDATE,
      FeedbackAction.CREATE,
      FeedbackAction.VIEW,
      OrderAction.VIEW,
      OrderAction.DELETE,
      OrderAction.CANCEL,
      OrderAction.UPDATE,
      OrderAction.VALIDATE,
      FaqAction.VIEW,
      FaqAction.CREATE,
      FaqAction.UPDATE,
      BalanceAction.VIEW,
      BalanceAction.UPDATE,
      BalanceAction.CREATE,
      // OrderAction.VALIDATE_LOW_BALANCE,
      OrderAction.ADMIN_VALIDATION,
      OrderAction.VIEW_CUSTOMERS,
      // OrderAction.VIEW_EMPLOYEES,
      PriceAction.CREATE,
      PriceAction.DELETE,
      PriceAction.UPDATE,
      PriceAction.VIEW,
      LocationAction.CREATE,
      LocationAction.DELETE,
      LocationAction.UPDATE,
      LocationAction.VIEW,
      // PlanificationAction.CREATE,
      // PlanificationAction.DELETE,
      // PlanificationAction.UPDATE,
      // PlanificationAction.VIEW,
      CompanyAction.CREATE,
      CompanyAction.DELETE,
      CompanyAction.UPDATE,
      CompanyAction.VIEW,
      CompanyAction.VIEW_USERS,
      CompanyAction.ADD_USER,
      PackagingAction.CREATE,
      PackagingAction.DELETE,
      PackagingAction.UPDATE,
      PackagingAction.VIEW,
      ProductAction.CREATE,
      ProductAction.DELETE,
      ProductAction.UPDATE,
      ProductAction.VIEW,
      CategoryAction.CREATE,
      CategoryAction.VIEW,
      CategoryAction.UPDATE,
      CategoryAction.DELETE,
      StoreAction.CREATE,
      StoreAction.DELETE,
      StoreAction.UPDATE,
      StoreAction.VIEW,
      UnitAction.CREATE,
      UnitAction.DELETE,
      UnitAction.UPDATE,
      UnitAction.VIEW,
      ShippingAction.CREATE,
      ShippingAction.VIEW,
      ShippingAction.UPDATE,
      ShippingAction.DELETE,
      // ErpItemIdAction.CREATE,
      // ErpItemIdAction.VIEW,
      ErpItemIdAction.UPDATE,
      ErpItemIdAction.DELETE,
      TechnicalSheetAction.CREATE,
      TechnicalSheetAction.DELETE,
      TechnicalSheetAction.UPDATE,
      TechnicalSheetAction.VIEW,
      FaqAction.CREATE,
      FaqAction.VIEW,
      FaqAction.UPDATE,
      FaqAction.DELETE,
      ItemsAction.CREATE,
      ItemsAction.UPDATE,
      ItemsAction.DELETE,
      ItemsAction.VIEW,
      OrderItemsAction.VIEW,
      OrderAction.DELETE,
      OrderItemsAction.UPDATE,
      // FeedbackAction.CREATE,

      // FeedbackAction.VIEW,
      // FeedbackAction.DELETE,
      // FeedbackAction.UPDATE,
    ],

  },
];

(async () => {
  // console.log('* Les autorisations suivantes seront insérer dans la collection profile:')

  // console.table(authorizations.map(authorization => {
  //   return {
  //     profile: authorization.label,
  //     authorizations: authorization.authorizations.join(', ')
  //   }
  // }));

  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  prompt.question(
    "Voulez-vous procéder à l'insertion des profiles (y/n) ? : ",
    async (answer) => {
      if (answer == 'y') {
        await dropCollection(collectionName);

        setLoader('Insertion des profiles\n');
        const insertedProfiles = await (await database.getDatabase())
          .collection(collectionName)
          .insertMany(authorizations);
        stopLoader(insertedProfiles);
      }

      prompt.close();
    },
  );

  prompt.on('close', () => {
    process.exit();
  });
})();
