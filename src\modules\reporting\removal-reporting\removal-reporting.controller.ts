import { Controller, Get, Query } from '@nestjs/common';
import { RemovalReportingService } from './removal-reporting.service';

@Controller('removal-reporting')
export class RemovalReportingController {
    constructor(public reportingSrv: RemovalReportingService) {}

    @Get('aes-repartition')
    async getRepartitionAes(@Query() query: QueryFilter) {
         return await this.reportingSrv.getAeRepartition(query);
    }

    @Get('aes-evolution')
    async getEvolutionAes(@Query() query: QueryFilter) {
         return await this.reportingSrv.getEvolution(query);
    }
    
    @Get('aes-product')
    async getProductAes(@Query() query: QueryFilter) {
         return await this.reportingSrv.getProductInAe(query);
    }
}
