import { Module } from '@nestjs/common';
import { RetrievementsService } from './retrievements.service';
import { RetrievementsController } from './retrievements.controller';
import { RetrievementRepository } from './repository/retrievements.repository';

@Module({
  controllers: [RetrievementsController],
  providers: [RetrievementsService,RetrievementRepository],
  exports: [RetrievementsService, RetrievementRepository]

})
export class RetrievementsModule {}
