import { Injectable, NestMiddleware, Logger } from "@nestjs/common";
import { config } from "convict-config";
import { NextFunction, Request, Response } from "express";
import oauthToken from 'oauth-token';


@Injectable()
export class EexAuthMiddleware implements NestMiddleware {
    public logger = new Logger(EexAuthMiddleware.name);

    async use(req: Request, res: Response, next: NextFunction) {
        const authorization = req.headers.authorization;

        if (!authorization || !authorization.includes(' ') || authorization.split(' ').length < 2) {
            return res.status(400).json({ message: 'missing or bad formatted token' });
        }

        const accessToken = authorization.split(' ')[1];

        try {
            const options = { salt: config.get('oauthSalt'), ttl: config.get('oauthTTL') };
            const OauthToken = oauthToken(options);
            const oauthData = await OauthToken.decode(accessToken);
            const user = JSON.parse(oauthData.userId);
            req.user = user;
            next();
        } catch (error) {
            if (error.message === 'Error validating token: Token has been expired.') {
                this.logger.error(`Token expired.`, `middleware.auth.oauthVerification()`, {});
                return res.status(401).json({ type: 'TokenExpired' });
            }
            this.logger.error(
                `Invalid token.`,
                `middleware.auth.oauthVerification()`,
                error
            );
            res.status(401).json({ message: 'invalid token.' });
        }
    }

}