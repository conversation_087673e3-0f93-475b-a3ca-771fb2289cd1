import { Order, PaymentMode } from "@la-pasta-module/order";
import { ReloadBalance } from "@la-pasta-module/reload-balance/entities/reload-balance.entity";

export class Payment {
  _id: string;
  mode: {
    id: PaymentMode;
    label: string;
  };
  clientOption?: {
    id: number;
    label: string;
  };
  data?: {
    reference: string;
    amount: number;
    bank: {
      id: number;
      label: string;
    }
  };
  order?: Partial<Order>;
  reloadBalance?: Partial<ReloadBalance>;
  tel: number;
  processingNumber?: string | number;
  transactionId?: string;
  isQ1: boolean;
  status: string;
  nbrTries?: number;
  paymentResponse?: any;
}
