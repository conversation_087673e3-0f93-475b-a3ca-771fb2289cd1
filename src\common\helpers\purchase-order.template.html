<!DOCTYPE html>
<!--
  Invoice template by invoicebus.com
  To customize this template consider following this guide https://invoicebus.com/how-to-create-invoice-template/
  This template is under Invoicebus Template License, see https://invoicebus.com/templates/license/
-->
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>BON DE COMMANDE {{client_name}} du </title>

  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <meta name="description" content="Invoicebus Invoice Template">
  <meta name="author" content="Invoicebus">

  <meta name="template-hash" content="24a3668887450eb7e74d5fbfcc9ea9f8">

  <!-- <link rel="stylesheet" href="templates/template.css"> -->
</head>

<body>
  <div id="container">
    <section id="memo">
      {{#if client_has_logo}}
      <div class="logo">
        <img src="{{client_logo}}" height="80%" />
      </div>
      {{/if}}

      <div class="company-name">
        <div>{{client_name}}</div>
        <div class="company-details">
          <div>
            <span>{{client_rccm}}</span>
          </div>
          <div>
            <span>CNI : {{client_cni}}</span>
          </div>
          <div>
            <span>NIU : {{client_niu}}</span>
          </div>
          <div>
            <span>{{client_address}}</span>
          </div>
          <div>
            <span>{{client_pobox}}</span>
          </div>
          <div>
            <span>Tel : {{client_contact}}</span>
          </div>
          {{#if tel_receiver}}<div>
            {{tel_receiver_label}}: <span>{{tel_receiver}}</span>
          </div>{{/if}}
          <div>
            <h2>Code Client : {{client_code}}</h2>
          </div>
        </div>
      </div>

      <div class="payment-info">
        <div>N°{{order_ref}}</div>
        <div>{{payment_mode}}</div>
        <div>{{order_retrievement_point}}</div>
        <div>{{payment_info4}}</div>
        <div>{{payment_info5}}</div>
        <div>{{payment_info6}}</div>
        <div>{{payment_info7}}</div>
        <div>{{payment_info8}}</div>
      </div>

    </section>

    <section id="invoice-title-number">

      <span id="title">{{invoice_title}} </span>
      <span id="number">{{order_ref}}</span>

    </section>

    <div class="clearfix"></div>

    <section id="client-info">
      <span>{{bill_to_label}}</span>
      <div>
        <span class="client-name">{{provider_name}}</span>
      </div>

      <div>
        <span>{{provider_rccm}}</span>
      </div>

      <div>
        <span>{{provider_niu}}</span>
      </div>

      <!-- <div>
        <span>{{provider_address}}</span>
      </div> -->

      <div>
        <span>{{provider_pobox}}</span>
      </div>

      <div>
        <span>{{provider_tel}}</span>
      </div>

      <!-- <div>
        <span>{{provider_fax}}</span>
      </div>

      <div>
        <span>{{provider_email}}</span>
      </div> -->
    </section>

    <section id="invoice-info">

      <div class="box-left">
        <div>
          <span>{{issue_date_label}}</span>
          {{#if due_date}}<span>{{due_date_label}}</span>{{/if}}
        </div>

        <div>
          <span>{{issue_date}}</span>
          {{#if due_date}}<span>{{due_date }}</span>{{/if}}
        </div>
      </div>

      <div class="box-right">
        <div>
          <span>{{amount_total_label}}</span>
          <span>{{currency_label}}</span>
          <!-- <span class="hidden">{{po_number_label}}</span> -->
        </div>

        <div>
          <span>{{amount_total}}</span>
          <span>{{currency}}</span>
          <!-- <span class="hidden">{po_number}</span> -->
        </div>
      </div>
    </section>

    <div class="clearfix"></div>

    <div class="{{class_size_class}}">
      <section id="items">

        <table cellpadding="0" cellspacing="0">

          <tr>
            <th>{{item_row_number_label}}</th> <!-- Dummy cell for the row number and row commands -->
            <th>{{item_description_label}}</th>
            <th>{{item_quantity_bags_label}}</th>
            <!-- <th>{{item_quantity_convert_label}}</th> -->
            {{#if not_company_category_baker}}
            <th>{{item_price_label}}</th>
            <th>{{item_line_total_label}}</th>
            {{/if}}
          </tr>

          {{#each items}}
          <tr data-iterate="item">
            <td>{{item_row_number}}</td>
            <!-- Don't remove this column as it's needed for the row commands -->
            <td>{{item_description }} {{item_quantity_tones}}</td>
            <td>{{item_quantity_bags}}</td>
            <!-- <td>{{item_quantity_tones}}</td> -->
            {{#if ../not_company_category_baker}}
            <td>{{item_price}}</td>
            <td>{{item_line_total}}</td>
            {{/if}}
          </tr>
          {{/each}}

        </table>

      </section>

      <section id="sums">

        <table cellpadding="0" cellspacing="0">

          {{#if not_company_category_baker}}<tr>
            <th>{{point_of_sale_label}}
            <td>{{point_of_sale}}</td>
          </tr>

          <tr>
            <th>{{delivery_address_label}}
            <td>{{delivery_address}}</td>
          </tr>

          <tr>
            <th>
              {{amount_subtotal_label}} <span class="label-details">(hors taxes)</span></th>
            <td>{{amount_subtotal}}</td>
          </tr>

          <tr>
            <th>{{amount_transport_label}}</th>
            <td>{{amount_transport}}</td>
          </tr>

          <tr>
            <th>{{amount_ht_label}} </th>
            <td>{{amount_ht}}</td>
          </tr>

          <tr>
            <th>{{tax_vat_label}}
              <!-- <span class="label-details">(19,25%)</span></th> -->
            <td>{{tax_vat}}</td>
          </tr>{{/if}}

          <!-- <tr>
            <th>{{tax_precompte_label}} <span class="label-details">{{tax_precompte_rate}}</span></th>
            <td>{{tax_precompte}}</td>
          </tr> -->

          <!-- <tr class="amount-total">
            <th>{{amount_total_label}}</th>
            <td>{{amount_total}}</td>
          </tr>

          <tr data-hide-on-quote="true">
            <th>{{amount_paid_label}}</th>
            <td>{{amount_paid}}</td>
          </tr> -->

          <tr>
            <th>{{amount_total_label}}</th>
            <td>{{amount_total}}</td>
          </tr>

        </table>

        <div class="clearfix"></div>

        <div class="total-stripe"></div>

      </section>
    </div>

    <div class="clearfix"></div>

    <!-- {{#if order_has_retrievements}}

    <section id="removals">

      <h3 class="removals-header">{{removal_header_label}}</h3>

      <table cellpadding="0" cellspacing="0">

        <tr>
          <th>{{item_name_label}}</th>
          <th>{{removal_date_label}}</th>
          <th>{{quarters_selected_label}}</th>
          <th>{{trucks_configuration_label}}</th>
        </tr>

        {{#each removals}}
        <tr data-iterate="item">
          <td>{{removal_date}}</td> 
          <td>{{item_name}}</td>
          <td>{{quarters_selected}}</td>
          <td>{{trucks_configuration}}</td>
        </tr>
        {{/each}}

      </table>

    </section>
    {{/if}} -->

    <section id="seal">
      {{#if client_has_signature}}
      <div>
        <div id="signature-and-seal">
          <div class="seal">
            <img src="{{client_signature}}" height="100px" />
          </div>
          <!-- <div class="signature">
            <img src="{{client_signature}}" height="100px" />
          </div> -->
        </div>
        <div class="signature-details-container">
          <p class="signature-name">{{user_full_name_seal}}</p>
          <p class="signature-label">{{client_seal_position}}</p>
        </div>
      </div>
      {{/if}}

      {{#if admin_has_signature}}
      <div>
        <div id="signature-and-seal">
          <div class="seal">
            <!-- <img src="{{admin_has_signature}}" height="100px" />
          </div> -->
            <div class="signature">
              <img src="{{admin_has_signature}}" height="100px" />
            </div>
          </div>
          <div class="signature-details-container">
            <p class="signature-name">{{admin_full_name_seal}}</p>
            <p class="signature-label">{{admin_seal_position}}</p>
          </div>
        </div>
        {{/if}}
    </section>

    <section id="terms">

      <span>{{terms_label}}</span>
      <div>{{terms}}</div>

    </section>

    <div class="company-info">
      <span>{{company_address}}</span>
      <!-- <span>{{company_city_zip_state}}</span> -->

      <br />

      <span>{{company_phone_fax}}</span>
      <span>{{company_email_web}}</span>
    </div>

    <style id="myStyle">
      @import url("https://fonts.googleapis.com/css?family=Open+Sans:400,700&subset=cyrillic,cyrillic-ext,latin,greek-ext,greek,latin-ext,vietnamese");

      * {
        margin: 0;
        padding: 0;
        border: 0;
        font: inherit;
        font-size: 100%;
        vertical-align: baseline;
      }

      html {
        line-height: 1;
      }

      ol,
      ul {
        list-style: none;
      }

      table {
        border-collapse: collapse;
        border-spacing: 0;
      }

      caption,
      th,
      td {
        text-align: left;
        font-weight: normal;
        vertical-align: middle;
      }

      q,
      blockquote {
        quotes: none;
      }

      q:before,
      q:after,
      blockquote:before,
      blockquote:after {
        content: "";
        content: none;
      }

      a img {
        border: none;
      }

      article,
      aside,
      details,
      figcaption,
      figure,
      footer,
      header,
      hgroup,
      main,
      menu,
      nav,
      section,
      summary {
        display: block;
      }

      /* Invoice styles */
      /**
 * DON'T override any styles for the <html> and <body> tags, as this may break the layout.
 * Instead wrap everything in one main <div id="container"> element where you may change
 * something like the font or the background of the invoice
 */

      /** 
 * IMPORTANT NOTICE: DON'T USE '!important' otherwise this may lead to broken print layout.
 * Some browsers may require '!important' in oder to work properly but be careful with it.
 */
      .clearfix {
        display: block;
        clear: both;
      }

      .total-stripe {
        top: 11rem;
      }

      .hidden {
        display: none !important;
      }

      b,
      strong,
      .bold {
        font-weight: bold;
      }

      #container {
        font: normal 13px/1.4em 'Open Sans', Sans-serif;
        margin: 0 auto;
        padding: 3px;
        min-height: auto;
      }

      #memo .logo {
        float: left;
        margin-right: 20px;
      }

      #memo .logo img {
        width: 150px;
        height: 100px;
      }

      #memo .company-name {
        font-size: 24px;
        font-weight: bold;
        display: inline-block;
        min-width: 20px;
        line-height: 1em;
      }

      #memo .company-name .company-details {
        margin-top: 10px;
        font-size: 11px;
        font-weight: lighter;
        line-height: 13px;
      }

      #memo .company-name .company-details div {
        margin-bottom: 5px;
      }

      #memo .payment-info {
        float: right;
        text-align: right;
        font-size: 11px;
        color: #999;
      }

      #memo .payment-info div {
        margin-bottom: 1px;
        min-width: 20px;
      }

      #memo:after {
        content: '';
        display: block;
        clear: both;
      }

      #invoice-title-number {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        margin-top: 6px;
      }

      #invoice-title-number span {
        display: block;
        min-width: 20px;
      }

      #invoice-title-number #title {
        font-size: 28px;
        margin-bottom: 10px;
      }

      #invoice-title-number #number {
        font-size: 15px;
        margin-left: 1rem;
        color: #999;
      }

      #client-info {
        float: left;
        margin-top: 10px;
        min-width: 220px;
      }

      #client-info>div {
        margin-bottom: 3px;
        min-width: 20px;
      }

      #client-info span {
        display: block;
        min-width: 20px;
        color: #999;
      }

      #client-info>span {
        color: black;
      }

      #client-info .client-name {
        font-weight: bold;
        color: black;
        margin: 12px 0;
      }

      #invoice-info {
        float: right;
        margin-top: 65px;
      }

      #invoice-info .box-left {
        background: #143c5d;
        color: white;
      }

      #invoice-info .box-right {
        background: #E7E6E0;
      }

      #invoice-info>div {
        float: left;
        min-width: 120px;
        padding: 20px 25px;
      }

      #invoice-info>div:first-child {
        margin-right: 15px;
      }

      #invoice-info>div>div {
        float: left;
      }

      #invoice-info>div>div>span {
        display: block;
        min-width: 20px;
        min-height: 18px;
        margin-bottom: 3px;
      }

      #invoice-info>div>div:last-child {
        margin-left: 10px;
        text-align: right;
        float: right;
      }

      #invoice-info:after {
        content: '';
        display: block;
        clear: both;
      }

      table {
        table-layout: fixed;
      }

      table th,
      table td {
        vertical-align: top;
        word-break: keep-all;
        word-wrap: break-word;
      }

      #items {
        margin-top: 35px;
      }

      #items .first-cell,
      #items table th:first-child,
      #items table td:first-child {
        width: 18px;
        text-align: right;
      }

      .large-size {
        height: 700px;
      }

      #items table {
        border-collapse: separate;
        width: 100%;
      }

      #items table th {
        padding: 12px 10px;
        text-align: right;
        background: #143c5d;
        color: white;
      }

      #items table th:nth-child(2) {
        width: 25%;
        text-align: left;
      }

      #items table th:last-child {
        text-align: right;
      }

      #items table td {
        padding: 15px 10px;
        text-align: right;
        border-bottom: 1px solid #c9c9c9;
      }

      #items table td:first-child {
        text-align: left;
      }

      #items table td:nth-child(2) {
        text-align: left;
      }

      #removals {
        margin-top: 35px;
      }

      .removals-header {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 15px;
      }

      #removals table {
        width: 100%;
      }

      #removals table th {
        padding: 12px 10px;
        text-align: right;
        background: #143c5d;
        color: white;
      }

      #removals table th {
        width: 25%;
        text-align: center;
      }

      #removals table td {
        padding: 15px 10px;
        text-align: center;
        border-bottom: 1px solid #c9c9c9;
        border-left: 1px solid #c9c9c9;
        border-right: 1px solid #c9c9c9;
      }

      #sums {
        margin-top: 20px;
        position: relative;
        page-break-inside: avoid;
      }

      #sums .label-details {
        font-size: 10px;
        color: rgb(135, 135, 135);
      }

      #sums table {
        float: right;
        position: relative;
        z-index: 5;
      }

      #sums table tr th,
      #sums table tr td {
        min-width: 100px;
        padding: 6px 10px;
        text-align: right;
      }

      #sums table tr th {
        text-align: left;
        padding-right: 45px;
      }

      #sums table tr.amount-total th,
      #sums table tr.amount-total td {
        font-size: 15px;
        font-weight: bold;
      }

      #sums table tr:last-child th,
      #sums table tr:last-child td {
        font-size: 15px;
        font-weight: bold;
      }

      #sums .total-stripe {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 42px;
        background: #E7E6E0;
        border-top: 3px solid hsl(7, 73%, 53%);
      }

      #terms {
        margin: 50px 0 20px 0;
        page-break-inside: avoid;
      }

      #terms>span {
        display: inline-block;
        min-width: 20px;
        font-weight: bold;
      }

      #terms>div {
        margin-top: 5px;
        min-height: 15px;
        min-width: 50px;
      }

      .company-info {
        color: #999;
        font-size: 12px;
      }

      .company-info span {
        display: inline-block;
        min-width: 20px;
      }

      .ib_invoicebus_fineprint {
        text-align: left;
        left: 40px !important;
      }

      #signature-and-seal {
        margin-top: 15px;
        height: 100px;
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        align-items: center;
      }

      #signature-and-seal .signature,
      #signature-and-seal .seal {
        width: auto;
      }

      .signature-details-container {
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        align-items: center;
        width: 100%;
      }

      .signature-label,
      .signature-name {
        text-align: right;
      }

      .signature-label {
        text-decoration: underline;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .marg {
        margin-top: 5px;
      }

      .margin {

        padding-top: 10px;
      }

      .general-condition {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        line-height: 14px;

      }

      .general-condition p {
        font-size: 9px;
      }

      .general-condition .row {
        width: 49%;
      }

      .general-condition .row .article {
        font-size: 9px;
        margin-top: 3px;
        text-align: justify;
      }

      .general-condition .row .article .article-title {
        font-size: 10px;
        font-weight: bold;
        margin-bottom: 1px;
        margin-top: 10px;
      }

      .general-condition .row .sub-section {
        margin: 3px 0 auto;
        text-align: justify;
        margin-bottom: 2px;
      }

      .general-condition .row ul {
        list-style: disc;
        margin: 3px 0 5px 10px;
        font-size: 9px;
      }

      .general-condition .row ol {
        margin: 1px 0 1px 10px;
        list-style: decimal;
        font-size: 9px;
      }

      #seal {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 1rem 0;
      }

      .general-condition .row li {}
    </style>
    <!-- <script src="http://cdn.invoicebus.com/generator/generator.min.js?data=true"></script> -->
</body>

</html>