import { UsersService } from './../users/users.service';
import { ResetPasswordRepository } from './repository/reset-password.repository';
import { CompaniesModule } from './../companies/companies.module';
import { JwtModule } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { UsersModule } from '../users';
import { AuthService } from './auth.service';
import {jest} from '@jest/globals'

const users = [
  {
    email: "<EMAIL>",
    name: "salam sahane",
    tel: "656302225",
    category: 0,
    password: "$2b$10$1LdjmcD1vV25K2soCXjb6e3PFKkJpbT1lz0dZxwejZSUuxMjTR3xe"
  },
  {
    email: "<EMAIL>",
    name: "fred ngatchou",
    tel: "656302225",
    category: 0,
    password: "123456"
  }
]

describe('AuthService', () => {
  let service: AuthService;

  const mockUserService = {
    findOne: jest.fn().mockImplementation((param: any) => {
      try {
        const user = users.find(user => user.email == param.filter.email);
        
        if (user != undefined) return user; 
  
        throw new Error('User not found');
      } catch(error) {
        return Promise.reject(error);
      }
    })
  }

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AuthService, {
        provide: ResetPasswordRepository,
        useValue: {}
      }, {
        provide: "QUEUE",
        useValue: {}
      },
      {
        provide: UsersService,
        useValue: mockUserService
      }],
      imports: [JwtModule.register({}), UsersModule, CompaniesModule]
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Login method', () => {
    it('should reject on bad credentials', async () => {
      const credentials = {email: '<EMAIL>', password: 'salam123'};
      expect(async () => await service.login(credentials)).rejects;
    })

    it('should return AuthUser', async () => {
      const credentials = {email: '<EMAIL>', password: 'salam'};
      expect((await service.login(credentials)).accessToken).toBeDefined();
    })
  })

  describe('SendResetPasswordMail method', () => {
    it('should reject if no user found with given email', () => {
      expect(async () => { await service.sendResetPasswordMail({ email: '<EMAIL>' })}).rejects;
    })
  })
});
