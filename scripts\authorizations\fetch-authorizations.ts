import { config } from './../../convict-config';
import fs from 'fs';
import glob from 'glob-promise';
import { setLoader, stopLoader } from 'scripts/common';

type AppAuthorization = {
  enable: boolean;
  label: string;
  actions: string[];
};

const actionNameRegEx =
  config.get('env') === 'development'
    ? /(?:[a-zA-Z]+)(?=\.action\.ts)/gm
    : /(?:[a-zA-Z]+)(?=\.action\.js)/gm;
const contentRegEx =
  config.get('env') === 'development'
    ? /(?<=')[a-zA-Z0-9_]+(?=')/gm
    : /(?<=")[a-zA-Z0-9_]+(?=")/gm;
const actionFilesRegEx =
  config.get('env') === 'development'
    ? '**/src/modules/**/*.action.ts'
    : '**/src/modules/**/*.action.js';

const appAuthorizations: AppAuthorization[] = [];

function fetchActions(file: string): string[] {
  const fileContent = fs.readFileSync(file, { encoding: 'utf8', flag: 'r' });
  return fileContent.match(contentRegEx);
}

function setAuthorization(file: string) {
  const actions = fetchActions(file);
  const label = file.split('/').pop().match(actionNameRegEx).toString();

  appAuthorizations.push({ label, actions, enable: true });
}

async function fetchAuthorizations() {
  setLoader("Récupération des autorisations dans l'application \n");

  const files = await glob(actionFilesRegEx);

  for await (const file of files) setAuthorization(file);

  stopLoader(true);

  return appAuthorizations;
}

export { fetchAuthorizations };
