import readline from 'readline';
import { insertCategoriesDataFile } from 'scripts/categories/category-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données categories des categories produits de Click CADYST, ce processus effacera la collection categories.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertCategoriesDataFile();
      console.log('Données des catégories insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();