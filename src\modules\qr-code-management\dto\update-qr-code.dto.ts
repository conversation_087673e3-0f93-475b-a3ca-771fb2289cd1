import { PartialType } from '@nestjs/swagger';
import { IsArray, IsOptional } from 'class-validator';
import { Product } from '@la-pasta-module/cart/products/entities';
import { Packaging } from '@la-pasta-module/cart/packagings/entities';
import { CreateQrCodeDto } from './create-qr-code.dto';
import { QrCodeData } from '../entities/qr-code.entity';
import { OrderSupplier } from '@la-pasta-module/order-supplier/entities/order-supplier.entity';

export class UpdateQrCodeDto extends PartialType(CreateQrCodeDto) {

    @IsOptional()
    @IsArray()
    qrCodeData: QrCodeData[];

    @IsOptional()
    product: Partial<Product>;

    @IsOptional()
    packaging: Partial<Packaging>;

    @IsOptional()
    order: OrderSupplier;

}
