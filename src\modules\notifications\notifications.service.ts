import { forwardRef, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { BaseService, getUser, setResponse, t } from '@la-pasta/common';
import { NotificationRepository } from './repository/notification.repository';
import moment from 'moment';
import { BaseNotification, FeedbackNotification, messageType, NotificationCategory, OrderNotification } from './entities';
import { BaseUser, User, UsersService } from '@la-pasta-module/users';
import { ObjectId } from 'mongodb';
import { Feedback } from '@la-pasta-module/feedbacks/entities';
import { Order } from '@la-pasta-module/order';
import { OrderSupplier } from '@la-pasta-module/order-supplier/entities/order-supplier.entity';
import { OrderItem } from '@la-pasta-module/order-items/entities/order-item.entity';

@Injectable()
export class NotificationsService extends BaseService {
  constructor(
    private notificationRepository: NotificationRepository,
    @Inject(forwardRef(() => UsersService)) private readonly userSrv: UsersService,

  ) {
    super(notificationRepository)
  }

  async createNotification(createNotificationDto: CreateNotificationDto, user: BaseUser) {
    createNotificationDto.emailAdmin = user?.email;
    createNotificationDto.dates = { created: (new Date()).getTime() };
    createNotificationDto.status = 100;
    const res = await this.create(createNotificationDto);
    return setResponse(HttpStatus.CREATED, t('NOTIF_CREATED'), res.data);

  }

  async generateActionNotification(category: NotificationCategory, data: Order | OrderSupplier | OrderItem | Feedback) {
    const events = {
      [NotificationCategory.ORDER]: OrderNotification,
      [NotificationCategory.FEEDBACK]: FeedbackNotification,
    };

    const notification = new events[category](data);
    await this.create(notification);
  }

  async findAllnNotifications(query: QueryOptions) {

    const orConditions = [];
    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }

    if (query?.filter['feedback.user.email']) {
      orConditions?.push({ 'feedback.user.email': query?.filter['feedback.user.email'] })
    }

    if (query.filter?.userId) {
      orConditions?.push({ userId: query?.filter?.userId })
    }

    if (query?.filter?._id && query?.filter?._id?.includes('$')) {
      query.filter._id = JSON.parse(query?.filter?._id);
      const objectIds = this.convertToObjectIdArray(query?.filter?._id?.$in);
      orConditions?.push({ _id: { $in: objectIds } });
    }

    if (orConditions?.length > 0) {
      query.filter["$or"] = orConditions;
    }

    delete query?.filter?.userId;
    delete query?.filter?._id;
    delete query?.filter['feedback.user.email'];

    return await this.findAll(query);

  }

  convertToObjectIdArray(ids: string[]): ObjectId[] {
    return ids?.map(id => new ObjectId(id));
  }

  async updateNotification(id: string, updateNotificationDto: UpdateNotificationDto, user?: User) {
    const userNotif = await this.findOne({ filter: { _id: new ObjectId(id) } });
    if (userNotif?.isGeneralNotif) {
      const newNotifs = this.transformNotifications(user?.notifications, userNotif?._id.toString(), 200);
      user.notifications = newNotifs;
      return await this.userSrv.update({ _id: user?._id }, user);
    }
    else {
      const res = await this.update(
        { _id: id },
        { status: messageType.READ },
      );
      return res;
    }
  }

  transformNotifications(notifications: any[], notificationId: string, status: number): Notification[] {
    return notifications.map(notification => {
      if (notification?.id?.toString() === notificationId) {
        return { id: notificationId, status: status };
      } else {
        return notification;
      }
    });
  }

  async sendNotificationToCustomers(data: any) {
    const notificationId = data?._id;
    const updatePipeline = [
      {
        $set: {
          notifications: {
            $cond: {
              if: { $isArray: "$notifications" },
              then: { $concatArrays: [{ $map: { input: "$notifications", as: "notification", in: { id: "$$notification.id", status: "$$notification.status" } } }, [{ id: notificationId, status: 100 }]] },
              else: [{ id: notificationId, status: 100 }]
            }
          }
        }
      }
    ];

    if (data?.categories > 0) {
      await this.userSrv.updateMany({
        $or: [
          { category: { $in: data?.categories } },
          { 'company.category': { $in: data?.categories } }
        ]
      }, updatePipeline);
    } else {
      await this.userSrv.updateMany({}, updatePipeline);
    }
  }

  private setDateFilter(query: QueryFilter) {
    query['created_at'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }

  // Méthode principale pour supprimer une notification unique
  async deleteNotification(id: string, user: User) {
    const notification = await this.findOne({ filter: { _id: new ObjectId(id) } });

    if ('isGeneralNotif' in notification) {
      return await this.handleGeneralNotification(id, user);
    } else {
      return await this.handleSpecificNotification(id);
    }
  }

  // Méthode principale pour supprimer plusieurs notifications
  async deleteMultipleNotifications(ids: string[], user: User) {
    for (const id of ids) {
      await this.deleteNotification(id, user)
    }

  }


  // Gère une notification générale unique
  private async handleGeneralNotification(id: string, user: User) {
    const updatedNotifications = user.notifications?.map(n => {
      if (n.id === id) {
        return { ...n, status: messageType.DELETE };
      }
      return n;
    });

    await this.userSrv.update(
      { _id: user?._id },
      { notifications: updatedNotifications },
    );

    return setResponse(HttpStatus.OK, t('NOTIF_REMOVED_FROM_USER'));
  }


  // Gère une notification spécifique unique
  private async handleSpecificNotification(id: string) {
    await this.update(
      { _id: new ObjectId(id) },
      { enable: false },
    );

    return setResponse(HttpStatus.OK, t('NOTIF_DELETED'));
  }


}

