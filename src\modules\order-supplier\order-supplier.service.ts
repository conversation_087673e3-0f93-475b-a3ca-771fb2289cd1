import { forwardRef, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { CreateOrderSupplierDto } from './dto/create-order-supplier.dto';
import { UpdateOrderSupplierDto } from './dto/update-order-supplier.dto';
import { BaseService, convertParams, setDateFilter, setResponse, t } from '@la-pasta/common';
import { OrderSupplierRepository } from './repository';
import { LoyaltyProgramService } from '@la-pasta-module/loyalty-program/loyalty-program.service';
import { Order, OrderStatus } from '@la-pasta-module/order/entities';
import { ObjectId } from 'bson';
import moment from 'moment';
import { NotificationCategory } from '@la-pasta-module/notifications/entities';
import { NotificationsService } from '@la-pasta-module/notifications/notifications.service';
import { OrderSupplier } from './entities/order-supplier.entity';
import { User } from '@la-pasta-module/users';
import { log } from 'console';
import { ClientProxy } from '@nestjs/microservices';

@Injectable()
export class OrderSupplierService extends BaseService {
  protected logger: Logger;

  constructor(
    private readonly orderSupplierRepository: OrderSupplierRepository,
    private notificationSrv: NotificationsService,
    @Inject(forwardRef(() => LoyaltyProgramService)) private readonly loyaltyProgramSrv: LoyaltyProgramService,
    @Inject('QUEUE') private queueClient: ClientProxy,

  ) {
    super(orderSupplierRepository);
    this.logger = new Logger(this.constructor.name)

  }

  private setDateFilter(query: QueryFilter) {
    query['created_at'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }


  async createOrder(user: User, newOrder: CreateOrderSupplierDto) {

    newOrder.appReference = await this.generateAppReference(newOrder?.supplier?.address?.city);

    newOrder.status = OrderStatus.CREATED;

    newOrder.user = user;
    newOrder.dates = { created: moment().valueOf() };

    const orderCreated = await this.create(newOrder);

    this.notificationSrv.generateActionNotification(NotificationCategory.ORDER, (newOrder as unknown as OrderSupplier));

    await this.loyaltyProgramSrv?.setProgramFidelity(newOrder as unknown as OrderSupplier);

    return setResponse(HttpStatus.CREATED, t('PAYMENT_RETAIL_INIT'), orderCreated.data,);

  }

  async generateAppReference(storeRef: string) {
    const dateStr = moment().format('YYMMDDHHmmss');
    const random = Math.floor(Math.random() * 999) + 100;
    const mapped = {
      L10: 'BON',
      K10: 'KRIB',
      CM10020: 'NOM',
      CM12000: 'DEPYAO',
      CM12010: 'DEPFIG',
      CM12020: 'DEPMAR',
      CM12030: 'DEPNGA',
      CM12040: 'DEPKYE',
      CM12070: 'DEPGAB',
      CM12090: 'DEPBEL',
      CM12100: 'DEPGAR',
      CM12110: 'DEPKOU',
    };

    const currAppRef = `${mapped[storeRef] ?? 'CUST'}${dateStr}`.substr(0, 12) + `${random}`;
    const document = await this.orderSupplierRepository.findOne({ filter: { appReference: currAppRef } });

    // AppReference déjà utilisé, on relance la fonction
    if (document)
      return await this.generateAppReference(storeRef);


    return currAppRef;
  }


  async updateOrderSupplier(id: string, body: UpdateOrderSupplierDto) {
    try {
      delete body?.orders?._id;
      body.orders.status = OrderStatus.VALIDATED;
      body.orders.dates.validated = new Date().valueOf();
      const result = await this.update({ _id: new ObjectId(id) }, body.orders);

      if (!result || result.status !== HttpStatus.OK) {
        this.logger.error(`Échec de la mise à jour de la commande avec l'ID ${id}.`);
        throw new Error(`Unable to update order with ID ${id}.`);
      }

      const order = await this.findOne({ filter: { _id: new ObjectId(id) } }) as unknown as OrderSupplier;

      if (!order) {
        this.logger.error(`Commande avec l'ID ${id} introuvable après mise à jour.`);
        throw new Error(`Order with ID ${id} not found after update.`);
      }

      // Application du programme de fidélité
      await this.loyaltyProgramSrv.setProgramFidelity(order);

      // 📲 Envoi de SMS après validation de la commande
      if (order?.user?.tel) {
        this.queueClient.emit('sms_received', {
          receiver: order.user.tel,
          message: `Bonjour ${order.user.firstName},\nVotre commande ${order.appReference} a été validée avec succès. ✅\nMerci pour votre confiance !`
        });
      }

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la commande avec l'ID ${id}:`, error);
      throw new Error(`Failed to update order with ID ${id}: ${error.message}`);
    }
  }



  async RejectOrder(id: string, orderToReject, user) {
    try {
      const reject = {
        raison: orderToReject.rejectMessage,
        date: moment().valueOf(),
        user: user._id,
      };

      const order = await this.findOne({ filter: { _id: new ObjectId(id) } }) as unknown as OrderSupplier;

      if (!order) {
        this.logger.error(`Commande avec l'ID ${id} introuvable.`);
        return;
      }

      const reson = {
        raison: orderToReject.rejectMessage,
        date: moment().valueOf(),
        user: {
          _id: user?._id,
          lastName: user?.lastName,
          email: user?.email
        }
      };

      order.status = OrderStatus?.REJECTED;
      order.validation = { ...reson };

      const result = await this.update({ _id: new ObjectId(id) }, order);

      if (!result) {
        this.logger.error(`Échec de la mise à jour de la commande avec l'ID ${id}.`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors du rejet de la commande avec l'ID ${id}:`, error);

    }
  }

  async orderSupplierParticular(query: QueryOptions, user: User) {
    try {
      if ('startDate' in query.filter || 'endDate' in query.filter) {
        this.setDateFilter(query.filter);
      }

      if ('status' in query.filter && query.filter.status.includes('$')) {
        query.filter.status = JSON.parse(query.filter.status);
      }

      query.filter['user._id'] = new ObjectId(user._id);
      delete query.filter.particularId;

      const results = await this.findAll(query);

      return results;
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des commandes particulier:', error);
      throw error;
    }
  }

  async getVolumeOrderByParticularClient(query: QueryOptions): Promise<any> {

    query = convertParams(query);
    if ('startDate' in query?.filter || 'endDate' in query?.filter) {
      query = setDateFilter(query);
    }
    const data = await this.orderSupplierRepository.getVolumeOrderByParticularClient(query);
    return data;
  }
}
