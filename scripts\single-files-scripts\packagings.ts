import readline from 'readline';
import { insertCompagniesDataFile } from 'scripts/company';
import { insertPackaginsDataFile } from 'scripts/packaging';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données des packagings de Click CADYST, ce processus effacera la collection packagings.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertPackaginsDataFile();
      console.log('Données des packagings insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();