import { ArrayMinSize, IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CompanyCategory, PrecompteRate } from "../entities";
import { CompanyEmployee, User } from "@la-pasta-module/users";
import { i18nValidationMessage } from "nestjs-i18n";
import { Option } from "@la-pasta-module/options/entities";

export class CreateCompanyDto {
    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsNumber({}, { message: i18nValidationMessage('validation.BAD_PHONE') })
    tel: number;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    name: string;

    @ApiProperty({ enum: CompanyCategory })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsEnum(CompanyCategory)
    category: CompanyCategory;

    @ApiProperty({ type: [CompanyEmployee] })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @IsArray({ message: i18nValidationMessage('validation.NOT_ARRAY') })
    @ArrayMinSize(1, { message: i18nValidationMessage('validation.MIN_COMPANY_USER') })
    users: CompanyEmployee[];

    @ApiPropertyOptional()
    @IsOptional()
    rccm: string;

    @ApiPropertyOptional()
    @IsOptional()
    nui: string;

    @ApiPropertyOptional()
    @IsOptional()
    defaultStore: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: Address;

    @ApiPropertyOptional()
    @IsOptional()
    precompteRate?: PrecompteRate;

    @ApiPropertyOptional()
    @IsOptional()
    Profiles?: any;

    @ApiPropertyOptional()
    @IsOptional()
    afrilandKey?: string;

    @ApiPropertyOptional()
    @IsOptional()
    enable?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    erpSoldToId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    erpShipToId?: number;

    @ApiPropertyOptional()
    @IsOptional()
    isLoyaltyProgDistributor?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    associatedCommercial: Partial<User>;

    @ApiPropertyOptional()
    @IsOptional()
    erpShipToDesc?: string;

    @ApiPropertyOptional()
    @IsOptional()
    q1Enabled?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    annualOrderEnabled?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    options?: Option[];

    @ApiPropertyOptional()
    @IsOptional()
    associatedShippingOption?: {
        optionId: string;
        shippingAddressId: string[];
    };
}
