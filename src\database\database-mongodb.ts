import { Logger } from "@nestjs/common";
import { Db, MongoClient, MongoClientOptions } from "mongodb";
import { config } from "../../convict-config";

export class MongoDatabase {

    private static instance: MongoDatabase;

    private options!: MongoClientOptions;
    private db!: Db;

    private constructor(private readonly logger?: Logger) { }

    public static getInstance = (): MongoDatabase => MongoDatabase.instance ??= new MongoDatabase();

    public getDatabase = async (): Promise<Db> => this.db ??= await this.setDatabase();

    private async setDatabase(): Promise<Db> {
        if (config.get(`db.auth.user`) && config.get('db.auth.password')) {
            this.options.auth = {
                username: config.get('db.auth.user'),
                password: config.get('db.auth.password')
            }
        }

        try {
            const connection = await MongoClient.connect(this.connectionUrl, this.options);
            return connection.db();
        } catch (error: unknown) {
            this.logger?.error(error);
        }
    }

    private get connectionUrl(): string {
        return (config.get('db.auth.user') && config.get('db.auth.password'))
            ? `mongodb://${config.get('db.auth.user')}:${config.get('db.auth.password')}@${config.get('db.host')}/${config.get('db.name')}?retryWrites=true&w=majority`
            : `mongodb://${config.get('db.host')}/${config.get('db.name')}?retryWrites=true&w=majority`;
    }
}