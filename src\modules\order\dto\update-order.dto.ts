import { PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { CreateOrderDto } from './create-order.dto';
import { i18nValidationMessage } from 'nestjs-i18n';
import { Carrier } from '../entities';

export class UpdateOrderDto extends PartialType(CreateOrderDto) {
  @IsOptional()
  _id: string;

  @IsOptional()
  enable: boolean;

  @IsOptional()
  created_at: number;

  @IsOptional()
  updated_at: number;

  @IsOptional()
  canceledJdeNumbers?: any;

  @IsOptional()
  erpReference?: string;

  @IsOptional()
  reference?: string;

  @IsOptional()
  rejectReason?: string;

  @IsOptional()
  nberModif?: number;

  @IsOptional()
  messageCancellation?: string;

  @IsOptional()
  cancellationStatus?: string;

  @IsOptional()
  carrier?: Carrier
}
export class ValidateNunOrderDto {
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  erpReference: string;
}
