import { Controller, Get, Post, Body, Patch, Param, UseGuards, Query } from '@nestjs/common';
import { CreateOrderItemDto } from './dto/create-order-item.dto';
import { UpdateOrderItemDto } from './dto/update-order-item.dto';
import { Permission } from '@la-pasta/infrastructures/security';
import { OrderItemsService } from './order-items.service';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { BaseUser } from '@la-pasta-module/users';
import { OrderItemsAction } from './actions';
import { getUser } from '@la-pasta/common';

@Controller('order-items')
export class OrderItemsController {

  constructor(private readonly orderItemsService: OrderItemsService) { }

  @Post()
  @UseGuards(JwtGuard)
  async createOderItem(@Body() createOrderItemDto: CreateOrderItemDto) {
    Permission.orderItemsAuthorization(getUser(), OrderItemsAction.CREATE);
    return await this.orderItemsService.createOrder(createOrderItemDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  async findAllOderItem(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    Permission.orderItemsAuthorization(user, OrderItemsAction.VIEW);
    return await this.orderItemsService.findAll({ filter: query });
  }

  @Get('history')
  @UseGuards(JwtGuard)
  async getOrderItemByUser(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    Permission.orderItemsAuthorization(user, OrderItemsAction.VIEW);
    return await this.orderItemsService.getAllOrderItem({ filter: query }, user);
  }

  @UseGuards(JwtGuard)
  @Patch('handle-order/:id')
  async handleOrderAction(@Param('id') id: string, @Body() body: UpdateOrderItemDto) {
    Permission.orderItemsAuthorization(getUser(), OrderItemsAction.UPDATE, { _id: id });
    return await this.orderItemsService.handleOrderAction(id, body);

  }

  @Get(':id')
  @UseGuards(JwtGuard)
  async findOneOderItem(@Param('id') id: string, @GetUser() user: BaseUser) {
    Permission.orderItemsAuthorization(user, OrderItemsAction.VIEW);
    return await this.orderItemsService.findOne({ filter: { _id: id } });
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async updateOderItem(@Param('id') id: string, @Body() body: UpdateOrderItemDto) {
    Permission.orderItemsAuthorization(getUser(), OrderItemsAction.UPDATE, { _id: id });
    return await this.orderItemsService.update({ filter: { _id: id } }, body);
  }

  @UseGuards(JwtGuard)
  @Patch('validate/:id')
  async ValidateOderItem(@Param('id') id: string) {
    Permission.orderItemsAuthorization(getUser(), OrderItemsAction.UPDATE, { _id: id });
    return await this.orderItemsService.validateOrder(id);
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.orderItemsService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }


}
