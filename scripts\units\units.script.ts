import { ObjectId } from 'mongodb';
import { MongoDatabase } from '@la-pasta/database';
import { Unit } from '@la-pasta-module/cart';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import moment from 'moment';

const database = MongoDatabase.getInstance();
const collectionName = 'units';

const units: Omit<Unit, '_id'>[] = [
  {
    label: "Kilogramme",
    symbol: "KG",
    value: 25,
    ratioToTone: 40
  },
  {
    label: "Kilogramme",
    symbol: "KG",
    value: 50,
    ratioToTone: 20
  }
]

export async function insertUnits() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des unités\n')
    units.forEach(unit => { unit.enable = true; unit.create_at = moment().valueOf() });
    const insertedUnits = await (await database.getDatabase()).collection(collectionName).insertMany(units);
    stopLoader(insertedUnits);
  } catch (error) {
    console.error(error);
  }
}

export async function getUnits() {
  try {
    return await (await database.getDatabase()).collection(collectionName).find().toArray() as unknown as Unit[];
  } catch (error) {
    console.error(error);
  }
}

export async function getUnit(id: any) {
  try {
    return await (await database.getDatabase()).collection(collectionName).findOne({ _id: new ObjectId(id) }) as unknown as Unit;
  } catch (error) {
    console.error(error);
  }
}