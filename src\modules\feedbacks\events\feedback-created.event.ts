import moment from "moment";
import { CreateFeedbackDto } from "../dto/create-feedback.dto";

export class FeedbackCreatedEvent {
    feedback_ref: string;
    feedback_category: string;
    feedback_subcategory: string;
    feedback_createAt: string;
    email: string;
    username: string;
    associateCommercialEmail: string;
    client_name: string;

    constructor(
        feedback: CreateFeedbackDto
    ) {
        this.feedback_ref = feedback.ref;
        this.feedback_category = feedback?.category?.label;
        this.feedback_subcategory = feedback?.subCategory?.label;
        this.feedback_createAt = moment().format('DD-MM-YYYY');
        this.associateCommercialEmail = feedback?.user?.company?.associatedCommercial?.email
        this.client_name = feedback?.user?.company?.name || (feedback?.user?.firstName ?? '') + ' ' + (feedback?.user?.lastName ?? '');
    }

}