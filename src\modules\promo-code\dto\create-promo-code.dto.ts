import { Packaging, Product } from "@la-pasta-module/cart";
import { CompanyCategory } from "@la-pasta-module/companies";
import { UserCategory, UserExists } from "@la-pasta-module/users";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";
import { DiscountType } from "../entities";
import { PromoCodeExists } from "../validations/promo-code-validation.decorators";

export class CreatePromoCodeDto {

    @ApiProperty({ type: String })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    // @PromoCodeExists()
    reference: string;

    @ApiProperty({ type: String })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    dates: {
        start: number;
        end: number;
    };

    @ApiPropertyOptional()
    @IsOptional()
    promoter: {
        name: string;
        email: string;
        tel: number;
    };

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    products: Partial<Product[]>;

    @ApiProperty()
    @IsOptional()
    userCategoriesAssociated: UserCategory[] | CompanyCategory[];

    @ApiProperty()
    @IsOptional()
    regions: string[];

    @ApiProperty()
    @IsOptional()
    packagings: Partial<Packaging[]>;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    discount: {
        type: DiscountType;
        appliedOn: string[];
        value: number;
    }

}
