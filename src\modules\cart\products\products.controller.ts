import { JwtGuard } from '@la-pasta-module/auth';
import { getUser } from '@la-pasta/common';
import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { setResponseController } from 'src/common/helpers';
import { ApiTags } from '@nestjs/swagger';
import { Permission } from '@la-pasta/infrastructures/security';
import { ProductAction } from './actions';

@ApiTags('Products')
@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) { }

  @UseGuards(JwtGuard)
  @Post()
  async create(@Body() createProductDto: CreateProductDto) {
    Permission.productAuthorization(getUser(), ProductAction.CREATE);
    return await this.productsService.createProduct(createProductDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.productAuthorization(getUser(), ProductAction.VIEW);
    const data = await this.productsService.findAll({ filter: query });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get('filters-elements')
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.productsService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.productAuthorization(getUser(), ProductAction.VIEW);
    const data = await this.productsService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    Permission.productAuthorization(getUser(), ProductAction.UPDATE);
    const data = await this.productsService.updateProduct(id, updateProductDto);
    return setResponseController(data);
  }
}
