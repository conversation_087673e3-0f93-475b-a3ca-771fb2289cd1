import { BaseService, setResponse, t } from '@la-pasta/common';
import {
  ForbiddenException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ClientProxy } from '@nestjs/microservices';
import { UserRepository } from '../users/repository/index';
import { config } from 'convict-config';
import { CompaniesService, Company } from '@la-pasta-module/companies';
import {
  AuthUser,
  CompanyEmployee,
  BaseUser,
  UserCategory,
  UsersService,
} from '@la-pasta-module/users';
import {
  CredentialDto,
  CredentialOtpDto,
  JwtPayload,
  NewPasswordDto,
  NewToken,
  RefreshTokenDto,
  ResetPasswordDto,
} from './dtos';
import { ResetPasswordEvent } from './events';
import { ResetPasswordRepository } from './repository';
import * as bcrypt from 'bcrypt';
import moment from 'moment';
import { ConnectionOtpEvent } from './events/connection-otp.event';

@Injectable()
export class AuthService extends BaseService {
  constructor(
    @Inject(forwardRef(() => UsersService))
    private readonly userService: UsersService,
    private readonly userRepository: UserRepository,
    private jwtService: JwtService,
    @Inject(forwardRef(() => CompaniesService))
    private companyService: CompaniesService,
    private resetPasswordRepository: ResetPasswordRepository,
    @Inject('QUEUE') private queueClient: ClientProxy,
  ) {
    super();
  }

  mockOtp = {
    [`${UserCategory.CompanyUser}`]: 1234,
    [`${UserCategory.Particular}`]: 123,
    [`${UserCategory.Commercial}`]: 12345,
    [`${UserCategory.DonutAnimator}`]: 123456,
  }

  async login(credential: CredentialDto): Promise<AuthUser> {
    const user = await this.getUser(credential.email);
    await this.verifyPassword(credential.password, user.password);

    delete user.password;

    return {
      ...user,
      accessToken: `${await this.generateToken({
        sub: user._id,
        email: user.email,
      })}`,
    };
  }

  async loginOtp(credential: CredentialOtpDto): Promise<QueryResult> {
    const { emailOrTel } = credential;

    const obj = new RegExp(/^6[0-9]{8}$/).test(emailOrTel as string)
      ? { tel: emailOrTel }
      : { email: emailOrTel };

    const users = (await this.userService.findAll({ filter: { ...obj, enable: true } }))?.data;
    if (users instanceof Error || users?.length <= 0)
      throw new HttpException(t('error.BAD_CREDENTIAL'), 400); //TODO: change UnauthorizedException to HttpException with the correct message

    if (users.length > 1) throw new HttpException(t('error.NOT_ACCEPTABLE'), HttpStatus.NOT_ACCEPTABLE);

    const category = users[0]?.category
    const userOtp = {
      value: ((config.get('isMockOtp')) || [`${config.get('telStore')}`, `${config.get('telIndirectClient')}`].includes(`${emailOrTel}`))
        ? this.mockOtp[`${category}`] : Number(this.getRandomString(4, Number)),
      // expiredAt: moment().valueOf() + 300000
      expiredAt: moment().add(config.get('time_otp_expire.value'), 'minutes').valueOf()
    };

    await this.userRepository.update({ _id: users[0]?._id }, { userOtp: userOtp });

    const envs = config.get('activeForEnv');

    if (['development'].includes(config.get('env')) || emailOrTel !== config.get('telStore')) {
      this.logger.log(`emailOrt tel is ${emailOrTel} and telStore is : ${config.get('telStore')}`);
      (obj?.email || envs.includes(config.get('env'))) ?
        this.queueClient.emit(
          'connexion_created', //TODO: Create enum for event Auth
          new ConnectionOtpEvent(userOtp.value, users[0].email),
        ) :
        this.queueClient.emit('sms_received',
          { receiver: users[0].tel, message: `Veuillez entrer le code suivant pour votre nouvelle connexion : '${userOtp?.value}'` });
    }

    return users[0];
    // return setResponse(HttpStatus.ACCEPTED, await t('mail.OTP_CODE_SEND')); // TODO: return the correct message
  }

  async verifyOtp(body: any): Promise<AuthUser> {
    const user = await this.getUserByOtpCode(body.value);
    delete user.password;

    return {
      ...user,
      accessToken: `${await this.generateToken({
        sub: user._id,
        email: user.email,
        tel: user.tel,
      })}`,
    };
  }

  async sendResetPasswordMail(resetPasswordDto: ResetPasswordDto) {
    const user = (await this.userService.findOne({
      filter: { email: resetPasswordDto.email },
    })) as unknown as BaseUser;

    if (user instanceof Error)
      throw new NotFoundException(t('error.NO_ACCOUNT'));

    const token = await this.generateToken(
      { sub: user._id, email: user.email, tel: user?.tel },
      '15m',
    );

    const resetData = await this.resetPasswordRepository.create({
      userId: user._id.toString(),
      email: user.email,
      token,
    });

    if (resetData.insertedId) {
      this.queueClient.emit(
        'reset_password',
        new ResetPasswordEvent(
          user.email,
          `${user?.firstName} ${user?.lastName}`,
          `${config.get('baseUrl')}${config.get(
            'basePath',
          )}/auth/change-password/${token}`,
        ),
      );

      return setResponse(HttpStatus.OK, t('mail.RESET_MAIL_SENT'));
    }
  }

  async getResetPasswordView(token: string) {
    const payload = this.jwtService.decode(token, {
      complete: false,
    }) as JwtPayload;

    if (!payload || Date.now() >= payload.exp * 1000)
      throw new ForbiddenException(t('error.INVALID_TOKEN'));

    const resetProcess = await this.resetPasswordRepository.findOne({
      filter: { userId: payload.sub, email: payload.email },
    });

    if (!resetProcess) throw new NotFoundException(t('error.NO_RESET_REQUEST'));

    return 'reset-password';
  }

  async setNewPassword(newPasswordDto: NewPasswordDto, token: string) {
    if (
      newPasswordDto.newPassword == '' ||
      newPasswordDto.newPasswordConfirm == ''
    ) {
      return { error: 'Veuillez remplir tous les champs' };
    }

    if (newPasswordDto.newPassword !== newPasswordDto.newPasswordConfirm) {
      return { error: 'Les deux mots de passe sont differents' };
    }

    const payload = this.jwtService.decode(token) as JwtPayload;
    if (!payload) throw new ForbiddenException(t('error.INVALID_TOKEN'));

    const user = (await this.userService.findOne({
      filter: { _id: payload.sub, email: payload.email },
    })) as unknown as BaseUser;
    if (user instanceof Error)
      throw new NotFoundException(t('error.NO_ACCOUNT'));

    user.password = await bcrypt.hash(newPasswordDto.newPassword, 10);

    const updatedPassword = await this.userService.update(
      { _id: user._id },
      { password: user.password },
    );

    if (updatedPassword.status == 200) {
      await this.resetPasswordRepository.delete({
        userId: payload.sub,
        email: payload.email,
      });
      return { success: 'Mot de passe réinitialisé avec succès' };
    }
  }

  async refeshToken({ token }: RefreshTokenDto): Promise<NewToken> {
    const payload = this.jwtService.decode(token) as JwtPayload;
    return {
      newToken: `${await this.generateToken({
        sub: payload.sub,
        email: payload.email,
        tel: payload.tel,
      })}`,
    };
  }

  private async getUser(email: string): Promise<BaseUser> {
    let user = (await this.userService.findOne({
      filter: { email, enable: true },
    })) as unknown as BaseUser;

    if (user instanceof Error)
      throw new UnauthorizedException(t('error.BAD_CREDENTIAL'));

    if (user.category == UserCategory.CompanyUser)
      user = await this.setCompanyData(user);

    return user;
  }

  async getUserByOtpCode(value: number): Promise<AuthUser> {
    const curenTime = moment().valueOf();
    let user = (await this.userService.findOne({
      filter: { 'userOtp.value': value, enable: true },
    })) as unknown as BaseUser;

    if (user instanceof Error)
      throw new UnauthorizedException(t('error.BAD_CREDENTIAL'));//TODO: change UnauthorizedException to HttpException with the correct message

    if (curenTime >= user?.userOtp?.expiredAt) {
      throw new UnauthorizedException(t('error.EXPIRE_TIME'));//TODO: change UnauthorizedException to HttpException with the correct message
    }
    if (user.category == UserCategory.CompanyUser)
      user = await this.setCompanyData(user);

    await this.userRepository.updateDeleteFeild({ _id: user?._id }, { userOtp: "" });

    return {
      ...user,
      accessToken: `${await this.generateToken({
        sub: user._id,
        email: user.email,
        tel: user?.tel,
      })}`,
    };
  }

  private async setCompanyData(user: CompanyEmployee) {
    const company = (await this.companyService.findOne({
      filter: { _id: user.company?._id },
    })) as unknown as Company;

    user.company = company;

    return user;
  }

  getRandomString(size: number, numberOnly: NumberConstructor) {
    size = size || 10;
    const chars = numberOnly
      ? '0123456789'
      : '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZ';
    let randomstring = '';
    for (let i = 0; i < size; i++) {
      const rnum = Math.floor(Math.random() * chars.length);
      randomstring += chars.substring(rnum, rnum + 1);
    }
    return randomstring;
  }

  private async verifyPassword(password: string, hashPassword: string) {
    if (!(await bcrypt.compare(password, hashPassword)))
      throw new UnauthorizedException(t('error.BAD_CREDENTIAL'));
  }

  private async generateToken(
    payload: JwtPayload,
    expirationTime = config.get('jwt.expiration'),
  ): Promise<string> {
    return await this.jwtService.signAsync(
      { sub: payload.sub, email: payload.email },
      {
        expiresIn: expirationTime,
        secret: config.get('jwt.secret'),
      },
    );
  }
}
