import { PartialType } from '@nestjs/swagger';
import { CreateLocationDto } from './create-location.dto';
import { IsBoolean, IsInt, IsOptional } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class UpdateLocationDto extends PartialType(CreateLocationDto) {

    @IsOptional()
    _id: string;
  
    @IsOptional()
    @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
    enable: boolean;
  
    @IsOptional()
    @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
    created_at:number;
  
    @IsOptional()
    @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
    updated_at: number;
}
