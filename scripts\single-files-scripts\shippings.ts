import readline from 'readline';
import { insertShippingsDataFile } from 'scripts/shippings/shipping-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données des addresses de livraison de Click CADYST, ce processus effacera la collection shippings.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertShippingsDataFile();
      console.log('Données des addresses de livraison insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();