import { ServerResponse } from "http";
import { IncomingMessage } from "http";
import { Params } from "nestjs-pino";
import { config } from "convict-config";

const consoleTransport = {
    target: 'pino-pretty',
    options: {
        level: 'trace',
        crlf: true,
        singleLine: true,
        translateTime: 'dd-mm-yyyy HH:MM:ss',
        ignore: 'pid,hostname',
        colorize: true,
        messageFormat: '{msg}'
    },
    level: 'trace'
};

const combineTransport = {
    target: 'pino-pretty',
    options: {
        level: 'trace',
        crlf: true,
        singleLine: true,
        translateTime: 'dd-mm-yyyy HH:MM:ss',
        ignore: 'pid,hostname',
        destination: './combine.log'
    },
    level: 'trace'
};

const errorTransport = {
    target: 'pino-pretty',
    options: {
        level: 'error',
        crlf: true,
        singleLine: true,
        translateTime: 'dd-mm-yyyy HH:MM:ss',
        ignore: 'pid,hostname',
        destination: './error.log'
    },
    level: 'error'
};
const fluentdTransport1 = {
    // target: 'pino-socket',
    target: '@lukadriel/pino-fluent',
    options: {
        prefix: `lapasta.apiV3.${config.get('env')}`,
        eventMode: "PackedForward",
        socket: {
            host: "**************",
            // host: config.get('fluentdHost'),
            port: 24224,
            timeout: 3000,
            reconnect: {
                backoff: 3,
                delay: 5
            },
        }
    },
    level: 'trace'
};

// const fluentdTransport = pino.transport<TransportOptions>({
//     // target: 'pino-pretty',
//     target: '@lukadriel/pino-fluent',
//     options: {
//         prefix: `mycimencam.apiV3.${config.get('env')}`,
//         // eventMode: "PackedForward",
//         // socket: {
//         //     host: config.get('fluentdHost'),
//         //     port: 24224,
//         //     timeout: 3000,
//         //     reconnect: {
//         //         backoff: 3,
//         //         delay: 500
//         //     },
//         // }
//     },
// });


export const pinoLoggerConfig: Params = {
    pinoHttp: {
        // Add HTTP context to http request
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        customProps: (req: IncomingMessage, res: ServerResponse) => ({ context: 'HTTP' }),
        transport: {
            targets: [consoleTransport, fluentdTransport1, combineTransport, errorTransport]
        },
        level: 'trace',
        serializers: {
            req: (req) => {
                return {
                    method: req.method,
                    url: req.url,
                    query: req.query,
                    params: req.params,
                    userAgent: req.headers['user-agent'],
                }
            },
            res: (res) => {
                return {
                    statusCode: res.statusCode
                }
            },
        },
    }
}
