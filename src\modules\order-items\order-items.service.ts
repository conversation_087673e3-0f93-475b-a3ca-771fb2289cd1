import { BadRequestException, HttpStatus, Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import { CompanyEmployee, User, UserCategory, UsersService } from '@la-pasta-module/users';
import { NotificationsService } from '@la-pasta-module/notifications/notifications.service';
import { Items, MarketPlaceCart, OrderItem } from './entities/order-item.entity';
import { NotificationCategory } from '@la-pasta-module/notifications/entities';
import { BaseService, getUser, setResponse, t } from '@la-pasta/common';
import { CreateOrderItemDto, UpdateOrderItemDto } from './dto';
import { OrderStatus } from '@la-pasta-module/order/entities';
import { OrderItemRepository } from './respository';
import { ClientProxy } from '@nestjs/microservices';
import { ObjectId } from 'bson';
import moment from 'moment';

@Injectable()
export class OrderItemsService extends BaseService {

  VAT = 0.1925;
  private readonly REJECTION_MESSAGES = {
    USER_CANCELLATION: "annulation à votre demande",
    BACKOFFICE_DEFAULT: "non-respect des conditions d'attribution",
  };

  constructor(
    private readonly oderItemRepository: OrderItemRepository,
    private notificationSrv: NotificationsService,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,
    private userSrv: UsersService

  ) {
    super(oderItemRepository)
  }

  private setDateFilter(query: QueryFilter) {
    query['created_at'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }


  async createOrder(newOrder: CreateOrderItemDto) {

    const requiredPoints = this.calculateRequiredPoints(newOrder?.cart);

    await this.verifyUserPoints(newOrder?.user?._id, requiredPoints);

    newOrder.appReference = await this.generateAppReference(newOrder?.user?._id);

    newOrder.status = OrderStatus.CREATED;

    newOrder.dates = { created: moment().valueOf() };

    if ('company' in newOrder?.user) {
      newOrder.company = newOrder?.user['company'];
      newOrder.company._id = new ObjectId(newOrder?.company?._id) as unknown as string;
    }

    newOrder.cart['amount'] = this.getTotalAmount(newOrder?.cart);

    const orderCreated = await this.create(newOrder);

    await this.decrementUserPoints(newOrder?.user?._id, requiredPoints);

    //TODO: check this service of notification
    this.notificationSrv.generateActionNotification(NotificationCategory.ORDER, (newOrder as unknown as OrderItem));

    newOrder.cart['items'] = [newOrder?.cart?.items] as unknown as Items;

    return setResponse(HttpStatus.CREATED, t('PAYMENT_RETAIL_INIT'), orderCreated.data,);

  }

  async validateOrder(id: string) {

    const order = await this.findOne({ filter: { _id: id } }) as unknown as OrderItem;

    if (!order)
      return setResponse(HttpStatus.NOT_FOUND, t('REWARD_REQUEST_NOT_FOUND'));

    if (order.status === OrderStatus.VALIDATED)
      return setResponse(HttpStatus.BAD_REQUEST, t('REWARD_REQUEST_ALREADY_VALIDATED'));

    order.status = OrderStatus.VALIDATED;
    order.validation = {
      user: getUser(),
      date: moment().valueOf(),

    };

    // await this.loyaltyProgramSrv?.countAndAdjustPointByOrderOfMAketPlace(order);

    await this.update({ _id: id }, order);

    return setResponse(HttpStatus.OK, t('REWARD_REQUEST_VALIDATED'));

  }

  async handleOrderAction(orderItemId: string, body: UpdateOrderItemDto) {

    const orderItem: OrderItem = await this.findOne({ filter: { _id: orderItemId } }) as unknown as OrderItem;

    const { reason, actionType, isMobile } = body;
    try {
      const requiredPoints = this.calculateRequiredPoints(orderItem?.cart);

      const statusOrderItem = actionType === 'reject' ? OrderStatus.FAILD : OrderStatus.REJECTED;

      const handleOrderItem = {
        ...orderItem,
        status: statusOrderItem,
        validation: {
          rejectReason: this.messageRejection(isMobile, orderItem, reason),
          user: getUser(),
          date: moment().valueOf(),
        }
      };

      await this.userSrv.updateUserValidePoints(orderItem?.user?._id, requiredPoints);
      await this.update({ _id: orderItemId }, handleOrderItem);
      await this.sendRejectionNotification(handleOrderItem, isMobile);

      return {
        message: `Votre demande de récompense a été ${actionType === 'annuler' ? 'annulée' : 'rejetée'}.`,
        status: HttpStatus.OK
      };
    } catch (error) {
      this.logger.error('Error rejecting reward request:', error);
      throw new InternalServerErrorException(t('ERROR_REJECT_ORDER_FAILED'));
    }
  }

  private messageRejection(isMobile: boolean, orderItem: OrderItem, reason?: string) {
    if (isMobile) {
      return reason || this.REJECTION_MESSAGES.USER_CANCELLATION;
    } else {
      return reason || this.REJECTION_MESSAGES.BACKOFFICE_DEFAULT;
    }
  }

  async generateAppReference(storeRef: string) {
    const dateStr = moment().format('YYMMDDHHmmss');
    const random = Math.floor(Math.random() * 999) + 100;
    const mapped = {
      L10: 'BON',
      K10: 'KRIB'
    };

    const currAppRef = `${mapped[storeRef] ?? 'CUST'}${dateStr}`.substr(0, 12) + `${random}`;
    const document = await this.oderItemRepository.findOne({ filter: { appReference: currAppRef } });

    // AppReference déjà utilisé, on relance la fonction
    if (document)
      return await this.generateAppReference(storeRef);


    return currAppRef;
  }

  private getTotalAmount(cart: MarketPlaceCart) {
    const TTC = cart.quantity * cart.items.price;
    const VAT = TTC * this.VAT;
    const HT = TTC - VAT;

    return { TTC, VAT, HT, discount: { points: TTC } }
  }

  private async decrementUserPoints(userId: string, pointsChange: number): Promise<void> {
    const user = await this.userSrv.findOne({ filter: { _id: userId } });
    user.points.validate -= pointsChange;
    await this.userSrv.update({ _id: userId }, user);
  }
  private calculateRequiredPoints(cart: MarketPlaceCart): number {
    return cart?.quantity * cart.items?.price;
  }

  private async verifyUserPoints(userId: string, requiredPoints: number): Promise<void> {
    const user = await this.userSrv.findOne({ filter: { _id: userId } });
    if (!user || user.points < requiredPoints) {
      throw new BadRequestException('Insufficient points');
    }
  }

  async getAllOrderItem(query: QueryOptions, user: User) {
    try {
      if ('startDate' in query.filter || 'endDate' in query.filter) {
        this.setDateFilter(query.filter);
      }

      if ('status' in query.filter && query.filter.status.includes('$')) {
        try {
          query.filter.status = JSON.parse(query?.filter?.status);
        } catch (parseError) {
          console.error('Error parsing status filter:', parseError);
          throw new Error('Invalid status filter format');
        }
      }

      if ([UserCategory.CompanyUser, UserCategory.Particular].includes(user?.category)) {
        const value = user?.category === UserCategory.CompanyUser ? (user as CompanyEmployee)?.company?._id : user?._id;
        query.filter[user?.category === UserCategory.CompanyUser ? 'company._id' : 'user._id'] = {
          $in: [new ObjectId(value), value.toString()]
        };
      }
      const result = await this.findAll(query);
      return result;
    } catch (error) {
      console.error('Error fetching order items:', error);
      throw new Error('An error occurred while fetching order items');
    }
  }

  private async sendRejectionNotification(orderItem: OrderItem, isMobile: boolean): Promise<void> {
    let rejectionMessage: string;

    if (isMobile) {
      rejectionMessage = `${orderItem?.user?.lastName}, votre demande d'annulation de récompense a été prise en compte.`;
    } else {
      const rejectionReason = orderItem?.validation?.reason || this.REJECTION_MESSAGES.BACKOFFICE_DEFAULT;
      rejectionMessage = `${orderItem?.user?.lastName}, nous regrettons de vous informer que votre demande de récompense n'a pas été acceptée en raison de : ${rejectionReason}`;
    }

    await this.sendSmsNotification(orderItem?.user?.tel, rejectionMessage);
  }

  private async sendSmsNotification(tel: number, message: string): Promise<void> {
    this.queueClient.emit('sms_received', {
      receiver: tel,
      message: message
    })
  }

}
