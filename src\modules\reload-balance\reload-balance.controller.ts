import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ReloadBalanceService } from './reload-balance.service';
import { CreateReloadBalanceDto } from './dto/create-reload-balance.dto';
import { JwtGuard } from '@la-pasta-module/auth';

@Controller('reload-balance')
export class ReloadBalanceController {
  constructor(private readonly reloadBalanceService: ReloadBalanceService) { }

  @Post()
  @UseGuards(JwtGuard)
  create(@Body() createReloadBalanceDto: CreateReloadBalanceDto) {
    return this.reloadBalanceService.initiateReloadBalance(createReloadBalanceDto);
  }

  @Post('verify-payment-status')
  @UseGuards(JwtGuard)
  verifypaymentStatusToOperator(@Body() body: any) {
    return this.reloadBalanceService.verifyPaymentStatusToOperator(body);
  }

  @Get()
  findAll(@Query() query: QueryOptions) {
    return this.reloadBalanceService.getReloadBalance({ filter: query })
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.reloadBalanceService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

}
