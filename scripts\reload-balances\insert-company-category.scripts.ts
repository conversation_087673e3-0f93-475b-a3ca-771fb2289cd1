import { Company } from '@la-pasta-module/companies';
import { ReloadBalance } from '@la-pasta-module/reload-balance/entities/reload-balance.entity';
import { MongoDatabase } from '@la-pasta/database';
import { ObjectId } from 'bson';
import { setLoader, stopLoader } from 'scripts/common';

const database = MongoDatabase.getInstance();
const collectionName = 'reload_balances';
const collectionCompanies = 'companies';

export async function updateCompaniesCategoriesInReloadBalance() {
    try {

        setLoader('Insertion des Categories de Compagnie dans la recharge de compte\n');
        let count = 0;

        const db = await database.getDatabase();

        const companiesAggrate = await db.collection(collectionName).aggregate([
            {
                '$match': {
                    'company.category': {
                        '$exists': false
                    }
                }
            }, {
                '$group': {
                    '_id': '$company._id',
                    'total': {
                        '$sum': 1
                    }
                }
            }
        ]).toArray();


        for (const elt of companiesAggrate) {
            const company = await db.collection(collectionCompanies).findOne({ _id: new ObjectId(elt?._id?.toString()) });
            if (company)
                await db.collection(collectionName).updateMany({ "company._id": elt?._id }, { $set: { 'company.category': company?.category } });
        }

        console.info(`la mise a jour c'est effectuer pour ${count} recharges`)
    }
    catch (error) {
        console.error(error);
    }
    finally {
        stopLoader(true);
    }
}