import { Module } from '@nestjs/common';
import { ComputePriceService } from './compute_price.service';
import { ComputePriceController } from './compute_price.controller';
import { OptionsModule } from '@la-pasta-module/options/options.module';
import { LoyaltyProgramModule } from '@la-pasta-module/loyalty-program/loyalty-program.module';

@Module({
  controllers: [ComputePriceController],
  providers: [ComputePriceService],
  imports: [OptionsModule, LoyaltyProgramModule],
  exports: [ComputePriceService],
})
export class ComputePriceModule { }
