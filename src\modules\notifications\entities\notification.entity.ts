import { Feedback, statusFeetback } from "@la-pasta-module/feedbacks/entities";
import { Order, OrderStatus } from "@la-pasta-module/order";
import { User } from "@la-pasta-module/users";
import { getUser } from "@la-pasta/common";
import { config } from "convict-config";

export class BaseNotification {
    _id?: object | string;
    feedback?: Feedback;
    emailAdmin?: string;
    message: string;
    status: number;
    isGeneralNotif?: boolean;
    title: string;
    img?: string;
    redirect?: string;
    category: NotificationCategory;
    dates: {
        created: Date | number;
        read?: Date;
    };
    smiley?: string;

}

export class OrderNotification extends BaseNotification {
    userId: string;

    constructor(order: Order) {
        super();
        this.title = `Commande ${this.getStatusLabel(order)}`;
        this.message = `Votre commande N°${order?.appReference} a été ${this.getStatusLabel(order)}\n, ${config.get('companyName')} vous remercie 😁`;
        this.category = NotificationCategory.ORDER;
        this.redirect = `${config.get('detailOrderBaseUrl')}${order?._id}`;
        this.smiley = '😊';
        this.userId = this.getUserToSendNotif(order);
        this.dates = { created: (new Date()).getTime() };
        this.status = messageType.CREATE;

    }

    getStatusLabel(order: Order) {
        if (order?.status === OrderStatus.PAID && order?.nberModif > 0) return 'modifier';

        const label = {
            [OrderStatus.CANCELLED]: 'annulée',
            [OrderStatus.PAID]: 'enregistrée',
            [OrderStatus.CREDIT_REJECTED]: 'renvoyée',
            [OrderStatus.VALIDATED]: 'validée'
        }

        return label[order?.status];
    }

    getUserToSendNotif(order: Order): string {
        const isOrderFinalized = [OrderStatus.CANCELLED, OrderStatus.VALIDATED, OrderStatus.CREDIT_REJECTED].includes(order?.status);
        return isOrderFinalized ? order?.user?._id?.toString() : (getUser())?._id?.toString();
    }

}
export class FeedbackNotification extends BaseNotification {
    userId: string;

    constructor(feedback: Feedback) {
        super();
        this.title = `Réclamation ${this.getStatusLabelClaim(feedback?.status)}`;
        this.message = `Votre réclémation a été ${this.getStatusLabelClaim(feedback?.status)}\n, ${config.get('companyName')} vous remercie 😁`;
        this.category = NotificationCategory.FEEDBACK;
        this.smiley = '😊',
            this.dates = { created: (new Date()).getTime() };
        this.userId = (getUser())?._id?.toString();
        this.status = messageType.CREATE
    }

    getStatusLabel(status: OrderStatus) {
        const label = {
            [OrderStatus.CANCELLED]: 'annuler',
            [OrderStatus.PAID]: 'enregistrer',
            [OrderStatus.CREDIT_REJECTED]: 'renvoyer',
            [OrderStatus.VALIDATED]: 'valider'
            // [OrderStatus.PAID]: 'modifier', :: TODO: Add update state
        }

        return label[status];
    }

    getStatusLabelClaim(status: statusFeetback) {
        const label = {
            [statusFeetback.CREATED]: 'envoyée',
            [statusFeetback.TREAT]: 'traitée'
        }

        return label[status];
    }
}

export enum messageType {
    CREATE = 100,
    READ = 200,
    DELETE = 300
}

export enum NotificationCategory {
    FEEDBACK = 100,
    ORDER = 200,
    GENERAL = 300
}