import { Company } from "@la-pasta-module/companies/entities";
import { OrderStatus } from "@la-pasta-module/order/entities/order.entity";
import { Cart } from "@la-pasta-module/price/compute_price/entities";
import { QrCodeData } from "@la-pasta-module/qr-code-management/entities/qr-code.entity";
import { User } from "@la-pasta-module/users/entities";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty } from "class-validator";

export class CreateOrderSupplierDto {

  @IsNotEmpty()
  supplier: Partial<Company>;

  @IsNotEmpty()
  cart: Cart;

  @IsNotEmpty()
  user: Partial<User>;

  status?: OrderStatus;

  appReference?: string;

  validation?: {
    user: Partial<User>;
    date: number;
    raison: string;
  }

  dates?: {
    created?: number;
    paid?: number;
    validated?: number;
  }

  @ApiProperty({ type: () => [QrCodeData] })
  @Type(() => QrCodeData)
  qrCodeData: QrCodeData[];
}
