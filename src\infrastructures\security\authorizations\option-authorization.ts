import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';
import { OptionAction } from '@la-pasta-module/options/actions';

class OptionAuthorization implements AuthorizationInterface {
    support(user: User, permission: string): boolean {
        return ([
            OptionAction.CREATE,
            OptionAction.DELETE,
            OptionAction.UPDATE,
            OptionAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
    }

    authorise(user: User, permission: string): boolean {
        return user.roles.includes(UserRole.BACKOFFICE);
    }
}

export const OptionAuthorizationInstance = new OptionAuthorization();