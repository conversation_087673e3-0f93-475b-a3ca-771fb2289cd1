import { User } from "@la-pasta-module/users";
import { Order } from "../entities";
import { ReloadBalance } from "@la-pasta-module/reload-balance/entities/reload-balance.entity";
import { MobileMoneyEventData } from "./mobile-money.event";

export class M2UEvent {

    order: { _id: string; appReference: string; user: User };
    paymentId: string;

    constructor(order: Order) {
        this.order = {
            _id: order._id,
            appReference: order.appReference,
            user: {
                _id: order?.user?._id,
                email: order?.user?.email,
                firstName: order?.user.firstName,
                lastName: order?.user.lastName
            },
        };
        this.paymentId = order.payment?.paymentId;
    }
}

export class M2UEventReloadBalance extends MobileMoneyEventData {

    reloadBalance: Partial<ReloadBalance>;
    paymentId: string;
    paymentInfo: any;
    Amount: string;
    SourceWallet: string;
    OTP: string;

    constructor(reloadBalance: Partial<ReloadBalance>) {
        super({ payment: reloadBalance?.payment, amount: reloadBalance?.payment?.amount });
        this.reloadBalance = {
            _id: reloadBalance?._id.toString(),
            user: reloadBalance?.user
        };
        this.Amount = `${reloadBalance.payment.amount}`;
        this.SourceWallet = `${reloadBalance.paymentInfo.SourceWallet}`;
        this.OTP = `${reloadBalance.paymentInfo.OTP}`;
    }
}