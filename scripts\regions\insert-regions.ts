import readline from 'readline';
import { insertCommercialRegions } from './regions';

(async () => {
    const prompt = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    console.log(`* Vous êtes sur le point inséré les region Commerciale, dans la base de données. \n`);

    prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {
        if (answer == 'y') {
            await insertCommercialRegions();
        }

        prompt.close();
    })

    prompt.on('close', () => {
        process.exit()
    })
})();