import { Response } from 'express';
import {
  Get,
  Res,
  Body,
  Post,
  Param,
  Inject,
  HttpCode,
  Controller,
  forwardRef
} from '@nestjs/common';
import {
  AuthUser,
  UsersService,
  CreateUserDto
} from '@la-pasta-module/users';
import { AuthService } from './auth.service';
import {
  ApiBody,
  ApiTags,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiUnauthorizedResponse
} from '@nestjs/swagger';
import {
  CredentialDto,
  CredentialOtpDto,
  NewPasswordDto,
  NewToken,
  RefreshTokenDto,
  RegisterUserDto,
  ResetPasswordDto,
  ResetToken,
} from './dtos';
import { RegisterUserParticularDto } from './dtos/register-user-particular.dto';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    @Inject(forwardRef(() => UsersService))
    private readonly userService: UsersService,
  ) { }

  @ApiOkResponse({
    description: 'The user has successfully been authenticated',
    type: AuthUser,
  })

  @ApiUnauthorizedResponse({
    description: 'Incorrect credentials send to the server',
  })

  @ApiBody({ type: CredentialOtpDto })
  @Post('login')
  @HttpCode(200)
  login(@Body() credential: CredentialDto): Promise<AuthUser> {
    return this.authService.login(credential);
  }

  @ApiBody({ type: CredentialDto })
  @Post('loginOtp')
  @HttpCode(200)
  loginOtp(@Body() credential: CredentialOtpDto) {
    return this.authService.loginOtp(credential);
  }

  @ApiBody({ type: CredentialDto })
  @Post('verify-otp')
  @HttpCode(200)
  verifyOtp(@Body() body: any): Promise<AuthUser> {
    return this.authService.verifyOtp(body);
  }

  @ApiCreatedResponse({ description: 'The user has successfully been created' })
  @ApiBody({ type: CreateUserDto })
  @Post('register')
  @HttpCode(201)
  register(@Body() createUser: RegisterUserDto) {
    return this.userService.createUser(createUser);
  }

  @ApiCreatedResponse({ description: 'The user has successfully been created' })
  @ApiBody({ type: CreateUserDto })
  @Post('register-particular')
  @HttpCode(201)
  registerParticular(@Body() createUser: RegisterUserParticularDto) {
    return this.userService.createIndirectUser(createUser);
  }

  @Post('reset-password')
  @HttpCode(200)
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.sendResetPasswordMail(resetPasswordDto);
  }

  @Get('change-password/:token')
  async changePassword(
    @Param() resetToken: ResetToken,
    @Res() response: Response,
  ) {
    await this.authService.getResetPasswordView(resetToken.token);
    return response.render('reset-password');
  }

  @Post('change-password/:token')
  async setNewPassword(
    @Body() newPasswordDto: NewPasswordDto,
    @Param() resetToken: ResetToken,
    @Res() response: Response,
  ) {
    const data = await this.authService.setNewPassword(
      newPasswordDto,
      resetToken.token,
    );
    return response.render('reset-password', { data });
  }

  @ApiOkResponse({ description: 'New access token generated', type: NewToken })
  @ApiBody({ type: RefreshTokenDto })
  @Post('refresh-token')
  @HttpCode(200)
  async refreshToken(@Body() token: RefreshTokenDto) {
    return await this.authService.refeshToken(token);
  }
}
