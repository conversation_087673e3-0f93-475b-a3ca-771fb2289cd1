import { getUser } from '@la-pasta/common';
import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { UnitService } from './unit.service';
import { CreateUnitDto } from './dto/create-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';
import { setResponseController } from 'src/common/helpers';
import { ApiTags } from '@nestjs/swagger';
import { Permission } from '@la-pasta/infrastructures/security';
import { UnitAction } from './actions';
import { JwtGuard } from '@la-pasta-module/auth';

@ApiTags('Units')
@Controller('units')
export class UnitController {
  constructor(private readonly unitService: UnitService) { }

  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createUnitDto: CreateUnitDto) {
    Permission.unitAuthorization(getUser(), UnitAction.CREATE);

    return this.unitService.create(createUnitDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.unitAuthorization(getUser(), UnitAction.VIEW);

    const data = await this.unitService.findAll({ filter: query });
    return setResponseController(data)
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.unitAuthorization(getUser(), UnitAction.VIEW);

    const data = await this.unitService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateUnitDto: UpdateUnitDto) {
    Permission.unitAuthorization(getUser(), UnitAction.UPDATE);

    const data = await this.unitService.update({ _id: id }, updateUnitDto);
    return setResponseController(data);
  }
}
