import { Test, TestingModule } from '@nestjs/testing';
import { PackagingsController } from './packagings.controller';
import { PackagingsService } from './packagings.service';

describe('PackagingsController', () => {
  let controller: PackagingsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PackagingsController],
      providers: [PackagingsService],
    }).compile();

    controller = module.get<PackagingsController>(PackagingsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
