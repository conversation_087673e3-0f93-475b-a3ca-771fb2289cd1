import { fetchAuthorizations } from './fetch-authorizations';
import { MongoDatabase } from '@la-pasta/database';
import { forEach } from 'lodash';
import readline from 'readline';
import { dropCollection, setLoader, stopLoader, table } from 'scripts/common';

const database = MongoDatabase.getInstance();
const collectionName = 'authorizations';

(async () => {
  const appAuthorizations = await fetchAuthorizations();

  console.log('* Les autorisations suivantes seront insérer dans la collection authorizations:',);

  table(
    appAuthorizations.map((authorization) => {
      return {
        module: authorization.label,
        actions: authorization.actions.join(', '),
      };
    }),
  );

  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  prompt.question(
    "Voulez-vous procéder à l'insertion des autorisations (y/n) ? : ",
    async (answer) => {
      if (answer == 'y') {
        try {
          await dropCollection(collectionName);

          setLoader('Insertion des autorisations\n');

          const insertedAuthorizations = await (await database.getDatabase())
            .collection(collectionName)
            .insertMany(appAuthorizations);

          stopLoader(insertedAuthorizations);
        } catch (error) {
          console.error(error);
        }
      }

      prompt.close();
    },
  );

  prompt.on('close', () => {
    process.exit();
  });
})();
