import { HttpException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { BaseService, getUser, setResponse, t } from '@la-pasta/common';
import { CreateWholeSaleDto } from './dto/create-whole-sale.dto';
import { WholeSaleRepository } from './repository';
import { User } from '@la-pasta-module/users';

@Injectable()
export class WholeSaleService extends BaseService {

  constructor(
    private readonly wholeSaleRepository: WholeSaleRepository
  ) {
    super(wholeSaleRepository);
  }

  async createWholeSale(wholeSale: CreateWholeSaleDto) {
    await this.verifyIfWholeSaleExist({ $or: [{ name: wholeSale?.name }, { tel: +wholeSale?.tel }] });
    try {
      const user: User = getUser();

      const document = {
        ...wholeSale,
        associatedDonutAnimator: {
          _id: user?._id,
          lastName: user?.lastName ?? user?.firstName,
          email: user?.email,
          tel: Number(user?.tel)
        },
      }
      const response = await this.create(document);
      return setResponse(HttpStatus.CREATED, t('USER_CREATED'), response?.data);
    } catch (error) {
      return error
    }
  }

  async verifyIfWholeSaleExist(query: QueryFilter) {
    const res = await this.findOne({ filter: query });
    if (!(res instanceof NotFoundException)) throw new HttpException(t('error.WHOLE_SALE_EXIST'), HttpStatus.BAD_REQUEST);
  }
}

