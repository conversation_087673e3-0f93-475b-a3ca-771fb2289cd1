import { Body, Controller, Get, Param, Patch, Query, UseGuards, } from '@nestjs/common';
import { RetrievementsService } from './retrievements.service';
import { UpdateRetrievementDto } from './dto/update-retrievement.dto';
import { JwtGuard } from '@la-pasta-module/auth';

@Controller('retrievements')
export class RetrievementsController {
  constructor(private readonly retrievementsService: RetrievementsService) { }


  @Get()
  async findAll(@Query() query: QueryFilter) {
    const data = await this.retrievementsService.getRetrievements({ filter: query });
    return data;
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query?.keyForFilters;
    return this.retrievementsService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() body: UpdateRetrievementDto,) {
    return await this.retrievementsService.updateRetrievement(id, body)
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.retrievementsService.findOne({ filter: { _id: id } });
  }


}
