import { HttpException, HttpStatus } from '@nestjs/common';
import { PaymentReloadBalance, ReloadBalance } from '@la-pasta-module/reload-balance/entities/reload-balance.entity';

export class AfrilandPaymentReloadBalanceEvent {
  afrilandKey: string;
  payment: PaymentReloadBalance;

  constructor(reloadBalance: ReloadBalance) {
    this.afrilandKey = this.getAfrilandKey(reloadBalance),
      this.payment = reloadBalance.payment
  }

  private getAfrilandKey(reloadBalance: ReloadBalance) {
    const afrilandKey = reloadBalance?.user?.afrilandKey;

    if (!afrilandKey || afrilandKey == '') {
      throw new HttpException("Cet utilisateur n'as pas de clé Afriland", HttpStatus.BAD_REQUEST);
    }

    return afrilandKey;
  }
}