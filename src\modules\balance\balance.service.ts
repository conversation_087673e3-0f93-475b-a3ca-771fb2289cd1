import { HttpStatus, Inject, Injectable, NotFoundException, forwardRef } from '@nestjs/common';
import { ClientBalance } from './entities/balance.entity';
import { BalanceRepository } from './repository';
import { BaseService, t } from '@la-pasta/common';
import { CompaniesService } from '@la-pasta-module/companies';

@Injectable()
export class BalanceService extends BaseService {

  constructor(
    @Inject(forwardRef(() => CompaniesService))
    private companiesSrv: CompaniesService,
    private balanceRepo: BalanceRepository,
  ) {
    super(balanceRepo)
  }

  async saveDataForBalanceClient(clientBalances: ClientBalance[]) {
    const unSaveData = [];
    try {
      for (const clientBalance of clientBalances) {
        const { company: { erpSoldToId } } = clientBalance;

        const verify = await this.verifyIfBalanceClientexists(erpSoldToId)
        if (!verify) {
          const company = await this.companiesSrv.findOne({ filter: { erpSoldToId: erpSoldToId }, projection: { erpSoldToId: 1, _id: 1, rrcm: 1, name: 1 } });
          if (company instanceof NotFoundException) { unSaveData.push(clientBalance); continue; }

          await this.create({ ...clientBalance, company });
          continue;
        }

        await this.update({ "company.erpSoldToId": clientBalance.company.erpSoldToId }, clientBalance);

      }
      return { status: HttpStatus.OK, message: `${await t('UPDATED')}: ${(clientBalances.length - unSaveData.length)}/${clientBalances.length}` };
    } catch (error) {
      return { status: HttpStatus.NOT_MODIFIED, message: `${await t('error.OCCURRED_ERROR')}  \n${JSON.stringify(error)}` }
    }


  }

  private async verifyIfBalanceClientexists(erpSoldToId: string) {
    const clientBalance = await this.findOne({ filter: { "company.erpSoldToId": erpSoldToId } });

    if (clientBalance instanceof NotFoundException) return false;

    return true;
  }
}
