import { IsNotEmpty } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";
import { User } from "@la-pasta-module/users/entities";
import { Cart } from "@la-pasta-module/price/compute_price";
import { OrderStatus } from "@la-pasta-module/order/entities";
import { Company } from "@la-pasta-module/companies";
import { MarketPlaceCart } from "../entities/order-item.entity";

export class CreateOrderItemDto {

    points?: Point

    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    user: User;

    @IsNotEmpty()
    cart: MarketPlaceCart;

    status?: OrderStatus;

    appReference?: string;

    validation?: {
        user: Partial<User>;
        date: number;
        raison: string;
    }

    dates?: {
        created?: number;
        paid?: number;
        validated?: number;
    }

    company?: Partial<Company>


}
