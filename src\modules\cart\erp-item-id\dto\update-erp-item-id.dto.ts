import { PartialType } from '@nestjs/swagger';
import { i18nValidationMessage } from 'nestjs-i18n';
import { CreateErpItemIdDto } from './create-erp-item-id.dto';
import { IsBoolean, IsInt, IsOptional } from 'class-validator';

export class UpdateErpItemIdDto extends PartialType(CreateErpItemIdDto) {
    // @IsOptional()
    // _id: string;
  
    @IsOptional()
    @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
    enable: boolean;
  
    @IsOptional()
    @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
    created_at:number;
  
    @IsOptional()
    @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
    updated_at: number;
}
