import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { BaseService, ReportingHelpers, convertFilter, convertParams } from '@la-pasta/common';
import { RetrievementRepository } from '@la-pasta-module/retrievements/repository/retrievements.repository';
import moment from 'moment';



@Injectable()
export class RetrievementReportingService extends BaseService {
  constructor(public retrievRepo: RetrievementRepository) {
    super(retrievRepo);
  }


  async getAeRepartition(query: QueryFilter) {
    try {
      query = this.convertFilter(query,);
      query['dates.created'] = query?.created_at;
      delete query?.created_at;


      const totaldriver = await this.retrievRepo.baseAggregation({ filter: query }, 'driverName');
      const totalproduct = await this.retrievRepo.baseAggregation({ filter: query }, 'details.item.label');


      return { totaldriver, totalproduct };
    } catch (error) {
      return error;
    }
  }

  async getEvolution(query: QueryFilter) {
    try {

      query = this.convertFilter(query);
      query['dates.created'] = query?.created_at;
      delete query?.created_at;
      const data = await this.retrievRepo.getEvolutionAes(query);

      const dataYear = ReportingHelpers.generateChartYearDataForRetails(data);

      return { dataYear };
    } catch (error) {
      throw error;
    }

  }

  convertFilter(fields: QueryFilter): QueryFilter {
    const { filter } = convertParams({ filter: fields });

    const year = parseInt(filter.startDate?.split('-')[0]);
    const month = parseInt(filter.startDate?.split("-")[1]);
    const startDay = parseInt(filter.startDate?.split("-")[2]);
    const endDay = parseInt(filter.endDate?.split("-")[2]);

    if (month == 2 && ((0 == year % 4) && (0 != year % 100) || (0 == year % 400)) && (startDay >= 29 || endDay >= 29)) {
      throw new HttpException(`The Query date format is invalid; The number of days is incorrect based on the month.`, HttpStatus.BAD_REQUEST);
    }

    if (moment(filter.startDate).valueOf() > moment(filter?.endDate).valueOf()) {
      throw new HttpException(`The start date should not be greater than end date.`, HttpStatus.BAD_REQUEST);
    }

    filter['actualShipDate'] = {
      $gte: moment((filter?.startDate || new Date()), 'YYYY-MM-DD').startOf(filter?.startDate ? 'day' : 'year').valueOf(),
      $lte: moment(filter?.endDate || new Date(), 'YYYY-MM-DD').endOf(filter?.startDate ? 'day' : 'year').valueOf()
    }


    delete filter?.startDate;
    delete filter?.endDate;

    return filter;
  }
}
