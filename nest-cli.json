{"collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "plugins": ["@nestjs/swagger/plugin"], "assets": [{"include": "**/*.html", "outDir": "dist/src", "watchAssets": true}, {"include": "../views/*.hbs", "outDir": "dist/views", "watchAssets": true}, {"include": "../public/**/*", "outDir": "dist/public", "watchAssets": true}, {"include": "i18n/**/*", "outDir": "dist/src", "watchAssets": true}, {"include": "env/**/*", "outDir": "dist/src", "watchAssets": true}]}}