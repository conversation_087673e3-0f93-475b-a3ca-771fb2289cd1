// const fs = require('fs').promises;
// const path = require('path');

// // Répertoire contenant les fichiers JSON
// const inputDirectory = '.';

// // Fichier de sortie
// const outputFile = 'fichier_sortie.txt';

// async function processJsonFiles() {
//     try {
//         // Lire le contenu du répertoire
//         const files = await fs.readdir(inputDirectory);

//         // Filtrer pour ne garder que les fichiers JSON
//         const jsonFiles = files.filter(file => path.extname(file).toLowerCase() === '.json');

//         // Préparer le contenu à écrire
//         let outputContent = '';

//         // Traiter chaque fichier JSON
//         for (const file of jsonFiles) {
//             const filePath = path.join(inputDirectory, file);
//             const fileContent = await fs.readFile(filePath, 'utf8');

//             // Ajouter le nom du fichier et son contenu au résultat
//             outputContent += `${path.basename(file, '.json')}:\n`;
//             outputContent += `${JSON.stringify(fileContent)}\n\n`;
//         }

//         // Écrire le contenu dans le fichier de sortie
//         await fs.writeFile(outputFile, outputContent);

//         console.log(`Les données ont été écrites dans ${outputFile}`);
//     } catch (error) {
//         console.error('Une erreur est survenue:', error);
//     }
// }

// processJsonFiles();

// import { MongoDatabase } from '@la-pasta/database';
const MongoDatabase = require('../src/database/database-mongodb');
const fs = require('fs').promises;

// URL de connexion à votre base de données MongoDB
const url = 'mongodb://localhost:27017';

// Nom de votre base de données
const dbName = 'mycimencam-v3';

// Fichier de sortie
const outputFile = 'output.txt';

async function getLastTenDocuments() {
  const database = MongoDatabase.getInstance();

  try {
    // await client.connect();
    console.log('Connecté à la base de données MongoDB');

    const db = await database.getDatabase();

    // Obtenir la liste des collections
    const collections = await db.listCollections().toArray();

    let outputContent = '';

    // Pour chaque collection
    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name;
      const collection = db.collection(collectionName);

      // Récupérer les 10 derniers documents
      const documents = await collection.find().sort({ _id: -1 }).limit(5).toArray();

      // Ajouter le nom de la collection et les documents au contenu de sortie
      outputContent += `${collectionName}:\n`;
      outputContent += JSON.stringify(documents, null, 2);
      outputContent += '\n\n';
    }

    // Écrire le contenu dans le fichier de sortie
    await fs.writeFile(outputFile, outputContent);

    console.log(`Les données ont été écrites dans ${outputFile}`);

  } catch (error) {
    console.error('Une erreur est survenue:', error);
  } finally {
    await client.close();
  }
}

getLastTenDocuments();