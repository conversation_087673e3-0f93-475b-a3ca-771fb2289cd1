import { BaseRepository } from "@la-pasta/common";
import { Injectable } from "@nestjs/common";

@Injectable()
export class DirectionRepository extends BaseRepository {

  constructor() {
    super();
  }

  async getDirections() {
    const database = await this.database.getDatabase();

    const directions = await database.collection('directions').find({}).toArray();

    for await (const direction of directions) {
      direction.services = await database.collection('services').find({ "directionId": direction._id.toString() }).toArray();

      for await (const service of direction.services) {
        service.fonctions = await database.collection('fonctions').find({ "serviceId": service._id.toString() }).toArray();
      }

      direction.fonctions = await database.collection('fonctions').find({ "directionId": direction._id.toString(), "serviceId": null }).toArray();
    }

    return directions;
  }

}