import { Controller, Get, Post, Body, Patch, Param, Query, HttpCode, UseGuards, Inject } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CompaniesService } from './companies.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { CreateCompanyUserDto } from './dto/create-company-user.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { getUser, setResponseController } from '@la-pasta/common';
import { Permission } from '@la-pasta/infrastructures/security';
import { CompanyAction } from './actions';
import { JwtGuard } from '@la-pasta-module/auth';
import { UpdateLogoDto } from './dto';
import { Order } from '@la-pasta-module/order';
import { CompanyEmployee, User } from '@la-pasta-module/users';

@ApiTags('Companies')
@Controller('companies')
export class CompaniesController {
  constructor(
    private readonly companiesService: CompaniesService
  ) { }

  @HttpCode(201)
  @UseGuards(JwtGuard)
  @Post()
  async create(@Body() createCompanyDto: CreateCompanyDto) {
    Permission.companyAuthorization(getUser(), CompanyAction.CREATE);

    const data = await this.companiesService.createCompany(createCompanyDto);
    return setResponseController(data)
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.companyAuthorization(getUser(), CompanyAction.VIEW);
    const data = await this.companiesService.findAll({ filter: query });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get('particular-suppliers')
  async getUsersCompanies(@Query() query: QueryFilter) {
    Permission.companyAuthorization(getUser(), CompanyAction.VIEW);
    const data = await this.companiesService.getUsersCompanies(getUser(), { filter: query });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get('balance')
  async getBalance(@Query() query: QueryFilter) {
    // Permission.companyAuthorization(getUser(), CompanyAction.VIEW);

    const res = await this.companiesService.getBalanceCompany(query)
    return setResponseController(res);
  }

  @UseGuards(JwtGuard)
  @Get('filters-elements')
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.companiesService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.companyAuthorization(getUser(), CompanyAction.VIEW);

    const data = await this.companiesService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get(':id/users')
  async findUsers(@Param('id') id: string, @Query() query: QueryFilter) {
    Permission.companyAuthorization(getUser(), CompanyAction.VIEW_USERS);

    const data = await this.companiesService.findUsers(id, { ...query, projection: { password: 0 } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Post(':id/users')
  async addUser(@Param('id') id: string, @Body() createUserData: CreateCompanyUserDto | CompanyEmployee) {
    Permission.companyAuthorization(getUser(), CompanyAction.ADD_USER);

    const data = await this.companiesService.addUser(id, createUserData);
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateCompanyDto: UpdateCompanyDto) {
    Permission.companyAuthorization(getUser(), CompanyAction.UPDATE, { _id: id });

    const data = await this.companiesService.updateCompanyInUser(id, updateCompanyDto);
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id/logo')
  async updateLogo(@Param('id') id: string, @Body() updateLogoDto: UpdateLogoDto) {
    Permission.companyAuthorization(getUser(), CompanyAction.UPDATE, { _id: id });

    const data = await this.companiesService.saveLogoCompany(id, updateLogoDto);
    return setResponseController(data);
  }
}