import { MongoDatabase } from '@la-pasta/database';
import { GlobalShipping, ParticularShipping, Shipping } from '@la-pasta-module/price/shipping';
import { getStores } from 'scripts/stores';
import { faker } from '@faker-js/faker';
import { UserCategory } from '@la-pasta-module/users';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import moment from 'moment';

const database = MongoDatabase.getInstance();
const collectionName = 'shippings';

const endRefs = [
  'BAFOU',
  'BABADJOU-BANSOA-BANGANGTE',
  'BAFOUSSAM-BALESSING',
  'TOVIDA_TIKO',
  'AKONOLINGA',
  'AYOS',
  'BAFANG',
  'BAFIA',
  'BAMBILI'
]

const ortherEndRefs = [
  'Douala',
  'Yaoundé'
]

const shippings: Omit<Shipping, '_id'>[] = [];

export async function insertShippings() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des adresses de livraison\n');

    const stores = await getStores();
    stores.forEach(store => {
      const { _id, storeRef, label } = store;
      endRefs.forEach(endRef => {
        const shipping: GlobalShipping = {
          startRef: { _id, storeRef, label },
          endRef,
          label: endRef,
          amount: faker.finance.amount(500, 2000, 0) as unknown as number
        }

        shippings.push(shipping);
      })

      ortherEndRefs.forEach(ortherEndRef => {
        const shipping: ParticularShipping = {
          startRef: { _id, storeRef, label },
          endRef: ortherEndRef,
          label: ortherEndRef,
          amount: 100,
          category: faker.helpers.arrayElement([UserCategory.Particular, UserCategory.EmployeeEntity]),
          address: {
            city: ortherEndRef
          }
        }

        shippings.push(shipping);
      })
    });

    shippings.forEach(shipping => { shipping.enable = true; shipping.create_at = moment().valueOf() });
    const insertedShippings = await (await database.getDatabase()).collection(collectionName).insertMany(shippings);
    stopLoader(insertedShippings);
  } catch (error) {
    console.error(error);
  }
}

export async function getShippings() {
  try {
    return await (await database.getDatabase()).collection(collectionName).find().toArray() as unknown as Shipping[];
  } catch (error) {
    console.error(error);
  }
}