import { Test, TestingModule } from '@nestjs/testing';
import { RenderTypesService } from './render-types.service';

describe('RenderTypesService', () => {
  let service: RenderTypesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RenderTypesService],
    }).compile();

    service = module.get<RenderTypesService>(RenderTypesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
