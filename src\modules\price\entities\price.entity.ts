import { CompanyShipping, ParticularShipping } from '@la-pasta-module/price/shipping';
import { Packaging, Product, RenderType, Store } from "@la-pasta-module/cart";
import { Shipping } from "../shipping";

export class Price {
  _id?: string;
  store: Partial<Store>;
  product: Partial<Product>;
  packaging: Partial<Packaging>;
  category: number;
  renderType: RenderType;
  amount: number;
  userId?: string;
  companyId?: string;
  shippingPrices?: Shipping[];
  commercialRegion?: string;
  enable?: boolean;
  create_at?: number;

  static pricePrototype: Price = {
    store: undefined,
    product: undefined,
    packaging: undefined,
    category: 0,
    renderType: null,
    amount: 0,
    enable: false,
  };

  static constructPartialPrice(price: Partial<Price>) {
    const newPrice = {};
    const keys = Object.keys(this.pricePrototype);
    for (const key of keys) {
      if (key in price) {
        newPrice[key] = price[key];
      }
    }
    return newPrice as Price;
  }
}

export class PriceCompanyShipping extends CompanyShipping {
  amount: number;

  static constructPartialPrice(currentPrice: any): PriceCompanyShipping {
    const price: PriceCompanyShipping = {
      category: currentPrice?.company?.category,
      erpShipToId: currentPrice?.erpShipToId,
      erpShipToDesc: currentPrice?.erpShipToDesc,
      amount: currentPrice?.price,
      startRef: currentPrice?.store,
      companyId: currentPrice?.company?._id.toString(),
      endRef: currentPrice?.endRef,
      label: currentPrice?.label,
      enable: currentPrice?.enable
    }
    return price;
  }
}
export class PriceParticularShipping extends ParticularShipping {
  amount: number;
}