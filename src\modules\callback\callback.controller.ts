import { Body, Controller, Post, Patch, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CallbackService } from './callback.service';
import { Request } from 'express';

@ApiTags('Callbacks')
@Controller('callback')
export class CallbackController {
  constructor(private readonly callBackService: CallbackService) {}

  @Post('orange')
  orangeCallBack(@Body() body: unknown) {
    return this.callBackService.sendOrangeCallBackResponse(body);
  }

  @Post('afriland')
  afrilandCallBack(@Body() body: unknown) {
    return this.callBackService.sendAfrilandOTP(body);
  }

  @Post('generate-validation-key')
  generateActivationKey(@Req() request: Request) {
    return this.callBackService.generateActivationKey(
      request.headers.authorization,
      request.body?.email,
    );
  }

  @Post('verify-validation-key')
  verifyValidationKey(@Req() request: Request) {
    return this.callBackService.verifyValidationKey(
      request.headers.authorization,
      request.body?.validationKey,
    );
  }

  @Patch('set-payment-code')
  setAfrilandCode(@Req() request: Request) {
    return this.callBackService.setAfrilandPaymentCode(
      request.headers.authorization,
      request.body,
    );
  }
}
