import { Document, InsertManyResult, InsertOneResult, ModifyResult, UpdateResult, WithId } from "mongodb";

export interface RepositoryInterface {

    create(data: Document): Promise<InsertOneResult<Document>>;

    createMany(data: Document[]): Promise<InsertManyResult<Document>>;

    findAll(query?: QueryOptions): Promise<Document[]>;

    findOne(query: QueryOptions): Promise<WithId<Document>>;

    findAndModify(query: QueryOptions, data: Document): Promise<WithId<Document>>;

    count(query: QueryFilter): Promise<number>;

    update(filter: QueryFilter, data: Document): Promise<UpdateResult>;
}