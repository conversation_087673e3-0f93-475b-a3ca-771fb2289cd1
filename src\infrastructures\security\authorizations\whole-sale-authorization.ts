import {
    EmployeeType,
    User,
    UserCategory,
    UserRole,
} from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';
import { WholeSaleAction } from '@la-pasta/whole-sale/action';

export class WholeSaleAuthorization implements AuthorizationInterface {
    support(user: User, permission: string): boolean {
        return (
            (
                [
                    WholeSaleAction.CREATE,
                    WholeSaleAction.UPDATE,
                    WholeSaleAction.VIEW,
                    WholeSaleAction.DELETE
                ] as string[]
            ).includes(permission) && user.authorizations.includes(permission)
        );
    }

    authorise(user: User, permission: string): boolean {
        if (permission == WholeSaleAction.VIEW)
            return (
                user.roles.includes(UserRole.BACKOFFICE) ||
                user.roles.includes(UserRole.CLIENT)
            );
        if (permission === WholeSaleAction.UPDATE) {
            return user.roles.includes(UserRole.BACKOFFICE);
        }
        
        return false;

    }
}

export const WholeSaleAuthorizationInstance = new WholeSaleAuthorization();
