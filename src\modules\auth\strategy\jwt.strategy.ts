import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { forwardRef, Injectable, Inject } from '@nestjs/common';
import { config } from 'convict-config';
import {
  CompanyEmployee,
  BaseUser,
  UserCategory,
  UsersService,
} from '@la-pasta-module/users';
import { CompaniesService, Company } from '@la-pasta-module/companies';
import { JwtPayload } from '../dtos';
import { ObjectId } from 'mongodb';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    @Inject(forwardRef(() => UsersService))
    private userService: UsersService,
    @Inject(forwardRef(() => CompaniesService))
    private companyService: CompaniesService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: config.get('jwt.secret'),
    });
  }

  async validate(payload: JwtPayload): Promise<BaseUser> {
    let user = (await this.userService.findOne({
      filter: { _id: payload.sub, email: payload.email },
      projection: { password: 0 },
    })) as unknown as BaseUser;

    if (user.category == UserCategory.CompanyUser)
      user = await this.setCompanyData(user);

    return user;
  }

  private async setCompanyData(user: CompanyEmployee) {
    const company = (await this.companyService.findOne({
      filter: { _id: user.company?._id },
    })) as unknown as Company;

    user.company = company;

    return user;
  }
}
