{"NOT_EMPTY": "le champ {property} est requis", "INVALID_EMAIL": "Adresse mail invalide", "INVALID_STRING": "Le champs {property} doit être une chaine de charactères", "INVALID_JWT": "Token de réinitialisation invalide", "BAD_DATA_URI": "L'image à un foramt base64 invalide", "NOT_BOOLEAN": "{property} doit être un boolean", "NOT_INT": "{property} doit être un nombre", "NOT_NUMBER": "{property} doit être un nombre", "NOT_ARRAY": "{property} doit être un tableau", "MIN_COMPANY_USER": "La compagnie doit contenir au moins un utilisateur.", "BAD_PHONE": "Numéro de téléphone incorrect"}