import { Packaging, Product } from "@la-pasta-module/cart";
import { CompanyCategory } from "@la-pasta-module/companies";
import { UserCategory } from "@la-pasta-module/users";
export class PromoCode {
    _id?: string;
    reference?: string;
    dates?: {
        start: number;
        end: number;
    }
    promoter?: {
        name: string;
        tel: number;
        email: string;
    }
    products?: Partial<Product[]>;
    userCategoriesAssociated?: UserCategory[] | CompanyCategory[];
    regions?: string[];
    packagings?: Partial<Packaging>[];
    discount?: {
        type: DiscountType;
        appliedOn: string;
        value: number;
    }
    enable?: boolean;
    created_at?: number;
}

export enum DiscountType {
    AMOUNT = 0,
    PERCENTAGE = 1
}
