import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";
import { PlanificationData } from "../entities";

export class CreatePlanificationDto {
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  id: number;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
  title: string;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  start: number;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  @IsNumber({}, { message: i18nValidationMessage('validation.NOT_NUMBER') })
  end: number;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  data: PlanificationData;
}
