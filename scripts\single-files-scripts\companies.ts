import readline from 'readline';
import { insertCompagniesDataFile } from 'scripts/company/compagny-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données companies des produits de Click CADYST, ce processus effacera la collection companies.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertCompagniesDataFile();
      console.log('Données des companies insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();