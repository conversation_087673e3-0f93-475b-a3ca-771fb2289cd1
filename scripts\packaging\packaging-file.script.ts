import { Packaging } from '@la-pasta-module/cart';
import { getDataInExcelFile } from '@la-pasta/common';
import { MongoDatabase } from '@la-pasta/database';
import moment from 'moment';
import { setLoader, stopLoader } from 'scripts/common';
import { getUnits } from 'scripts/units';

const database = MongoDatabase.getInstance();
const collectionName = 'packagings';

export async function insertPackaginsDataFile() {
  try {

    setLoader('Chargement des packagins depuis le fichier Excel\n');
    const packagingsCollection = (await database.getDatabase()).collection(collectionName)

    const datas = getDataInExcelFile('la-pasta.packaging') as any[];
    const units = await getUnits();
    const packagings: Packaging[] = [];

    for (const data of datas) {
      const unit = units.find(unit => unit.value == data.unit_value);

      const packaging: Packaging = {
        label: data?.label,
        unit,
        enable: true,
        create_at: moment().valueOf(),
      }

      const query = {
        label: data?.label,
        'unit.value': unit.value,
        enable: true,
      }

      const res = await packagingsCollection.updateOne(query, { $set: { ...packaging } }, { upsert: true });
      if (res?.matchedCount == 0 && !res?.upsertedCount)
        packagings.push(packaging);
    }

    // await dropCollection(collectionName);
    if (packagings.length)
      await packagingsCollection.insertMany(packagings as unknown[]);

    stopLoader(true);
    console.log('Packagins insérés');

  } catch (error) {
    console.error('Erreur lors de l\'insertion des packagins :', error);
  }
}