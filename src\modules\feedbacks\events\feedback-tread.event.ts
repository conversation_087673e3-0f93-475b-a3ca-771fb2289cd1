import moment from "moment";
import { CreateFeedbackDto, UpdateFeedbackDto } from "../dto";
import { Feedback } from "../entities";

export class FeedbackTreadEvent {
    feedback_ref: string;
    feedback_category: string;
    feedback_subcategory: string;
    feedback_treatDate: any;
    email: string;
    associateCommercialEmail: string;
    username: string;

    constructor(
        feedback: any,
        user: { email: string },
    ) {
        this.feedback_ref = feedback.ref;
        this.feedback_category = feedback?.category?.label;
        this.feedback_subcategory = feedback?.subCategory?.label;
        this.feedback_treatDate = moment().format('DD-MM-YYYY');
        this.email = user.email;
        this.associateCommercialEmail = feedback?.user?.company?.associatedCommercial?.email
    }

}
