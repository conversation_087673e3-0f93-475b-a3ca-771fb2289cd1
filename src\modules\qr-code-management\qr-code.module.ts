import { LoyaltyProgramModule } from '@la-pasta-module/loyalty-program/loyalty-program.module';
import { QrCodeController } from './qr-code.controller';
import { QrCodeService } from './qr-code.service';
import { QrCodeRepository } from './repository';
import { forwardRef, Module } from '@nestjs/common';
import { UsersModule } from '@la-pasta-module/users';

@Module({
  imports:[
    forwardRef(() => LoyaltyProgramModule),
    forwardRef(() => UsersModule),
    
  ],
  controllers: [QrCodeController],
  exports: [QrCodeRepository, QrCodeService],
  providers: [QrCodeService, QrCodeRepository],
})
export class QrCodeModule { }
