import { Controller, Get, Patch, Body, UseGuards } from '@nestjs/common';
import { ManualOrderService } from './manual-order.service';

@Controller('manual-order')
export class ManualOrderController {
  constructor(private readonly manualOrderService: ManualOrderService) {}

  @Get()
  @UseGuards()
  async getManualOrder() {
    return this.manualOrderService.getStateManualOrderSetting();
  }

  @Patch()
  @UseGuards()
  async updateManualOrder(@Body() body: { enabled: boolean }) {
    return this.manualOrderService.updateStateManualOrderSetting(body.enabled);
  }
}