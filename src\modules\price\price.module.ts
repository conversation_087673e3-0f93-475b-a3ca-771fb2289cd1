import { Module, forwardRef } from '@nestjs/common';
import { PackagingsModule, ProductsModule, StoresModule } from '@la-pasta-module/cart';
import { PriceService } from './price.service';
import { PriceController } from './price.controller';
import { ShippingModule } from './shipping/shipping.module';
import { PriceRepository } from './repository';
import { ComputePriceModule } from './compute_price/compute_price.module';
import { UsersModule } from '@la-pasta-module/users';
import { CompaniesModule } from '@la-pasta-module/companies';

@Module({
  controllers: [PriceController],
  providers: [PriceService, PriceRepository,],
  imports: [ShippingModule, ProductsModule, PackagingsModule, StoresModule, ComputePriceModule,
    forwardRef(() => UsersModule),
    forwardRef(() => CompaniesModule),

  ],
  exports: [PriceService]
})
export class PriceModule { }
