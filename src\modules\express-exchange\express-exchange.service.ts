import moment from 'moment';
import auth from 'basic-auth';
import oauthToken from 'oauth-token';
import { config } from 'convict-config';
import { ClientProxy } from '@nestjs/microservices';
import { RefreshTokenDto } from '@la-pasta-module/auth/dtos';
import { BaseService, setResponse, t } from '@la-pasta/common';
import { CreateExpressExchangeDto } from './dto/create-express-exchange.dto';
import { EexTransactionEvent } from './events/eex-transaction-created.event';
import { ReferenceKey, StatusTransaction } from './entities/express-exchange.entity';
import { ExpressExchangeRepository } from './repository/express-exchange.repository';
import { ForbiddenException, HttpException, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ExpressExchangeService extends BaseService {
  logger = new Logger(ExpressExchangeService.name);
  authTokenOptions: { salt: string, ttl: string }

  constructor(
    private readonly expressExhangeRepository: ExpressExchangeRepository,
    @Inject('QUEUE') private queueClient: ClientProxy,
  ) {
    super(expressExhangeRepository);
    this.authTokenOptions = { salt: config.get('oauthSalt'), ttl: config.get('oauthTTL') };
  }

  async addEexTransaction(eexTransaction: CreateExpressExchangeDto) {

    try {
      await this.verifyPayload(eexTransaction);

      eexTransaction.status = StatusTransaction.AWAIT_RELOAD;
      const res = await this.create({ ...eexTransaction });

      if (res) {
        this.queueClient.emit(
          'eexTransaction-created',
          new EexTransactionEvent(eexTransaction),
        );
      }

      return setResponse(
        HttpStatus.CREATED,
        `${t('TRANSACTION_CREATED')} ${eexTransaction.status}`,
        res.data);
    } catch (error) {
      this.logger.error(`Error while inserting eex transaction \n ${error.stack}`);
      return error;
    }
  }

  async verifyPayload(eexTransaction: CreateExpressExchangeDto) {
    for (const key in eexTransaction) {
      if ([null, 'null', undefined, 'undefined', NaN, 'NaN', '', "", 0].includes(eexTransaction[key]))
        throw new HttpException(`Please enter a valid value for the property ${key}.`, HttpStatus.BAD_REQUEST);


      if (typeof eexTransaction[key] === 'number' && (eexTransaction[key] <= 0 || !Number.isInteger(eexTransaction[key])))
        throw new HttpException(`Enter a valid value for the property ${key}.`, HttpStatus.BAD_REQUEST);


      if (typeof eexTransaction[key] === 'object') {
        const newObject = eexTransaction[key];
        for (const keyInObject in newObject) {
          if (!newObject[keyInObject]) {
            throw new HttpException(`Please enter a valid value for the property ${keyInObject} in ${key} property.`, HttpStatus.BAD_REQUEST);
          }
          if (keyInObject === 'tel' && !/^2376[0-9]{8}$/.test(newObject[keyInObject])) {
            throw new HttpException(`Fill in the correct format of the phone number with correct values, ex: 2376xXxXxXxX`, HttpStatus.BAD_REQUEST);

            // throw new Error('Fill in the correct format of the phone number with correct values, ex: 2376xXxXxXxX');
          }
          if (typeof newObject[keyInObject] === 'number' && newObject[keyInObject] <= 0) {
            throw new HttpException(`Enter a positive value for the property ${keyInObject}.`, HttpStatus.BAD_REQUEST);
          }
        }
      }

    }

    if (!('referenceKey' in eexTransaction) || eexTransaction.referenceKey !== ReferenceKey.EEXPAY) {
      throw new HttpException(`The referencekey value does not match Express exchange payment process.`, HttpStatus.BAD_REQUEST);
    }

    const regexDate = /^\d{4}-\d{2}-\d{2}$/;
    const regexMounts = /^(0?[1-9]|1[0-2])$/;

    if (!regexDate.test(eexTransaction.transactionDate)
      || +eexTransaction.transactionDate.split('-')[0] < moment().year()
      || !regexMounts.test(eexTransaction.transactionDate.split('-')[1])
    ) {
      throw new HttpException(`The transaction date format is invalid; format: YYYY-MM-DD.`, HttpStatus.BAD_REQUEST);
    }

    if (!((/^[a-zA-Z0-9]+$/).test(eexTransaction.transactionId)) || eexTransaction.transactionId.length < 5) {
      throw new HttpException(`The transactionId format is invalid; please enter an alpha-numeric value of at least 5 characters`, HttpStatus.BAD_REQUEST);
    }

    if (!((/^[0-9]+$/).test(eexTransaction.cimencamAccount)) || eexTransaction.cimencamAccount.length < 2) {
      throw new HttpException(`The cimencamAccount format is invalid; please enter an numeric value of at least 3 characters`, HttpStatus.BAD_REQUEST);
    }

    const month = parseInt(eexTransaction.transactionDate.split("-")[1]);
    const day = parseInt(eexTransaction.transactionDate.split("-")[2]);
    if (day <= 0 || (month <= 6 && month % 2 === 0 && day > 30) || (month > 6 && month % 2 !== 0 && day > 30) || day > 31) {
      throw new HttpException(`The transaction date format is invalid; The number of days is incorrect based on the month.`, HttpStatus.BAD_REQUEST);
    }

    eexTransaction.transactionDate = moment(eexTransaction.transactionDate).valueOf().toString();

    if (await this.verifyDataEexTransactionExist({ transactionId: eexTransaction.transactionId }))
      throw new HttpException(`The transaction already exists.`, HttpStatus.FOUND);
  }

  async getEexTransactionBy(fields: QueryFilter) {
    return await this.expressExhangeRepository.getEexTransactionBy({ ...fields });
  }

  async updateEexTransactionBy(query: QueryFilter, data: any) {
    return await this.expressExhangeRepository.updateEexTransaction({ query }, data);
  }

  async updateEexTransaction(transactionId: any, data: any) {
    try {
      if (!(await this.verifyDataEexTransactionExist(transactionId)))
        throw new Error(`Transaction not found in system.`);

      if ('status' in data && !(['CANCEL', 'PENDING'].includes(data['status']))) {
        throw new Error(JSON.stringify({
          message: `An error has occurred while updating transaction with status ${data['status']}`,
          error: `status ${data['status']} does not match status define for transaction`,
        }));
      }
      await this.expressExhangeRepository.updateEexTransaction(transactionId, data);
      return { message: ` Your transaction has been successfully modified`, };
    } catch (error) {
      this.logger.error(`Error while updating eex transaction by ${transactionId} \n ${error.stack}`);
      return error;
    }
  }

  async checkStatus(query: QueryFilter) {
    try {
      const res = await this.expressExhangeRepository.getEexTransactionBy(query ?? {});
      if (!res || !('_id' in res)) {
        throw new Error(JSON.stringify(`Could not find transaction in system\n result:${res}`));
      }
      return res;
    } catch (error) {
      return error;
    }
  }

  async verifyDataEexTransactionExist(query: QueryFilter) {
    const res = await this.checkStatus({ ...query });
    if (res instanceof Error) return false;

    return true;
  }

  async generateToken(req: any): Promise<any> {

    try {
      var user = auth(req)
      if (user?.name != config.get('eexTransaction.login') || user?.pass != config.get('eexTransaction.password')) {
        return new ForbiddenException(t('error.NOT_AUTHORIZE'));
      }

      const tokenData = {
        _id: user.pass,
        email: user.name,
        referenceKey: ReferenceKey.EEXPAY
      };
      const OauthToken = oauthToken(this.authTokenOptions);
      const token = await OauthToken.create({ userId: JSON.stringify({ ...tokenData }) });
      return token;

    } catch (error) {
      this.logger.error(`Error during token generation \n ${error.stack}`);
      return error;
    }

  }

  async refeshToken({ token }: RefreshTokenDto): Promise<any> {
    const OauthToken = oauthToken(this.authTokenOptions);
    return await OauthToken.refresh(token);
  }

}


