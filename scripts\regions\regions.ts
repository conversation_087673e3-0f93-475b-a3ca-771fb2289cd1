import { MongoDatabase } from "@la-pasta/database";
import { setLoader, stopLoader } from "../common";
import { ObjectId } from "mongodb";

const mapRegions = {
    littoral: 'R1',
    ouest: 'R1',
    'nord-ouest': 'R1',
    'sud-ouest': 'R1',
    centre: 'R2',
    sud: 'R2',
    est: 'R2',
    adamaoua: 'R3',
    nord: 'R3',
    'extrême-nord': 'R3',
}

let updatedOrders = 0;

const database = MongoDatabase.getInstance();

async function getOrders() {
    return await (await database.getDatabase())
        .collection('orders')
        .find({ "cart.store.address.commercialRegion": { $exists: false } })
        .limit(1000)
        .toArray()
}

async function getLenghtOrders(): Promise<number> {
    return await (await database.getDatabase())
        .collection('orders')
        .countDocuments({ "cart.store.address.commercialRegion": { $exists: false } });
}

async function addCommercialRegionToOrder(order: any) {
    const { store: { address: { region } } } = order?.cart;

    const updatedOrder = await (await database.getDatabase())
        .collection('orders')
        .updateOne(
            { _id: new ObjectId(order._id) },
            { $set: { 'cart.store.address.commercialRegion': mapRegions[region.toLocaleLowerCase()] } }
        );

    return updatedOrder.acknowledged;
}

export async function insertCommercialRegions() {
    try {
        setLoader('Insertion des region Commerciales\n');
        const count = await getLenghtOrders();
        do {
            const orders = await getOrders();
            await Promise.allSettled(orders.map(order => addCommercialRegionToOrder(order)));
            updatedOrders += +orders.length;
        } while (count >= updatedOrders);

        stopLoader(true);
    } catch (error) {
        console.error(error)
    }
}