import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateAdvantageDto } from './dto/create-advantage.dto';
import { UpdateAdvantageDto } from './dto/update-advantage.dto';
import { BaseService } from '@la-pasta/common';
import { AdvantageRepository } from './repository/advantage.repository';
import { LoyaltyRewardRepository } from './repository/loyalty-reward.repository';
import { FidelityStatus } from '../loyalty-program/entities';
import { WithId, Document } from 'mongodb';

@Injectable()
export class AdvantagesService extends BaseService {

  constructor(
    private readonly advantagesRepository: AdvantageRepository,
    private readonly loyaltyRewardRepository: LoyaltyRewardRepository
  ) {
    super(advantagesRepository);
  }

// advantage.service.ts
async getRewardItemsByFidelityStatus(status: FidelityStatus): Promise<string[]> {
  const advantage = await this.advantagesRepository.findOne({ filter: { status: status, enable: true } });
  if (!advantage || !advantage.oneTimeBenefit.length) {
    throw new NotFoundException(`Aucun lot disponible pour le statut ${status}`);
  }
  return advantage.oneTimeBenefit; // tableau d'IDs de produits
}


}
