import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { GetUser } from 'src/modules/auth/decorator';
import { JwtGuard } from 'src/modules/auth/guard';
import { BaseUser } from 'src/modules/users/entities';
import { ComputePriceService } from './compute_price.service';
import { CreateComputePriceDto } from './dto/create-compute_price.dto';

@ApiTags('Compute price')
@Controller('compute-price')
export class ComputePriceController {
  constructor(private readonly computePriceService: ComputePriceService) {}

  @Post()
  @UseGuards(JwtGuard)
  create(@GetUser() user: BaseUser, @Body() createComputePriceDto: CreateComputePriceDto) {
    return this.computePriceService.compute(user, createComputePriceDto);
  }
}
