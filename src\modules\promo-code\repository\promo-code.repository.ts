import { BaseRepository } from "@la-pasta/common";
import { Injectable } from "@nestjs/common";
import { InsertOneResult, ObjectId } from 'mongodb';

@Injectable()
export class PromoCodeRepository extends BaseRepository {
    constructor() {
        super();
    }

    async getPromoCode(filters: QueryFilter) {
        try {
            return await (await this.getCollection()).find({}).toArray();
        } catch (error) {
            return error;
        }
    }

    async getPromoCodeById(filters: QueryFilter) {
        try {
            if (filters._id) { filters._id = new ObjectId(filters._id) }
            return await (await this.getCollection()).findOne(filters);
        } catch (error) {
            return error;
        }
    }

    async getPromoCodeUnique(field: QueryFilter) {
        try {
            return await (await this.getCollection()).findOne(field);
        } catch (error) {
            return error;
        }
    }

    async getPromoCodeBy(fields: QueryFilter) {
        try {
            const query = { ...fields };
            return await (await this.getCollection()).find({ query }).toArray();
        } catch (error) {
            return error;
        }
    }

    async updatePromoCode(id: string, code: any) {
        try {
            delete code._id;
            return await (await this.getCollection()).updateOne(
                { _id: new ObjectId(id), },
                {
                    $set: {
                        ...code,
                    },
                },
            );
        } catch (error) {
            return error;
        }
    }

    async deletePromoCode(id: string) {
        try {
            return await (await this.getCollection()).deleteOne({
                _id: new ObjectId(id)
            });
        } catch (error) {
            return null;
        }
    }
}

