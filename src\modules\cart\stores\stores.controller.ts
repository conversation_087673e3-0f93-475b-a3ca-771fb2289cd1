import { getUser } from '@la-pasta/common';
import { Controller, Get, Post, Body, Patch, Param, HttpCode, Query, UseGuards } from '@nestjs/common';
import { StoresService } from './stores.service';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { setResponseController } from 'src/common/helpers';
import { ApiTags } from '@nestjs/swagger';
import { Permission } from '@la-pasta/infrastructures/security';
import { StoreAction } from './actions';
import { JwtGuard } from '@la-pasta-module/auth';

@ApiTags('Stores')
@Controller('stores')
export class StoresController {
  constructor(private readonly storesService: StoresService) { }

  @HttpCode(201)
  @UseGuards(JwtGuard)
  @Post()
  async create(@Body() createStoreDto: CreateStoreDto) {
    Permission.storeAuthorization(getUser(), StoreAction.CREATE);
    return await this.storesService.create(createStoreDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.storeAuthorization(getUser(), StoreAction.VIEW);
    const data = await this.storesService.findAll({ filter: query });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get('filters-elements')
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.storesService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.storeAuthorization(getUser(), StoreAction.VIEW);
    const data = await this.storesService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateStoreDto: UpdateStoreDto) {
    Permission.storeAuthorization(getUser(), StoreAction.UPDATE);
    const data = await this.storesService.update({ _id: id }, updateStoreDto);
    return setResponseController(data);
  }
}
