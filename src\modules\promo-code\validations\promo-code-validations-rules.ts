import { ForbiddenException, Injectable } from "@nestjs/common";
import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from "class-validator";
import { t } from '@la-pasta/common';
import { PromoCodeRepository } from "../repository/promo-code.repository";

@ValidatorConstraint({ name: 'PromoCodeExists', async: true })
@Injectable()
export class PromoCodeExistsRule implements ValidatorConstraintInterface {

  constructor(private PromoCodeRepository: PromoCodeRepository) { }

  async validate(value: any, validationArguments: ValidationArguments): Promise<boolean> {
    try {
      const existVerify = await this.PromoCodeRepository.findOne({ filter: { [validationArguments.property]: value } });
      if (existVerify) throw new ForbiddenException(t('error.PROMO_CODE_EXIST'));
    } catch (error) {
      return false;
    }

    return true;
  }

  defaultMessage(validationArguments?: ValidationArguments): string {
    return t('error.PROMO_CODE_EXIST')
  }

}