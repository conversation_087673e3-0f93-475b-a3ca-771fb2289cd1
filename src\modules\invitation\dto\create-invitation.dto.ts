import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsPhoneNumber, IsNumber } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class CreateInvitationDto {
    @ApiProperty({ type: String })
    @IsString()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    referrerId: string;

    @IsNumber()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    prospectTel: number;
}