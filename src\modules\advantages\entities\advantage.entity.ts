import { FidelityStatus } from "@la-pasta-module/loyalty-program/entities";

export interface Advantage {
  name: string;
  statusValue: FidelityStatus;
  pointsRange: { min: number; max?: number };
  pointsRequired: number;
  oneTimeBenefit: string[];
  monthlyBenefit: string[];
  annualBenefit: string[];
  enable: boolean;
}


export const POINTS_BY_STATUS_AND_PACKAGING = {
  AMIGO: { '5': 1, '25': 5, '50': 10 },
  COLOMBE: { '5': 2, '25': 6, '50': 11 },
  PELICAN: { '5': 2, '25': 7, '50': 12 },
};

