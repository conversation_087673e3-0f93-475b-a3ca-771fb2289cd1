import { Module } from '@nestjs/common';
import { ReportingReloadBalanceService } from './reporting-reload-balance.service';
import { ReportingReloadBalanceController } from './reporting-reload-balance.controller';
import { ReloadBalanceRepository } from '@la-pasta-module/reload-balance/repository';

@Module({
  controllers: [ReportingReloadBalanceController],
  providers: [ReportingReloadBalanceService, ReloadBalanceRepository]
})
export class ReportingReloadBalanceModule { }
