import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { FeedbacksService } from './feedbacks.service';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { FeedbackAction } from './actions';
import { getUser } from '@la-pasta/common';
import { Permission } from '@la-pasta/infrastructures/security';
import { BaseUser } from '@la-pasta-module/users';

@Controller('feedbacks')
export class FeedbacksController {

  constructor(private readonly feedbacksService: FeedbacksService) { }

  @Post()
  @UseGuards(JwtGuard)
  async create(@Body() createFeedbackDto: CreateFeedbackDto) {
    Permission.feedbackAuthorization(getUser(), FeedbackAction.CREATE);
    return await this.feedbacksService.createFeedBack(createFeedbackDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  async findAll(@GetUser() user: BaseUser, @Query() query: QueryFilter) {

    Permission.feedbackAuthorization(user, FeedbackAction.VIEW);

    return await this.feedbacksService.getFeedback(user,{ filter: query });
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.feedbacksService.findOne({ filter: { _id: id } });
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  async update(@Param('id') id: string) {
    Permission.feedbackAuthorization(getUser(), FeedbackAction.UPDATE);
    return await this.feedbacksService.updateFeedback(id)
  }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.feedbacksService.remove(+id);
  // }
}
