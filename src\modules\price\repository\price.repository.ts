import { ObjectId } from "mongodb";
import { BaseRepository } from "@la-pasta/common";
import { Product, RenderType } from "@la-pasta-module/cart";
import { CompanyEmployee, BaseUser, UserCategory, User } from "@la-pasta-module/users";
import { Price } from "../entities";
import { Company, CompanyCategory } from "@la-pasta-module/companies";
import { config } from "convict-config";

export class PriceRepository extends BaseRepository {
  UserCategory: any;

  constructor() {
    super()
  }


  async findStoresWithPackaging(user: User, query: QueryFilter = {}) {
    const matchExpression = {
      $match: {
        // ...query,
        $or: [
          { $and: [{ 'category': ('company' in user) ? user.company.category : user.category, $and: [{ 'userId': { $exists: false } }, { 'companyId': { $exists: false } }] }] },
          {
            $and: [{
              'category': ('company' in user)
                ? user.company.category
                : user.category,
              $or: [{
                'userId': ('company' in user)
                  ? ''
                  : user._id.toString()
              },
              { 'companyId': ('company' in user) ? user.company._id.toString() : '' }]
            }]
          }]
      }
    };

    const groupExpression = {
      $group: {
        '_id': {
          'store': '$store._id', 'label': '$store.label', storeRef: '$store.storeRef',
        },
        'packaging': {
          '$addToSet': {
            '_id': '$packaging._id',
            'label': '$packaging.label',
            'unit': '$packaging.unit'
          }
        },
        address: { $first: '$store.address' }

      }
    };

    const projectExpression = {
      $project: {
        '_id': '$_id.store',
        'label': '$_id.label',
        'packaging': '$packaging',
        'storeRef': '$_id.storeRef',
        'address': '$address'
      }
    };

    const sortExpression = {
      $sort: {
        'label': 1,
      }
    };

    return (await this.getCollection()).aggregate([matchExpression, groupExpression, projectExpression, sortExpression]).toArray()
  }
  async findStoreProducts(user: BaseUser | CompanyEmployee, store: string, packaging: string) {

    const matchExpression = {
      $match: {
        $or: [
          {
            $and: [
              {
                'store._id': new ObjectId(store),
                // 'packaging._id': new ObjectId(packaging),
                'category': ('company' in user) ? user.company.category : user.category,
                'commercialRegion': ('company' in user)
                  ? (user as CompanyEmployee)?.company?.address?.commercialRegion : user?.address?.commercialRegion,
                'userId': { '$exists': false },
                'companyId': { '$exists': false },
                'enable': true
              }]
          },
          {
            $and: [
              {
                'store._id': new ObjectId(store),
                // 'packaging._id': new ObjectId(packaging),
                'category': ('company' in user) ? user.company.category : user.category,
                'enable': true,
                $or: [
                  { 'userId': ('company' in user) ? '' : user._id.toString() },
                  { 'companyId': ('company' in user) ? user.company._id.toString() : '' }
                ]
              }]
          }
        ]
      }
    };

    const groupExpression = {
      $group: {
        '_id': '$product._id',
        'fields': { $push: '$$CURRENT' },
      }
    };

    const projectExpression = {
      $project: {
        '_id': 0,
        'product': '$_id',
        'priceOffers': '$fields'
      }
    };

    const results = await (await this.getCollection()).aggregate([matchExpression, groupExpression, projectExpression]).toArray() as unknown as { product: Partial<Product>, priceOffers: Price[] }[];

    const products: any[] = []


    if (config.get('switchFilterPrice')) {
      for (let i = 0; i < results.length; i++) {
        if (results[i].priceOffers.length >= 2) {
          const offer = results[i].priceOffers.find(priceOffer => 'companyId' in priceOffer || 'userId' in priceOffer);
          let offerPickupGlobal: Price;
          if (!('amount' in offer)) {
            offerPickupGlobal = results[i]?.priceOffers.find(priceOffer => !('companyId' in priceOffer || 'userId' in priceOffer));
          }

          products.push({ ...offer, amount: offer?.amount ?? offerPickupGlobal?.amount });
          continue;
        }
        products.push(results[i].priceOffers[0]);
      }
    } else {
      for (let i = 0; i < results.length; i++) {
        const priceOffers = this.removeDuplicatesKeepSpecific(results[i].priceOffers);

        for (const priceOffer of priceOffers) {

          let shippingPrices = [];
          let offer: Price;
          if (('companyId' in priceOffer || 'userId' in priceOffer) && priceOffer?.enable && ('shippingPrices' in priceOffer)) {
            const items = priceOffer?.shippingPrices.filter(shippingPrice => shippingPrice?.enable);
            shippingPrices = shippingPrices.concat(items);
            offer = priceOffer;
          }

          let offerPickupGlobal: Price;
          if (('companyId' in priceOffer || 'userId' in priceOffer) && priceOffer?.enable) {
            offerPickupGlobal = priceOffers.find(priceOffer => !('companyId' in priceOffer || 'userId' in priceOffer));
          }

          // let offer: Price;
          if (priceOffer?.renderType === RenderType.PICKUP || ('amount' in priceOffer))
            offer = priceOffer;

          offer = { ...offer, shippingPrices };

          products.push({ ...offer, amount: offer?.amount ?? offerPickupGlobal?.amount });
        }

        // if (priceOffers.length >= 2) {
        //   const offers = priceOffers.filter(priceOffer => ('companyId' in priceOffer || 'userId' in priceOffer) && priceOffer?.enable);

        //   let shippingPrices = [];

        //   for (const offer of offers) {
        //     if ('shippingPrices' in offer) {
        //       const items = offer?.shippingPrices.filter(shippingPrice => shippingPrice?.enable);
        //       // offer?.shippingPrices.forEach(shippingPrice =>  shippingPrices.push(shippingPrice));
        //       shippingPrices = shippingPrices.concat(items);
        //     }
        //   }

        //   let offer = offers.find(offer => (offer?.renderType === RenderType.PICKUP || ('amount' in offer)));

        //   offer = { ...offers[0], shippingPrices };

        //   let offerPickupGlobal: Price;
        //   if (!('amount' in offer)) {
        //     offerPickupGlobal = priceOffers.find(priceOffer => !('companyId' in priceOffer || 'userId' in priceOffer));
        //   }

        //   products.push({ ...offer, amount: offer?.amount ?? offerPickupGlobal?.amount });
        //   continue;
        // }

        // products.push(results[i].priceOffers[0]);

      }
    }

    return products;
  }

  removeDuplicatesKeepSpecific(priceOffers: Price[]) {
    const seen = new Map();

    for (const item of priceOffers) {
      const packagingId = item.packaging?._id.toString();
      const renderType = item.renderType;

      // Create a unique key based on packaging._id and renderType
      const key = `${packagingId}-${renderType}`;

      // Check if this packaging ID and renderType combination is already seen
      if (seen.has(key)) {
        const existingItem = seen.get(key);

        // If existing item doesn't have companyId or userId but the current item does, replace it
        if (!('companyId' in existingItem || 'userId' in existingItem) && ('companyId' in item || 'userId' in item)) {
          seen.set(key, item);
        }
      } else {
        // If packaging ID and renderType combination is not seen, add it to the map
        seen.set(key, item);
      }
    }

    // Convert the map values back to an array
    return Array.from(seen.values());
  }

  async findCategoriesPrices(store: string, packaging: string, renderType: RenderType, commercialRegion: string) {
    const
      matchExpression = {
        $match: {
          'store._id': new ObjectId(store),
          'packaging._id': new ObjectId(packaging),
          'renderType': renderType,
          'commercialRegion': commercialRegion,
          'enable': true,
          $or: [{
            'userId': { $exists: false },
            'companyId': { $exists: false }
          }]
        }
      },
      groupExpression = {
        $group: {
          '_id': '$category',
          'priceOffers': { $push: { $mergeObjects: [{ 'product': '$product' }, { 'amount': '$amount' }, { '_id': "$_id" }, { 'enable': '$enable' }] } }
        }
      },
      projectionExpression = {
        '$project': { 'category': '$_id', 'priceOffers': 1, '_id': 0 }
      };

    return await (await this.getCollection()).aggregate(
      [matchExpression, groupExpression, projectionExpression]
    ).toArray() as unknown as { category: UserCategory | CompanyCategory, products: Product[] };
  }

  async updateShippingPrice(id: string, shipping: { _id: string;[data: string]: any }) {
    const { _id, ...rest } = shipping;
    return await (await this.getCollection()).updateOne(
      { _id: new ObjectId(id), "shippingPrices._id": new ObjectId(_id) },
      { $set: { [`shippingPrices.$.${Object.keys(rest).toString()}`]: rest[Object.keys(rest).toString()] } }
    );
  }
}