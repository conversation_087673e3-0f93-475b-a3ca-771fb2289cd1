import { MongoDatabase } from "@la-pasta/database";
import { ObjectId } from "mongodb";
import moment from "moment";
import { dropCollection, setLoader, stopLoader } from "../common";

const fonctions: any[] = [{
  "_id": "621dcf312a394a3a74acf71a",
  "DIRECTIONS": "DFC",
  "SERVICES": "Service IT",
  "FONCTIONS": "Technicien Support IT",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c0"
}, {
  "_id": "621dcf312a394a3a74acf71b",
  "DIRECTIONS": "DFC",
  "SERVICES": "Service IT",
  "FONCTIONS": "Responsable Service Informatique",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c0"
}, {
  "_id": "621dcf312a394a3a74acf71f",
  "DIRECTIONS": "DFC",
  "SERVICES": "Service IT",
  "FONCTIONS": "Support Utilisateur Help Desk",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c0"
}, {
  "_id": "621dcf312a394a3a74acf720",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Operational Procurement Specialist",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf727",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Head of Procurement",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf728",
  "DIRECTIONS": "DFC",
  "SERVICES": "Contrôle de Gestion",
  "FONCTIONS": "Contrôleur de Gestion",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55cb"
}, {
  "_id": "621dcf312a394a3a74acf72a",
  "DIRECTIONS": "DFC",
  "SERVICES": "Contrôle de Gestion",
  "FONCTIONS": "Contrôleur de Gestion",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55cb"
}, {
  "_id": "621dcf312a394a3a74acf72c",
  "DIRECTIONS": "DFC",
  "SERVICES": "Comptabilité Client",
  "FONCTIONS": "Cassière",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c8"
}, {
  "_id": "621dcf312a394a3a74acf733",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "Comptable",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf734",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "Country Shared Service Centre Manager",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf736",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Service",
  "FONCTIONS": "Fixed Assets & Capex Accountant",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55cc"
}, {
  "_id": "621dcf312a394a3a74acf73b",
  "DIRECTIONS": "DG",
  "SERVICES": "Mortier",
  "FONCTIONS": "Superviseur Santé, Sécurité, Sûreté & Environnement",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d9"
}, {
  "_id": "621dcf312a394a3a74acf743",
  "DIRECTIONS": "DG",
  "SERVICES": "Sécurité",
  "FONCTIONS": "Responsable Santé, Sécurité & Sûreté",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d6"
}, {
  "_id": "621dcf312a394a3a74acf746",
  "DIRECTIONS": "DG",
  "SERVICES": "Projets",
  "FONCTIONS": "Project Manager",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d7"
}, {
  "_id": "621dcf312a394a3a74acf74b",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Cassier Dépôt",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf750",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Distribution Supervisor",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf751",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Responsable Sécurité Logistique",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf754",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Assistant Chef de Dépôt -Garoua Boulai",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf756",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Responsable Logistique",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf758",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Weighbridge Operator",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf75a",
  "DIRECTIONS": "DL",
  "SERVICES": "Service Clients",
  "FONCTIONS": "Agent Ventes",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55df"
}, {
  "_id": "621dcf312a394a3a74acf75f",
  "DIRECTIONS": "DL",
  "SERVICES": "Service Clients",
  "FONCTIONS": "Customer Care Specialist",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55df"
}, {
  "_id": "621dcf312a394a3a74acf761",
  "DIRECTIONS": "DM",
  "SERVICES": "Marketing",
  "FONCTIONS": "Assistante Marketing",
  "directionId": "621dcda5aa44e43d68e7731b",
  "serviceId": "621dcee2cb0c0f3f641c55e0"
}, {
  "_id": "621dcf312a394a3a74acf768",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Chargé Services Généraux",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf769",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Chargée Développement RH",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf76a",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Chargée Rémunération",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf76d",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Directrice des Ressources Humaines",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf774",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Responsable Formation BU",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf77a",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Electricien",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf77b",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Patroller",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf77c",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Production Planner & Reporting",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf77e",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Superintendent Fabrication",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf782",
  "DIRECTIONS": "DUB",
  "SERVICES": "Ensachage",
  "FONCTIONS": "Packaging Supervisor",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55eb"
}, {
  "_id": "621dcf312a394a3a74acf789",
  "DIRECTIONS": "DUB",
  "SERVICES": "Magasin",
  "FONCTIONS": "Préparateur Electrique",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55f8"
}, {
  "_id": "621dcf312a394a3a74acf78c",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Chargé Automatism & Instrumentaion",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf78d",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Electricien",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf792",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Superintendant Electrique",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf795",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "FONCTIONS": "Chef Atelier Mécanique",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e4"
}, {
  "_id": "621dcf312a394a3a74acf799",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "FONCTIONS": "Senior Maintenance Supervisor (Mechanical)",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e4"
}, {
  "_id": "621dcf312a394a3a74acf79f",
  "DIRECTIONS": "DUB",
  "SERVICES": "Port",
  "FONCTIONS": "Contremaitre Port",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e5"
}, {
  "_id": "621dcf312a394a3a74acf7a0",
  "DIRECTIONS": "DUB",
  "SERVICES": "Port",
  "FONCTIONS": "Superviseur Port",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e5"
}, {
  "_id": "621dcf312a394a3a74acf7a1",
  "DIRECTIONS": "DUB",
  "SERVICES": "Usine Bonaberi",
  "FONCTIONS": "Capex Project Manager",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf7a3",
  "DIRECTIONS": "DUB",
  "SERVICES": null,
  "FONCTIONS": "Assistante DUB",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf7a4",
  "DIRECTIONS": "DUB",
  "SERVICES": null,
  "FONCTIONS": "Responsable des Opérations",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf7e7",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Senior Field Sales",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf72b",
  "DIRECTIONS": "DFC",
  "SERVICES": "Contrôle de Gestion",
  "FONCTIONS": "Head of Controlling",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55cb"
}, {
  "_id": "621dcf312a394a3a74acf730",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "Comptable GL",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf739",
  "DIRECTIONS": "DG",
  "SERVICES": "Mortier",
  "FONCTIONS": "Mortar Commercial & Logistics Manager",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d9"
}, {
  "_id": "621dcf312a394a3a74acf73d",
  "DIRECTIONS": "DG",
  "SERVICES": "Digital",
  "FONCTIONS": "Digital Manager",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d1"
}, {
  "_id": "621dcf312a394a3a74acf73f",
  "DIRECTIONS": "DG",
  "SERVICES": "Audit Interne",
  "FONCTIONS": "Responsable Audit & Contrôle Interne",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d0"
}, {
  "_id": "621dcf312a394a3a74acf740",
  "DIRECTIONS": "DG",
  "SERVICES": "Communication",
  "FONCTIONS": "Responsable Communication",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d4"
}, {
  "_id": "621dcf312a394a3a74acf744",
  "DIRECTIONS": "DG",
  "SERVICES": null,
  "FONCTIONS": "Directeur Général",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf745",
  "DIRECTIONS": "DG",
  "SERVICES": null,
  "FONCTIONS": "Directeur Général Adjoint",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf759",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Chef de Dépôt (Garoua Boulai)",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf75c",
  "DIRECTIONS": "DL",
  "SERVICES": "Service Clients",
  "FONCTIONS": "Customer Care Supervisor",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55df"
}, {
  "_id": "621dcf312a394a3a74acf75e",
  "DIRECTIONS": "DL",
  "SERVICES": "Service Clients",
  "FONCTIONS": "Sales Agent",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55df"
}, {
  "_id": "621dcf312a394a3a74acf762",
  "DIRECTIONS": "DM",
  "SERVICES": "Marketing",
  "FONCTIONS": "Responsable Marketing Ciment",
  "directionId": "621dcda5aa44e43d68e7731b",
  "serviceId": "621dcee2cb0c0f3f641c55e0"
}, {
  "_id": "621dcf312a394a3a74acf763",
  "DIRECTIONS": "DM",
  "SERVICES": "Ventes",
  "FONCTIONS": "Pricing, Margin & Performance Analyst",
  "directionId": "621dcda5aa44e43d68e7731b",
  "serviceId": "621dcee2cb0c0f3f641c55e1"
}, {
  "_id": "621dcf312a394a3a74acf76b",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Chauffeur",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf773",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Responsable Développement RH & Organisation",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf779",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Control Room Operator",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf77d",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Responsable Production",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf780",
  "DIRECTIONS": "DUB",
  "SERVICES": "Ensachage",
  "FONCTIONS": "Dispatch Supervisor",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55eb"
}, {
  "_id": "621dcf312a394a3a74acf784",
  "DIRECTIONS": "DUB",
  "SERVICES": "Ensachage",
  "FONCTIONS": "Responsable Dispatch",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55eb"
}, {
  "_id": "621dcf312a394a3a74acf78f",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Electricien Sénior",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf793",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Electricien CMR",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf794",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "FONCTIONS": "Chaudronnier",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e4"
}, {
  "_id": "621dcf312a394a3a74acf79e",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Méthodes",
  "FONCTIONS": "Technicien Procédés",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e8"
}, {
  "_id": "621dcf312a394a3a74acf7e5",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Sales Representative",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7ef",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Regional Sales Manager (Centre, Sud, Est)",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf71c",
  "DIRECTIONS": "DFC",
  "SERVICES": "Service IT",
  "FONCTIONS": "IT User End Support",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c0"
}, {
  "_id": "621dcf312a394a3a74acf71d",
  "DIRECTIONS": "DFC",
  "SERVICES": "Service IT",
  "FONCTIONS": "Chargé Infrastructures & Réseaux",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c0"
}, {
  "_id": "621dcf312a394a3a74acf722",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Procurement Administrator",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf723",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Procurement Category Manager",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf724",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Operational Procurement Specialist",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf725",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Procurement Administrator / Transit",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf726",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Transactional Procurement / P2P Manager",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf72d",
  "DIRECTIONS": "DFC",
  "SERVICES": "Comptabilité Client",
  "FONCTIONS": "Risk & Credit Manager",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c8"
}, {
  "_id": "621dcf312a394a3a74acf72f",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "Accounting & R2R Manager",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf732",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "SSC Assistant",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf735",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "Directeur Financier & Comptable",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf737",
  "DIRECTIONS": "DG",
  "SERVICES": "Juridique",
  "FONCTIONS": "Directrice Juridique",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d8"
}, {
  "_id": "621dcf312a394a3a74acf73c",
  "DIRECTIONS": "DG",
  "SERVICES": "Missions",
  "FONCTIONS": "Chargée des Mission au Projet Nachtigal",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55da"
}, {
  "_id": "621dcf312a394a3a74acf73e",
  "DIRECTIONS": "DG",
  "SERVICES": "Audit Interne",
  "FONCTIONS": "Directeur Audit Interne",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d0"
}, {
  "_id": "621dcf312a394a3a74acf742",
  "DIRECTIONS": "DG",
  "SERVICES": "Laboratoire",
  "FONCTIONS": "Laboratory Technician",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d3"
}, {
  "_id": "621dcf312a394a3a74acf74e",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Weighbridge Specialist",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf752",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Superviseur Burex",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf753",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Superviseur Planification",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf757",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Chef de Dépôt (Belabo)",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf75b",
  "DIRECTIONS": "DL",
  "SERVICES": "Service Clients",
  "FONCTIONS": "Sales Administration Agent",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55df"
}, {
  "_id": "621dcf312a394a3a74acf765",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Assistante DRH",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf767",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Chargé RH",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf76c",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Coordinateur Santé",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf770",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Responsable Administatif Bonaberi",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf775",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Responsable Rémuneration & Reporting",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf776",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Training Coordinator",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf777",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Chef de Quart",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf783",
  "DIRECTIONS": "DUB",
  "SERVICES": "Ensachage",
  "FONCTIONS": "Packaging Supervisor",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55eb"
}, {
  "_id": "621dcf312a394a3a74acf787",
  "DIRECTIONS": "DUB",
  "SERVICES": "Laborantoire",
  "FONCTIONS": "Responsable Contrôle Qualité",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf788",
  "DIRECTIONS": "DUB",
  "SERVICES": "Magasin",
  "FONCTIONS": "Magasinier",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55f8"
}, {
  "_id": "621dcf312a394a3a74acf78b",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance",
  "FONCTIONS": "Responsable Maintenance",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55f1"
}, {
  "_id": "621dcf312a394a3a74acf78e",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Electricien Instrumentiste",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf797",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "FONCTIONS": "Ingénieur Mécanique",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e4"
}, {
  "_id": "621dcf312a394a3a74acf79b",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Méthodes",
  "FONCTIONS": "Préparateur Mécanique",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e8"
}, {
  "_id": "621dcf312a394a3a74acf79d",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Méthodes",
  "FONCTIONS": "Responsable Procédés",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e8"
}, {
  "_id": "621dcf312a394a3a74acf7a2",
  "DIRECTIONS": "DUB",
  "SERVICES": "Usine Bonaberi",
  "FONCTIONS": "Responsable Sécurité, Sûreté et Environnement",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf7e9",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Export Sales Manager",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7eb",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Technical Sales Supervisor",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7ed",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Responsable Infrastructures",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7ee",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Superviseur Projets Ventes",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7e8",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Junior Field Sales Representative",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7ec",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Directeur des Ventes",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7f1",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Product & Channel Manager",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7f2",
  "DIRECTIONS": "DV",
  "SERVICES": "Sales",
  "FONCTIONS": "Regional Sales Manager (Littoral & Ouest)",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf71e",
  "DIRECTIONS": "DFC",
  "SERVICES": "Service IT",
  "FONCTIONS": "Superviseur Maintenance Micro Informatique",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c0"
}, {
  "_id": "621dcf312a394a3a74acf721",
  "DIRECTIONS": "DFC",
  "SERVICES": "Procurement",
  "FONCTIONS": "Procurement Category Manager",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c2"
}, {
  "_id": "621dcf312a394a3a74acf729",
  "DIRECTIONS": "DFC",
  "SERVICES": "Contrôle de Gestion",
  "FONCTIONS": "Nomayos & RMX Controller",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55cb"
}, {
  "_id": "621dcf312a394a3a74acf72e",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "Trésorière",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf731",
  "DIRECTIONS": "DFC",
  "SERVICES": "Shared Services Center",
  "FONCTIONS": "Comptable Clients",
  "directionId": "621dcda5aa44e43d68e77317",
  "serviceId": "621dcee2cb0c0f3f641c55c1"
}, {
  "_id": "621dcf312a394a3a74acf738",
  "DIRECTIONS": "DG",
  "SERVICES": "Juridique",
  "FONCTIONS": "Responsable Juridique",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d8"
}, {
  "_id": "621dcf312a394a3a74acf73a",
  "DIRECTIONS": "DG",
  "SERVICES": "Mortier",
  "FONCTIONS": "Mortar Operations & Development Manager",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d9"
}, {
  "_id": "621dcf312a394a3a74acf741",
  "DIRECTIONS": "DG",
  "SERVICES": "DG",
  "FONCTIONS": "Assistante de Direction",
  "directionId": "621dcda5aa44e43d68e77318",
  "serviceId": "621dcee2cb0c0f3f641c55d2"
}, {
  "_id": "621dcf312a394a3a74acf74c",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Chef de Dépôt(Kousseri)",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf74d",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Weighbridge Operator",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf74f",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Assistant Sécurité Routière",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf755",
  "DIRECTIONS": "DL",
  "SERVICES": "Logistique",
  "FONCTIONS": "Superviseur Logistique",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55de"
}, {
  "_id": "621dcf312a394a3a74acf75d",
  "DIRECTIONS": "DL",
  "SERVICES": "Service Clients",
  "FONCTIONS": "Sales Administration Coordinator",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": "621dcee2cb0c0f3f641c55df"
}, {
  "_id": "621dcf312a394a3a74acf760",
  "DIRECTIONS": "DL",
  "SERVICES": null,
  "FONCTIONS": "Directeur Logistique",
  "directionId": "621dcda5aa44e43d68e7731a",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf764",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Administrative Assistant & Travel Coordinator",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf766",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Assistante RH",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf76e",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "HR Digital Project Supervisor",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf76f",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "HR Project Manager",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf771",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Responsable Administratif Figuil",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf772",
  "DIRECTIONS": "DRH",
  "SERVICES": "RH",
  "FONCTIONS": "Responsable Administratif Nomayos)",
  "directionId": "621dcda5aa44e43d68e7731c",
  "serviceId": "621dcee2cb0c0f3f641c55e2"
}, {
  "_id": "621dcf312a394a3a74acf778",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Contremaitre Carrière & Housekeeping",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf77f",
  "DIRECTIONS": "DUB",
  "SERVICES": "Production",
  "FONCTIONS": "Superviseur Production",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e6"
}, {
  "_id": "621dcf312a394a3a74acf781",
  "DIRECTIONS": "DUB",
  "SERVICES": "Ensachage",
  "FONCTIONS": "Packaging Supervisor",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55eb"
}, {
  "_id": "621dcf312a394a3a74acf785",
  "DIRECTIONS": "DUB",
  "SERVICES": "Laborantoire",
  "FONCTIONS": "Laborantin",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf786",
  "DIRECTIONS": "DUB",
  "SERVICES": "Laborantoire",
  "FONCTIONS": "Contremaitre Laboratoire",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": null
}, {
  "_id": "621dcf312a394a3a74acf78a",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance",
  "FONCTIONS": "Chargé suivi des travaux neufs",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55f1"
}, {
  "_id": "621dcf312a394a3a74acf790",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Inspecteur Electrique",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf791",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Electrique",
  "FONCTIONS": "Senior Maintenance Supervisor (Electrical)",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e3"
}, {
  "_id": "621dcf312a394a3a74acf796",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "FONCTIONS": "Graisseur",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e4"
}, {
  "_id": "621dcf312a394a3a74acf798",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "FONCTIONS": "Mécanicien",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e4"
}, {
  "_id": "621dcf312a394a3a74acf79a",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Mécanique",
  "FONCTIONS": "Superviseur Graissage",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e4"
}, {
  "_id": "621dcf312a394a3a74acf79c",
  "DIRECTIONS": "DUB",
  "SERVICES": "Maintenance - Méthodes",
  "FONCTIONS": "Responsable Méthodes",
  "directionId": "621dcda5aa44e43d68e7731d",
  "serviceId": "621dcee2cb0c0f3f641c55e8"
}, {
  "_id": "621dcf312a394a3a74acf7e6",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Regional Sales Manager (Grand Nord)",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7ea",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Area Sales Supervisor",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}, {
  "_id": "621dcf312a394a3a74acf7f0",
  "DIRECTIONS": "DV",
  "SERVICES": "VENTES",
  "FONCTIONS": "Field Sales Representative",
  "directionId": "621dcda5aa44e43d68e77320",
  "serviceId": "621dcee2cb0c0f3f641c5604"
}]

const database = MongoDatabase.getInstance();
const collectionName = 'fonctions';

export async function insertFonctions() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des posts\n');

    fonctions.forEach(fonction => { fonction._id = new ObjectId(fonction._id), fonction.enable = true; fonction.create_at = moment().valueOf() });
    const insertedFonctions = await (await database.getDatabase()).collection(collectionName).insertMany(fonctions);
    stopLoader(insertedFonctions);
  } catch (error) {
    console.error(error)
  }
}