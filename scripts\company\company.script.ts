import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { MongoDatabase } from "@la-pasta/database";
import { Company, CompanyCategory, PrecompteRate } from '@la-pasta-module/companies';
import moment from 'moment';

const database = MongoDatabase.getInstance();
const collectionName = 'companies';

const companies: Omit<Company, '_id'>[] = [
  {
    "name": "Distributeur",
    "category": 101,
    "precompteRate": PrecompteRate.Real,
    "tel": *********,
    "address": {
      "region": "R1",
      "city": "DOUALA",
      "district": "LOGPOM"
    },
    "erpSoldToId": '104182',
    "erpShipToId": 104183,
    "rccm": "",
    "nui": "*********",
    "erpShipToDesc": "Distributeur 1"
  },
  {
    "name": "Distributeur",
    "category": 102,
    "precompteRate": PrecompteRate.Simple,
    "tel": null,
    "address": {
      "region": "R2",
      "city": "KRIBI",
      "district": "LOBE"
    },
    "erpSoldToId": '103794',
    "erpShipToId": 103795,
    "rccm": "RC",
    "nui": "*********0",
    "erpShipToDesc": "Distributeur 2"
  },
  {
    "name": "Distributeur 3",
    "category": 102,
    "precompteRate": PrecompteRate.Simple,
    "tel": null,
    "address": {
      "region": "R2",
      "city": "YAOUNDE",
      "district": "MOKOLO"
    },
    "erpSoldToId": '103794',
    "erpShipToId": 103795,
    "rccm": "RC",
    "nui": "*********0",
    "erpShipToDesc": "Distributeur 3"
  },
  {
    "name": "Distributeur 4",
    "category": 101,
    "precompteRate": PrecompteRate.Simple,
    "tel": null,
    "address": {
      "region": "R2",
      "city": "KRIBI",
      "district": "LOBE"
    },
    "erpSoldToId": '103794',
    "erpShipToId": 103795,
    "rccm": "RC",
    "nui": "*********0",
    "erpShipToDesc": "Distributeur 4"
  },
  {
    "name": "Distributeur 5",
    "category": 102,
    "precompteRate": PrecompteRate.Simple,
    "tel": null,
    "address": {
      "region": "R1",
      "city": "DOUALA",
      "district": "AKWA"
    },
    "erpSoldToId": '103794',
    "erpShipToId": 103795,
    "rccm": "RC",
    "nui": "*********0",
    "erpShipToDesc": "Distributeur 5"
  }
];

export function getCompaniesCategories() {
  return Object.values(CompanyCategory).filter(value => typeof value != 'string') as number[];
}

export async function insertCompanies() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des compagnies\n');
    let i = 1;
    while (i < 7) {
      companies.push({
        "name": "Distributeur " + i,
        "category": i % 2 === 0 ? CompanyCategory.Baker : CompanyCategory.WholeSaler,
        "precompteRate": i % 2 === 0 ? PrecompteRate.Real : PrecompteRate.Simple,
        "tel": ********* + i,
        "address": {
          "region": "R2",
          "city": "KRIBI",
          "district": "LOBE"
        },
        "erpSoldToId": '103794' + i,
        "erpShipToId": 103795 + i,
        "rccm": "RC",
        "nui": "*********0",
        "erpShipToDesc": "Distributeur " + i,
        enable: true,
      })

      i += 1;
    }
    companies.forEach(company => { company.enable = true; company.created_at = moment().valueOf() });
    const insertedCompanies = await (await database.getDatabase()).collection(collectionName).insertMany(companies);
    stopLoader(insertedCompanies);
  } catch (error) {
    console.error(error);
  }
}

export async function getCompany(query: QueryFilter) {
  try {
    const companies = await (await database.getDatabase()).collection(collectionName).find(query).toArray() as unknown as Company[];
    return companies[0];
  } catch (error) {
    console.error(error);
  }
}

export async function updateCompany(query: QueryFilter, data: QueryFilter) {
  try {
    return await (await database.getDatabase()).collection(collectionName).updateOne(query, data);
  } catch (error) {
    console.error(error);
  }
}

export async function getCompanies() {
  try {
    return await (await database.getDatabase()).collection(collectionName).find().toArray() as unknown as Company[];
  } catch (error) {
    console.error(error);
  }
}

