import { Controller, Post, Body, UseGuards, Get, Param, Query } from '@nestjs/common';
import { AuthorizationRemovalService } from './authorization-removal.service';
import { CreateAuthorizationRemovalDto } from './dto/create-authorization-removal.dto';
import { GetUser } from '@la-pasta-module/auth/decorator';
import { JwtGuard } from '@la-pasta-module/auth/guard';
import { BaseUser } from '@la-pasta-module/users';

@Controller('authorization-removal')
export class AuthorizationRemovalController {
  constructor(private readonly authorizationRemovalService: AuthorizationRemovalService) {}

  @Post('create')
  @UseGuards(JwtGuard)
  async create(
    @Body() data
  ) {
    return this.authorizationRemovalService.transmitOrderToCadystLogistic(data);
  }

  @Get()
  findAll(@Query() query: QueryFilter) {
    return this.authorizationRemovalService.getAllAutorizations(query);
  }

  @Get('users-removal')
  findUserRemoval(@Query() query: QueryFilter) {
    return this.authorizationRemovalService.getUserRemoval(query);
  }

  @Get('filters-elements')
  getElementForFilter(@Query() query: QueryFilter) {
    return this.authorizationRemovalService.getFilterElement(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.authorizationRemovalService.findOne({ filter: { _id: id } });
  }

}
