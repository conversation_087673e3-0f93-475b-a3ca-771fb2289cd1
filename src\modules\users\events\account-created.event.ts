import { config } from "convict-config";
import { User, UserCategory } from "../entities";

export class AccountCreatedEvent {
  public email: string;
  public username: string;
  app_url: string;
  support_email: string;
  company_rccm: string;
  user_tel: number;


  constructor(user: User) {
    this.email = user?.email;
    this.username = `${user?.firstName ?? ''} ${user?.lastName ?? ''}`;
    this.app_url = user?.category >= UserCategory.Commercial ? `${config.get('appUrl.backoffice')}` : `${config.get('appUrl.website')}`;
    this.company_rccm = user['company']?.name ?? '';
    this.user_tel = user['tel'];
  }
}