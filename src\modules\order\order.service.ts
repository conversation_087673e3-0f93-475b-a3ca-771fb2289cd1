import { Inject, Injectable, HttpStatus, forwardRef, HttpException } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ObjectId } from 'mongodb';
import moment from 'moment';
import { BaseService, converDateFilter, convertParams, decodePdfCode, encodePdfCode, getTemplateBody, getUser, setResponse, splitArray, t } from '@la-pasta/common';
import { EmployeeEntity, EmployeeType, User, UserCategory, UsersService } from '@la-pasta-module/users';
import { OrderRepository } from './repository';
import { CreateOrderDto, UpdateOrderDto } from './dto';
import { CancellationStatus, CommercialComment, Order, OrderStatus, OrderValidation, PaymentMode, OrderStatusDelivery } from './entities';
import {
  AfrilandPaymentEvent,
  CreditPaymentEvent,
  ExpressExchangePaymentEvent,
  VisaPaymentEvent,
  MobileMoneyEvent,
  MyAccountPaymentEvent,
  OrangeMoneyEvent,
  M2UEvent,
  keyEventJDE,
} from './events';
import { Permission } from '@la-pasta/infrastructures/security';
import { PaymentAction } from './actions';
import { CompaniesService, Company } from '@la-pasta-module/companies';
import { LowBalanceEvent } from './events/low-balance.event';
import { lastValueFrom } from 'rxjs';
import { pdf } from '@la-pasta/common/helpers/pdf-generator';
import { config } from 'convict-config';
import { LogoService } from '@la-pasta-module/logo/logo.service';
import { Logo, LogoType } from '@la-pasta-module/logo/entities/logo.entity';
import { SnitchsService } from '@la-pasta-module/snitchs/snitchs.service';
import { Snitch } from '@la-pasta-module/snitchs/entities';
import { NotificationsService } from '@la-pasta-module/notifications/notifications.service';
import { NotificationCategory } from '@la-pasta-module/notifications/entities';
import { AuthorizationRemovalService } from '@la-pasta-module/authorization-removal/authorization-removal.service';
import { MongoDatabaseCadystLogistic } from '@la-pasta/database/database-mongodb-cadyst-logistic';

@Injectable()
export class OrderService extends BaseService {
  private readonly cadystLogisticDatabase: MongoDatabaseCadystLogistic;

  constructor(
    private logoSrv: LogoService,
    private snitchSrv: SnitchsService,
    private notificationSrv: NotificationsService,
    private readonly orderRepository: OrderRepository,
    // private loyaltyProgramSrv: LoyaltyProgramService,
    private readonly authorizationRemovalSrv: AuthorizationRemovalService,
    @Inject('JDE') private readonly JDEService: ClientProxy,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,
    @Inject('PAYMENT') private readonly paymentClient: ClientProxy,
    @Inject(forwardRef(() => UsersService)) private readonly userSrv: UsersService,
    @Inject(forwardRef(() => CompaniesService)) private readonly companiesService: CompaniesService,
  ) {
    super(orderRepository);
    this.cadystLogisticDatabase = MongoDatabaseCadystLogistic.getInstance();
  }

  async createOrderByCommercialForClient(query: QueryFilter, newOrder: CreateOrderDto, user: User) {
    // TODO 1: get user with id in query using userService.findOne
    const company = await this.companiesService.findOne({
      filter: { "_id": new ObjectId(query.companyId) },
      projection: { category: 1, name: 1 }
    }) as unknown as Company;

    // TODO 2: throw error if no user in response of findOne
    if (!company) {
      throw new HttpException(t("USER_NOT_FOUND"), 404);
    }

    newOrder['user'] = {
      ...user
    };

    newOrder['company'] = company;


    // TODO 3: Call createOrder method with parameters and return response
    // Permission.paymentAuthorization(
    //   getUser(),
    //   PaymentAction[PaymentMode[newOrder?.payment?.mode?.id]],
    // );

    if (newOrder?.cart?.amount?.TTC <= 0)
      throw new HttpException(t('error.NO_AMOUNT'), 404);

    newOrder.appReference = await this.generateAppReference(newOrder?.cart?.store?.storeRef);

    newOrder.status = (newOrder?.payment?.mode?.id === PaymentMode.LOW_BALANCE)
      ? (OrderStatus.CREDIT_IN_AWAIT_VALIDATION)
      : (newOrder?.payment?.mode?.id === PaymentMode.MY_ACCOUNT) || (newOrder?.payment?.mode?.id === PaymentMode.M2U)
        ? (OrderStatus.PAID)
        : (OrderStatus.CREATED);

    newOrder.statusDelivery = null;

    newOrder.dates = { created: moment().valueOf() };
    newOrder.dates.paid =
      newOrder?.payment?.mode?.id == PaymentMode.MY_ACCOUNT
        ? moment().valueOf()
        : null;

    // if (user.category === UserCategory.Commercial) {
    //   newOrder.user['company'] = user.company;
    // }


    const orderCreated = await this.create(newOrder);

    if (newOrder.payment.mode.id != PaymentMode.VISA) {
      this.paymentClient.emit(
        `${newOrder?.payment?.mode?.id}_payment`,
        this.loadOrderEventData({ _id: orderCreated.data, ...(newOrder as unknown as Order) }, {
          user: { ...user, company: newOrder?.company },
          dailyNbrOrder: await this.count({
            startDate: new Date(), endDate: new Date,
            "conmpany.erpSoldToId": newOrder?.company?.erpSoldToId
          }) as number,
        }),
      );
    }

    return setResponse(HttpStatus.CREATED, t('PAYMENT_RETAIL_INIT'), orderCreated.data);
  }

  async createOrder(user: User, newOrder: CreateOrderDto) {
    let allUserMails;

    if (newOrder?.cart?.amount?.TTC <= 0)
      throw new HttpException(t('error.NO_AMOUNT'), 404);

    newOrder.appReference = await this.generateAppReference(newOrder?.cart?.store?.storeRef);

    newOrder.status = (newOrder?.payment?.mode?.id === PaymentMode.LOW_BALANCE)
      ? (OrderStatus.CREDIT_IN_AWAIT_VALIDATION)
      : ((newOrder?.payment?.mode?.id === PaymentMode.MY_ACCOUNT) || (newOrder?.payment?.mode?.id === PaymentMode.M2U))
        ? (OrderStatus.PAID) : (newOrder?.payment?.mode?.id === PaymentMode.CREDIT)
          ? (OrderStatus.CREDIT_IN_AWAIT_VALIDATION)
          : (OrderStatus.CREATED);

    newOrder.statusDelivery = null;

    if ('company' in user) {
      newOrder.company = newOrder?.user ? newOrder?.user['company'] : user?.company;
      newOrder?.user ? newOrder.company._id = new ObjectId(newOrder?.company?._id) as unknown as string : null;
      allUserMails = (await this.companiesService.findUsers(newOrder.company?._id.toString(), { enable: true, projection: { email: 1, _id: 0 } }))?.data;
      delete user.company;
    }

    newOrder.user = user;
    newOrder.dates = { created: moment().valueOf() };
    newOrder.dates.paid =
      newOrder?.payment?.mode?.id == PaymentMode.MY_ACCOUNT
        ? moment().valueOf()
        : null;

    if (user.category === UserCategory.EmployeeEntity) {
      const OrderQty: number = newOrder?.cart?.items?.reduce((accumulator, curItem) =>
        accumulator + (+curItem?.quantity / +curItem.packaging?.unit?.ratioToTone), 0);

      const tonnage: Tonnage = {
        capacityPerYear: (user as EmployeeEntity).tonnage.capacityPerYear - OrderQty,
        capacity: (user as EmployeeEntity).tonnage.capacity - OrderQty,
      };

      if (tonnage.capacityPerYear < 0) {
        this.logger.error(t('Vous avez dépassé votre quota annuel de produit'));
        throw new HttpException(t("EXECEDE_CAPACITY_ANNUAL"), 404);
      }
      this.userSrv.update({ _id: user?._id }, { tonnage });
    }
    const orderArrivalTime = moment().format('DD/MM/YYYY');

    const orderCreated = await this.create(newOrder);
    this.notificationSrv.generateActionNotification(NotificationCategory.ORDER, (newOrder as unknown as Order));

    const { dataOrder, userValidateLogo } = await this.addLogoWithSignatureInOrder({ _id: orderCreated.data, ...(newOrder as unknown as Order) });

    this.logger.debug(`[DEBUG] Getting daily order count for company ${newOrder?.company?.erpSoldToId}`);
    const dailyNbrOrder = await this.getDailyOrderCount(newOrder?.company?.erpSoldToId);
    this.logger.debug(`[DEBUG] Daily order count result: ${dailyNbrOrder}`);

    if (newOrder.payment.mode.id != PaymentMode.VISA) {
      this.paymentClient.emit(
        `${newOrder?.payment?.mode?.id}_payment`,
        this.loadOrderEventData(dataOrder, {
          user: { ...user, company: newOrder?.company },
          userValidate: userValidateLogo,
          dailyNbrOrder,
          allUserMails,
          orderArrivalTime
        }),
      );
    }

    // await this.loyaltyProgramSrv?.setProgramFidelity(newOrder as Order);

    return setResponse(HttpStatus.CREATED, t('PAYMENT_RETAIL_INIT'), orderCreated.data,);
  }

  async getAllOrders(query: QueryOptions) {
    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }
    // if ((user as EmployeeEntity).employeeType === EmployeeType.CORDO_RH) {
    //   query.filter['status'] = { $in: [100, 101] }

    // }

    return await this.findAll(query);
  }

  async getRecapInfosForOrderList(query: QueryOptions) {

    // query.filter.status ??= { $in: [OrderStatus.PAID, OrderStatus.VALIDATED] };

    if ('status' in query.filter && query.filter.status.includes('$'))
      query.filter.status = JSON.parse(query?.filter?.status);

    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }
    query = convertParams(query);

    return await this.orderRepository.getRecapInfosForOrderList(query?.filter);
  }

  async getUserOrders(user: User, query: QueryOptions) {
    try {
      if ('startDate' in query.filter || 'endDate' in query.filter) {
        this.setDateFilter(query.filter);
      }
      // TODO render this if statement generic
      if ('status' in query.filter && query.filter.status.includes('$'))
        query.filter.status = JSON.parse(query?.filter?.status)

      if ('company' in user) {
        query?.filter?.selectedCompanyId ? user.company._id = query?.filter?.selectedCompanyId : null;
        delete query?.filter?.selectedCompanyId;
        query.filter['company._id'] = new ObjectId(user.company._id);
      } else if ('commercial' in query?.filter) {

        // const associatedCompanies: Company[] = user?.associatedCompanies instanceof Array
        //   ? user?.associatedCompanies
        //   : user?.associatedCompanies instanceof Object ? [user?.associatedCompanies] : [];

        const associatedCompaniesRaw = (user as EmployeeEntity)?.associatedCompanies;
        const associatedCompanies: Company[] = Array.isArray(associatedCompaniesRaw)
          ? associatedCompaniesRaw.flat()
          : associatedCompaniesRaw
            ? [associatedCompaniesRaw]
            : [];

         const companyIds = associatedCompanies?.filter(company => company?._id && typeof company._id === 'string' && company._id.match(/^[a-f\d]{24}$/i))
            ?.reduce<(ObjectId | string)[]>((arr, company) => {
              arr.push(new ObjectId(company._id), company._id);
              return arr;
            }, []);
        // If the commercial has associated companies, use the existing logic
        query.filter["$or"] = [
          { 'company.associatedCommercial._id': query.filter.commercial },
          { 'company.associatedCommercial._id': new ObjectId(query.filter.commercial) },
        ]

        if (companyIds.length) query.filter["$or"].push({ 'company._id': { $in: companyIds } })

        delete query.filter['commercial'];
        const res = await this.findAll(query)
        if (res?.count || companyIds.length) return res;

        query.filter['company.address.commercialRegion'] = user.address.commercialRegion;
        delete query.filter['$or'];
      } else {
        query.filter['user._id'] = new ObjectId(user._id);
      }

      return await this.findAll(query);
    } catch (error) {
      return error;
    }
  }

  async getUserOrdersCommercials(query: QueryOptions) {
    // query.filter.status ??= { $in: [OrderStatus.PAID, OrderStatus.VALIDATED] };

    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }

    if ('status' in query.filter && query.filter.status.includes('$'))
      query.filter.status = JSON.parse(query?.filter?.status)

    query.filter.except = ['erpReference'];

    return await this.findAll(query);
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    query = converDateFilter({ filter: query });
    // query['status'] = OrderStatus.PAID;

    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async saveNumOrderErp(_id: string, { erpReference }: Partial<Order>) {
    return await this.update({ _id }, { erpReference });
  }

  async validateOrder(id: string, data: QueryFilter) {
    const { rejected = false, reference } = data;

    const order = (await this.findOne({
      filter: { _id: id },
    })) as unknown as Order;
    if (!order) {
      return { status: HttpStatus.NOT_FOUND, message: 'Commande non trouvée' };
    }

    const user = getUser();
    let status: OrderStatus;

    if (order.payment.mode.id === PaymentMode.CREDIT) {
      if ('isRetired' in user && user.employeeType === EmployeeType.CORDO_RH &&
        (!('validation' in order) || order.validation < OrderValidation.DRH)) {
        status = rejected ? OrderStatus.CREDIT_REJECTED : OrderStatus.CREDIT_IN_VALIDATION;

        await this.update({ _id: id },
          {
            validation: OrderValidation.CORDO_RH,
            status,
          });
        if (rejected) {
          return { status: HttpStatus.OK, message: 'Commande rejetée' };
        }

        this.queueClient.emit('coordo_validation', this.loadOrderEventData(order, {}));
        this.queueClient.emit('sms_coordo_validation', {
          receiver: '',
          message: `Commande à crédit prévalidée sur LaPasta ! .\n Bonjour, La commande de ${order?.user?.lastName} a été validée par le coordonateur RH.`
        });

        return { status: HttpStatus.OK, message: 'Commande validée' };
      }

      if ('isRetired' in user && user.employeeType === EmployeeType.DRH &&
        'validation' in order && order.validation === OrderValidation.CORDO_RH) {
        await this.update({ _id: id }, {
          validation: OrderValidation.DRH,
          status: rejected ? OrderStatus.CREDIT_REJECTED : OrderStatus.PAID,
        });

        if (rejected) {
          return { status: HttpStatus.OK, message: 'Commande rejetée' };
        }

        this.queueClient.emit('payment_received', this.loadOrderEventData(order, {}));
        this.sendSmsNotificationTOCus(order);
        this.queueClient.emit('commercial_validation', this.loadOrderEventData(order, {}));
        this.sendSmsNotificationToCommercial(order);

        await this.transmitValidatedOrderToCadystLogistic(order);

        return { status: HttpStatus.OK, message: 'Commande validée et bon généré avec succès' };
      }
    }

    if (order.payment.mode.id === PaymentMode.LOW_BALANCE) {
      const validation = {
        user: user.email,
        motif: "rejet de la commande en solde insuffisant",
      };

      status = rejected ? OrderStatus.CREDIT_REJECTED : OrderStatus.PAID;

      const res = await this.update({ _id: id }, { validation, status });

      if (rejected) {
        return { status: HttpStatus.OK, message: 'Commande rejetée' };
      }

      this.queueClient.emit('sms_received', {
        receiver: `${order?.user?.tel}`,
        message: `Bonjour Mme/M. La commande ${order?.appReference} a été rejetée.\n Merci pour votre attention.`
      });
      return res;
    }

    if (order.status === OrderStatus.CREDIT_IN_AWAIT_VALIDATION) {
      await this.update({ _id: id }, {
        reference,
        validation: OrderValidation.COMMERCIAL,
        status: data?.status ?? OrderStatus.PAID,
        'dates.paid': moment().valueOf(),
      });

      await this.transmitValidatedOrderToCadystLogistic(order);
      this.sendSmsNotificationToCommercial(order);
      this.queueClient.emit('payment_received', this.loadOrderEventData(order, {}));
      this.sendSmsNotificationTOCus(order);
      this.JDEService.emit({ cmd: keyEventJDE.CREATE_ORDER }, { appReference: order.appReference });

      return { status: HttpStatus.OK, message: await t('VALIDATE_ORDER_RETAILER') };
    }

    if ([OrderStatus.PAID, OrderStatus.CREDIT_REJECTED].includes(order.status) &&
      ([UserCategory.Commercial, UserCategory.Administrator].includes(user.category))) {
      await this.update({ _id: id }, {
        status: OrderStatus.VALIDATED,
        statusDelivery: OrderStatusDelivery.WAITING,
        'dates.validated': moment().valueOf(),
      });

      const updatedOrder = await this.findOne({ filter: { _id: id } }) as unknown as Order;

      await this.transmitValidatedOrderToCadystLogistic(updatedOrder);

      const allUserMails = (await this.companiesService.findUsers(updatedOrder?.company?._id.toString(), { projection: { email: 1, _id: 0 } }))?.data ?? [];
      this.queueClient.emit('order_validated', {
        order_ref: updatedOrder.appReference,
        order_validated_date: moment().format('DD/MM/YYYY'),
        total: updatedOrder.cart?.amount?.TTC,
        email: splitArray(allUserMails, 'email') ?? updatedOrder.user.email,
        username: `${updatedOrder.user.firstName} ${updatedOrder.user.lastName}`,
        ...updatedOrder,
      });

      if (updatedOrder?.user.category === UserCategory.EmployeeEntity) {
        const useEmploy = await this.userSrv.findOne({ filter: { _id: updatedOrder?.user?._id } }) as unknown as EmployeeEntity;
        const OrderQty: number = updatedOrder?.cart?.items?.reduce((acc, curItem) => acc + (+curItem?.quantity / +curItem?.packaging?.unit?.ratioToTone), 0);

        const tonnage: Tonnage = {
          capacityPerYear: useEmploy.tonnage.capacityPerYear - OrderQty,
          capacity: useEmploy.tonnage.capacity - OrderQty,
        };

        if (tonnage.capacityPerYear < 0) {
          this.logger.error(t('Vous avez dépassé votre quota annuel de produit'));
          throw new HttpException(t("EXECEDE_CAPACITY_ANNUAL"), 404);
        }
        this.userSrv.update({ _id: useEmploy?._id }, { tonnage });
      }

      const newOrder = (await this.findOne({ filter: { _id: id } })) as unknown as Order;
      this.notificationSrv.generateActionNotification(NotificationCategory.ORDER, newOrder);

      return { status: HttpStatus.OK, message: await t('VALIDATE_ORDER_RETAILER') };
    }

    return { status: HttpStatus.UNAUTHORIZED, message: await t('error.NOT_AUTHORIZE') };
  }

  async transmitValidatedOrderToCadystLogistic(order: Order): Promise<any> {
    return await this.authorizationRemovalSrv.transmitOrderToCadystLogistic(order);
  }


  async updateOrder(id: any, order: any) {
    if ('company' in order) {
      order.company['_id'] = new ObjectId(order?.company?._id);
    }
    order.status = OrderStatus.PAID;

    // Préserver le statut de livraison existant ou le définir à null s'il n'existe pas
    if (!order.statusDelivery) {
      const existingOrder = await this.findOne({ filter: { _id: id } }) as unknown as Order;
      order.statusDelivery = existingOrder?.statusDelivery || null;
    }

    order.dates.validated = new Date().valueOf();
    order.nberModif = (order?.nberModif ?? 0) + 1;
    const res = await this.update({ _id: id }, order);

    if (!(res instanceof Error)) {
      this.queueClient.emit('order_modify', {
        client_name: order?.company?.name || order?.user?.firstName + ' ' + order?.user?.lastName,
        order_ref: order?.appReference,
        order_createAt: moment(order?.created_at).format('DD/MM/YYYY'),
        order_modificationDate: moment().format('DD/MM/YYYY'),
        email: order?.user?.email,
        ...order,
      });
      this.queueClient.emit('sms_received',
        {
          receiver: order?.user?.tel, message:
            `Bonjour,\nLa commande numero ${order?.appReference} du client ${order?.user?.lastName || order?.user?.firstName || order?.company?.name} a été modifié.`
        });
    }
    const newOrder = (await this.findOne({
      filter: { _id: id },
    })) as unknown as Order;
    this.notificationSrv.generateActionNotification(NotificationCategory.ORDER, newOrder);
    return res;
  }

  async rejectOrder(id: string, data: any) {
    const order = (await this.findOne({
      filter: { _id: id },
    })) as unknown as Order;
    const user = getUser();
    if ([OrderStatus.PAID, OrderStatus.VALIDATED, OrderStatus.CREDIT_REJECTED].includes(order.status)
      && [UserCategory.Commercial, UserCategory.Administrator].includes(user.category)) {
      order.status = data?.type == 'reject' ? OrderStatus.CREDIT_REJECTED : OrderStatus.CANCELLED

      const isOrderCancellationIssued = order?.cancellationStatus === CancellationStatus.ISSUE;

      const updateData = {
        status: order?.status,
        [data?.type == 'reject' ? 'rejectReason' : 'cancelReason']: data?.reason,
        [data?.type == 'reject' ? 'dates.rejected' : 'dates.cancel']: moment().valueOf(),
      };

      if (isOrderCancellationIssued) updateData.cancellationStatus = CancellationStatus.ACCEPTED;

      await this.update({ _id: id }, updateData);

      this.notificationSrv.generateActionNotification(NotificationCategory.ORDER, order);

      const res = await this.userSrv.findOne({ filter: { "associatedCompanies._id": order?.company?._id?.toString() } })
      this.queueClient.emit(data?.type == 'reject' ? 'order_rejected' : 'order_canceled', {
        order_ref: order?.appReference,
        [data?.type == 'reject' ? 'order_rejected_date' : 'order_cancel_date']: moment().format('DD/MM/YYYY'),
        [data?.type == 'reject' ? 'rejectReason' : 'cancelReason']: data?.reason,
        total: order?.cart?.amount?.TTC,
        email: order?.user?.email,
        order_created_date: moment(order?.dates?.created).format('DD/MM/YYYY'),
        username: `${order?.user?.firstName} ${order?.user?.lastName}`,
        commercial_email: res?.email,
        ...order,
      });
      const order_created_date = moment(order?.dates?.created).format('DD/MM/YYYY')
      this.queueClient.emit('sms_received',
        { receiver: order?.user?.tel, message: `Bonjour ${order?.user?.lastName || order?.user?.firstName || order?.company?.name}, votre commande numero ${order?.appReference} du ${order_created_date} a été ${data?.type == 'reject' ? 'rejetée' : 'annulée'} par le service client.\nMotif : ${data?.reason}` });

      this.queueClient.emit('sms_received',
        { receiver: res?.tel, message: `Bonjour ${res?.lastName || res?.firstName}, La commande numero ${order?.appReference} de ${order?.company?.name} du ${order_created_date} a été ${data?.type == 'reject' ? 'rejetée' : 'annulée'} par le service client.\nMotif : ${data?.reason}` });
      return { status: HttpStatus.OK, message: await t(data?.type == 'reject' ? 'REJECT_ORDER' : 'CANCEL_ORDER') };
    }
  }

  sendSmsNotificationTOCus(order: Order) {
    const itemsStr = order?.cart?.items.map((item) => {
      const qty = item?.quantity * (item?.packaging?.unit?.value / 1000);
      return `${item?.product?.label}: ${qty.toFixed(2)}t`;
    })
      .join(",");

    this.queueClient.emit('sms_received',
      { receiver: order?.user?.tel, message: `Bonjour ${order?.user?.firstName}. Votre commande ${order?.appReference} de ${itemsStr} a bien été reçue.\n Merci de faire confiance à La Pasta.` });

  }

  sendSmsNotificationToCommercial(order: Order) {
    this.queueClient.emit('commercial_validation',
      { receiver: '', message: ` Bonjour, le client ${order?.user?.lastName} a effectué une commande sur LaPasta.` });
  }

  async sendAfrilandOTP(requestBody: any) {
    this.paymentClient.send(
      { cmd: `${PaymentMode.AFRILAND}_OTP` },
      requestBody,
    );
  }

  // VISA
  async orderGenerateVisaKey() {
    return await lastValueFrom(this.paymentClient.send({ cmd: `${PaymentMode.VISA}_generate_visa_key` }, {}));
  }

  async setupCompletionWithFlexTransientToken(body: any) {
    return await lastValueFrom(this.paymentClient.send({ cmd: `${PaymentMode.VISA}_setup_payer_auth` }, { ...body }));
  }

  async AuthorizationWithPayEnrollAuthenticationNeeded(user: User, body: any) {
    const { order, options } = body
    const currenOrder = await this.createOrder(user, order)
    const orderId = currenOrder.data

    return await lastValueFrom(this.paymentClient.send({ cmd: `${PaymentMode.VISA}_authorization_pay_enroll` }, { orderId, options }));

  }

  async authorizationWithPayerAuthValidation({ transactionId, transientTokenId, user }) {
    return await lastValueFrom(this.paymentClient.send({ cmd: `${PaymentMode.VISA}_cybersource-callback` }, { transactionId, transientTokenId, user }));

  }

  private async generateAppReference(storeRef: string) {
    const dateStr = moment().format('YYMMDDHHmmss');
    const random = Math.floor(Math.random() * 999) + 100;
    const mapped = {
      L10: 'BON',
      K10: 'KRIB',
      CM10020: 'NOM',
      CM12000: 'DEPYAO',
      CM12010: 'DEPFIG',
      CM12020: 'DEPMAR',
      CM12030: 'DEPNGA',
      CM12040: 'DEPKYE',
      CM12070: 'DEPGAB',
      CM12090: 'DEPBEL',
      CM12100: 'DEPGAR',
      CM12110: 'DEPKOU',
    };

    const currAppRef = `${mapped[storeRef] ?? 'CUST'}${dateStr}`.substr(0, 12) + `${random}`;
    const document = await this.orderRepository.findOne({ filter: { appReference: currAppRef } });

    // AppReference déjà utilisé, on relance la fonction
    if (document)
      return await this.generateAppReference(storeRef);


    return currAppRef;
  }

  private setDateFilter(query: QueryFilter) {
    query['created_at'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }

  private loadOrderEventData(order: Order, options: { dailyNbrOrder?: number, user?: User, userValidate?: Logo, allUserMails?: Partial<User[]>, orderArrivalTime?: string }) {
    const events = {
      [PaymentMode.MY_ACCOUNT]: MyAccountPaymentEvent,
      [PaymentMode.AFRILAND]: AfrilandPaymentEvent,
      [PaymentMode.ORANGE_MONEY]: OrangeMoneyEvent,
      [PaymentMode.MOBILE_MONEY]: MobileMoneyEvent,
      [PaymentMode.EXPRESS_EXCHANGE]: ExpressExchangePaymentEvent,
      [PaymentMode.VISA]: VisaPaymentEvent,
      [PaymentMode.CREDIT]: CreditPaymentEvent,
      [PaymentMode.LOW_BALANCE]: LowBalanceEvent,
      [PaymentMode.M2U]: M2UEvent,
    };
    return new events[order.payment.mode.id](order, options);
  }

  async verifyM2UWalletNumber(requestBody: Data): Promise<any> {
    const res = await lastValueFrom(this.paymentClient.send({ cmd: 'verify-wallet' }, { ...requestBody }));
    if (res instanceof HttpException)
      throw new HttpException(t('error.M2U_VERIFY_WALLET'), HttpStatus.NOT_FOUND);

    const data = { ...res[0] };

    if (data?.Result !== 'Success' && !data?.WalletNumber)
      throw new HttpException(`${t('error.M2U_VERIFY_WALLET')}.\t\n${data?.Description}`, HttpStatus.NOT_FOUND);

    return setResponse(HttpStatus.FOUND, data?.Description, data);
  }

  async paymentRequest(requestBody: Data, user: User): Promise<any> {
    const { paymentRequest, order } = requestBody;

    Permission.paymentAuthorization(
      getUser(),
      PaymentAction[PaymentMode[order?.payment?.mode?.id]],
    );

    const paymentRes = await lastValueFrom(this.paymentClient.send(
      { cmd: `${PaymentMode.M2U}_pay_order` }, { ...paymentRequest }));

    if (paymentRes?.Result === "Success") {
      await this.VerifyPointEligibility({ ...order, ...user });
      order['payment'] = { ...order.payment, ...paymentRes };
      return await this.createOrder(user, order);
    }

    throw new HttpException(
      `${t('error.M2U_PAY_ORDER')}.\n${paymentRes[0]?.Description}`, HttpStatus.PAYMENT_REQUIRED, paymentRes);
  }

  async VerifyPointEligibility(order: Order) {
    if (!('company' in order)) return;

    const balance: any = await this.companiesService.getBalanceCompany({ _id: order?.company?._id });

    if ([PaymentMode.MOBILE_MONEY, PaymentMode.ORANGE_MONEY, PaymentMode.M2U, PaymentMode.AFRILAND, PaymentMode.VISA].includes(order?.payment?.mode?.id)
      && (+balance?.Creditlimit > 0 && +balance?.OpenorderAmount > 0)) {
      const query = { filter: { _id: new ObjectId(order?.company?._id?.toString()) } }

      const company = await this.companiesService.findOne({ ...query, projection: { 'points': 1 } });
      await this.companiesService.update(query?.filter, { points: (company?.points || 0) + 1 });
    }
  }

  async generateExportPurchaseOrderLink(_id: string, user: User) {
    try {

      const ttl = moment().add(1800, 'seconds').valueOf();

      const options = { _id, ttl, user: JSON.stringify(user) };

      const pdfCode = await encodePdfCode(options);

      const basePath = `${config.get('baseUrl')}${config.get('basePath')}/orders`;

      return {
        pdfPath: `${basePath}/${pdfCode}/generate-pdf`
      }

    } catch (error) {
      return error;
    }
  }

  async exportPurchaseOrder(code: any) {
    try {
      const data = await decodePdfCode(code);

      const { ttl, _id, user } = data;

      if ((new Date()).getTime() >= ttl) {
        return new Error('ExportLinkExpired');
      }

      return await this.generatePurchaseOrder(_id, user);
    } catch (error) {
      this.logger.error(`An error has occured${error.message}\n${error.stack}`);
      return error.message;
    }
  }

  async generatePurchaseOrder(_id: string, user: string) {
    try {
      const order = await this.findOne({ filter: { _id } }) as unknown as Order;
      const { dataOrder, userValidateLogo } = await this.addLogoWithSignatureInOrder(order);

      const dailyNbrOrder = await this.getDailyOrderCount(order?.company?.erpSoldToId);
      const orderArrivalTime = moment(order.dates.created).format('DD/MM/YYYY');

      const htmlCompile = await getTemplateBody('purchase-order', this.loadOrderEventData(dataOrder,
        { user: JSON.parse(user), userValidate: userValidateLogo, dailyNbrOrder, orderArrivalTime }));

      const pdfString = await pdf.setAttachment(htmlCompile);
      const pdfBuffer = Buffer.from(pdfString, 'base64');

      return {
        client: order?.company?.name ?? order?.user?.firstName,
        pdfBuffer
      };
    } catch (error) {
      return error;
    }
  }

  async addLogoWithSignatureInOrder(order: Order): Promise<{ dataOrder: Order, userValidateLogo: Logo }> {
    const dataImg = (await this.logoSrv.findAll({ filter: { 'company._id': order?.company?._id } }))?.data ?? [];
    order['company']['logo'] = (dataImg.find(el => el?.logoType === LogoType.LOGO))?.value;
    order['company']['signature'] = (dataImg.find(el => el?.logoType === LogoType.SIGNATURE))?.value;

    let userValidateLogo: Logo;
    if (order?.status === OrderStatus.VALIDATED) {
      const snitch = await this.snitchSrv.findOne({
        filter: {
          'oldData.appReference': order?.appReference,
          'newData.status': OrderStatus.VALIDATED,
          'action': 'UPDATE',
          'user.category': UserCategory.Administrator,
        }
      }) as unknown as Snitch;
      userValidateLogo = (await this.logoSrv.findOne({ filter: { 'user._id': snitch?.user?._id, logoType: 2 } })) as unknown as Logo
    }

    return { dataOrder: order, userValidateLogo };
  }

  async addCommentToOrder(orderId: string, userId: string, commentText: string) {
    const user = await this.userSrv.findOne({ filter: { _id: new ObjectId(userId) } }) as unknown as User;
    if (!user) {
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }

    const comment: CommercialComment = {
      text: commentText,
      userCommercial: {
        _id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        category: user.category
      },
      created_at: moment().valueOf(),
    };

    const order = await this.findOne({ filter: { _id: new ObjectId(orderId) } }) as unknown as Order;

    if (!order) {
      throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
    }

    ('comments' in order) ? order.comments.push(comment) : order['comments'] = [comment];

    // Changer le statut si l'utilisateur n'est pas un commercial et que la commande est rejetée
    if (user.category !== UserCategory.Commercial && order.status === OrderStatus.CREDIT_REJECTED) {
      order.status = OrderStatus.PAID;
    }

    return await this.update({ _id: new ObjectId(orderId) }, order);

  }

  async cancellationOrder(id: string, data: { message: string }) {
    const order = await this.findOne({
      filter: { _id: id },
    }) as unknown as Order;

    const updatedOrder = {
      ...order,
      messageCancellation: data?.message,
      cancellationStatus: CancellationStatus.ISSUE,
    };

    const res = await this.update({ _id: id }, updatedOrder);

    if (!(res instanceof Error)) {
      const commercial = await this.userSrv.findOne({ filter: { "associatedCompanies._id": order?.company?._id?.toString() } })
      this.queueClient.emit('order-cancellation', {
        client_name: order?.company?.name || `${order?.user?.firstName} ${order?.user?.lastName}`,
        order_ref: order?.appReference,
        messageCancellation: data?.message,
        order_reject_request_date: moment().format('DD/MM/YYYY'),
        order_created_date: moment(order?.dates?.created).format('DD/MM/YYYY'),
        commercial_email: commercial?.email,
        total: order?.cart?.amount?.TTC,
      });

      this.queueClient.emit('sms_received', {
        receiver: order?.user?.tel,
        message: `Bonjour,\nVotre demande d'annulation pour la commande ${order?.appReference} a bien été émise. Nous la traiterons dans les plus brefs délais.`
      });
    }

    return res;
  }

  async updateOrderForCancelDemand(id: string, orderUpdated: UpdateOrderDto) {

    const res = await this.update({ _id: id }, orderUpdated);

    const order = await this.findOne({
      filter: { _id: id },
    }) as unknown as Order;

    if (!(res instanceof Error)) {
      this.queueClient.emit('sms_received',
        {
          receiver: order?.user?.tel,
          message: `Bonjour ${order?.company?.name || order?.user?.lastName || order?.user?.firstName},\nVotre demande d'annulation pour la commande numéro ${order?.appReference} a été refusée.`
        });
    }
    return res;
  }

  async getDailyOrderCount(companyId: string): Promise<number> {
    const startOfDay = moment().startOf('day');
    const endOfDay = moment().endOf('day');

    this.logger.debug(`[DEBUG] Counting orders for company ${companyId} between ${startOfDay.format('YYYY-MM-DD HH:mm:ss')} and ${endOfDay.format('YYYY-MM-DD HH:mm:ss')}`);

    const count = await this.count({
      "dates.created": {
        $gte: startOfDay.valueOf(),
        $lte: endOfDay.valueOf()
      },
      "company.erpSoldToId": companyId
    });

    const result = Number(count) || 0;
    this.logger.debug(`[DEBUG] Found ${result} orders for today for company ${companyId}`);
    return result;
  }

}
