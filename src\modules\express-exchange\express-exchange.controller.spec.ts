import { Test, TestingModule } from '@nestjs/testing';
import { ExpressExchangeController } from './express-exchange.controller';
import { ExpressExchangeService } from './express-exchange.service';

describe('ExpressExchangeController', () => {
  let controller: ExpressExchangeController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ExpressExchangeController],
      providers: [ExpressExchangeService],
    }).compile();

    controller = module.get<ExpressExchangeController>(ExpressExchangeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
