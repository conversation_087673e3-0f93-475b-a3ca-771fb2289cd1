import { registerDecorator, ValidationOptions } from "class-validator";
import { PromoCodeExistsRule } from "./promo-code-validations-rules";

export function PromoCodeExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'PromoCodeExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: PromoCodeExistsRule,
    });
  };
}