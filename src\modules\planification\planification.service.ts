import { Injectable } from '@nestjs/common';
import { BaseService, convertParams } from '@la-pasta/common';
import { PlanificationRepository } from './repository';

@Injectable()
export class PlanificationService extends BaseService {
  constructor(
    private readonly planificationRepository: PlanificationRepository
  ) {
    super(planificationRepository);
  }

  async getCustomPlanifications(query: QueryOptions) {
    try {
      query = convertParams(query);
      return await this.planificationRepository.getCustomPlanifications(query.filter?.storeId, query.filter?.today);
    } catch (error) {
      return (error);
    }
  }
}
