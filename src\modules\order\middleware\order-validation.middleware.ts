import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { Order, OrderStatus } from '../entities/order.entity';

@Injectable()
export class OrderValidationMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const order = req.body as Order;
    
    // List of required fields to check
    const requiredFields = [
      '_id',
      'appReference',
      'erpReference',
      'payment',
      'cart',
      'status',
      'user',
      'removals',
      'validation',
      'created_at'
    ];

    const missingFields = requiredFields.filter(field => !order[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`Missing required fields in order: ${missingFields.join(', ')}`);
    }

    // Status validation
    if (order.status !== OrderStatus.VALIDATED) {
      throw new Error('Order must be validated before transmission');
    }

    // Cart validation
    if (!order.cart || !order.cart.items || order.cart.items.length === 0) {
      throw new Error('Order must contain at least one item in the cart');
    }

    next();
  }
} 