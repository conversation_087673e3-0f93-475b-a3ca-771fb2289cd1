import { IsNotEmpty, IsOptional, IsString } from "class-validator";
import { Cart } from "@la-pasta-module/price/compute_price";
import { BaseUser, User } from "@la-pasta-module/users";
import { Company } from "@la-pasta-module/companies";
import { Carrier, OrderStatus, OrderStatusDelivery, Payment } from "../entities";
import { Removal } from "@la-pasta-module/planification/entities";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateOrderDto {
  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  payment: Partial<Payment>;

  @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  cart: Cart;

  appReference?: string;

  @IsOptional()
  @IsString()
  customerDeliveryDestination?: string;

  status?: OrderStatus;
  
  @IsOptional()
  statusDelivery?: OrderStatusDelivery;
  
  @IsOptional()
  user?: Partial<User>;

  company?: Partial<Company>;

  @IsOptional()
  customerReference?: string;

  @IsOptional({ message: i18nValidationMessage('validation.NOT_EMPTY') })
  removals?: Removal[];

  dates?: {
    created?: number;
    paid?: number;
    validated?: number;
  };

  @IsOptional()
  optionVisa?: any;

  @IsOptional()
  carrier?: Carrier;
}
