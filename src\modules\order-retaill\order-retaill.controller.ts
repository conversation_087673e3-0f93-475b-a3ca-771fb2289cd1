import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Patch,
  Param,
  Query,
} from '@nestjs/common';
import { OrderRetaillService } from './order-retaill.service';
import { CreateOrderRetaillDto } from './dto/create-order-retaill.dto';
import { getUser } from '@la-pasta/common/helpers';
import { JwtGuard } from '@la-pasta-module/auth';
import { ArchiveSessionDto, OrderRetailRejectDto, OrderRetailValidationDto } from './dto';
import { Permission } from '@la-pasta/infrastructures/security';
import { OrderAction } from '@la-pasta-module/order';
@Controller('order-retaill')
export class OrderRetaillController {
  constructor(private readonly orderRetaillService: OrderRetaillService) { }

  @Post()
  @UseGuards(JwtGuard)
  create(@Body() createOrderRetaillDto: CreateOrderRetaillDto) {
    Permission.orderAuthorization(getUser(), OrderAction.CREATE);
    return this.orderRetaillService.createRetaill(createOrderRetaillDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  async findAll(@Query() query: QueryFilter) {
    //query.status ??= { "$in": [ StatusReseller.CREATED] }
    Permission.orderAuthorization(getUser(), OrderAction.VIEW);
    return await this.orderRetaillService.getOrderRetails({ filter: query });
  }

  @Get('history')
  @UseGuards(JwtGuard)
  async getUserOder(@Query() query: QueryFilter) {
    Permission.orderAuthorization(getUser(), OrderAction.VIEW);
    return await this.orderRetaillService.getUserOrders(getUser(), { filter: query });
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.orderRetaillService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @Get('recap-orderRetail-list')
  @UseGuards(JwtGuard)
  getRecapInfosForOrderList(@Query() query: QueryFilter) {
    return this.orderRetaillService.getRecapInfosForOrderList({ filter: query });
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  async findOne(@Param('id') id: string) {
    Permission.orderAuthorization(getUser(), OrderAction.VIEW);

    return await this.orderRetaillService.findOne({ filter: { _id: id } });
  }

  @Patch(':id/validate')
  @UseGuards(JwtGuard)
  async update(
    @Param('id') id: string,
    @Body() body: OrderRetailValidationDto,
  ) {
    Permission.orderAuthorization(getUser(), OrderAction.VALIDATE);
    return await this.orderRetaillService.validateOrder(id, body);
  }

  @Patch(':id/rejectOder')
  @UseGuards(JwtGuard)
  async updateReject(
    @Param('id') id: string,
    @Body() body: OrderRetailRejectDto,
  ) {
    Permission.orderAuthorization(getUser(), OrderAction.VALIDATE);
    return await this.orderRetaillService.RejectOrder(id, body);
  }

  @Patch('archive')
  async ArchivePoint(
    @Body() body: ArchiveSessionDto,
  ) {
    // Permission.orderAuthorization(getUser(), OrderAction.UPDATE);
    return await this.orderRetaillService.ArchivePoint(body);
  }
}
