import { HttpException, HttpStatus } from "@nestjs/common";
import { convertParams } from "./helpers";
import moment from "moment";

export class ReportingHelpers {

  static getChartBaseQuery(query: QueryFilter) {
    return [
      { $match: { ...query } },
      {
        $project: {
          yearMonthDate: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: { $toDate: { $toLong: '$created_at' } }
            }
          },
        }
      },
      { $group: { _id: '$yearMonthDate', total: { $sum: 1 } } },
      { $sort: { '_id': 1 } },
      { $project: { _id: 0, date: '$_id', total: 1 } }
    ];
  }

  static getChartBaseQueryRemoval(query: QueryFilter) {
    return [
      { $match: { ...query } },
      {
        $project: {
          yearMonthDate: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: { $toDate: { $toLong: '$dates.created' } }
            }
          },
        }
      },
      { $group: { _id: '$yearMonthDate', total: { $sum: 1 } } },
      { $sort: { '_id': 1 } },
      { $project: { _id: 0, date: '$_id', total: 1 } }
    ];
  }

  static getChartEvolutionBaseQuery(query: QueryFilter) {
    return [
      { $match: { ...query } },
      {
        $project: {
          yearMonthDate: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: { $toDate: { $toLong: '$created_at' } }
            }
          },
          items: 1,
          packaging: 1
        }
      },
      {
        $unwind: {
          path: "$items",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $group: {
          _id: '$yearMonthDate',
          ratioToTone: {
            '$first': '$packaging.unit.ratioToTone'
          },
          total: {
            $sum: '$items.quantity'
          }
        }
      },
      { $sort: { '_id': 1 } },
      {
        $project: {
          _id: 0, date: '$_id',
          total: {
            '$divide': [
              '$total', '$ratioToTone'
            ]
          }
        }
      }
    ];
  }

  static generateChartYearDataForRetails(data: any) {

    const arrJan = data.filter(elt => elt?.date?.split('-')[1] === '01');
    const Jan = this.sum(arrJan, 'total');

    const arrFev = data.filter(elt => elt?.date?.split('-')[1] === '02');
    const Fev = this.sum(arrFev, 'total');

    const arrMar = data.filter(elt => elt?.date?.split('-')[1] === '03');
    const Mar = this.sum(arrMar, 'total');

    const arrAvr = data.filter(elt => elt?.date?.split('-')[1] === '04');
    const Avr = this.sum(arrAvr, 'total');

    const arrMai = data.filter(elt => elt?.date?.split('-')[1] === '05');
    const Mai = this.sum(arrMai, 'total');

    const arrJun = data.filter(elt => elt?.date?.split('-')[1] === '06');
    const Jun = this.sum(arrJun, 'total');

    const arrJul = data.filter(elt => elt?.date?.split('-')[1] === '07');
    const Jul = this.sum(arrJul, 'total');

    const arrAou = data.filter(elt => elt?.date?.split('-')[1] === '08');
    const Aou = this.sum(arrAou, 'total');

    const arrSep = data.filter(elt => elt?.date?.split('-')[1] === '09');
    const Sep = this.sum(arrSep, 'total');

    const arrOct = data.filter(elt => elt?.date?.split('-')[1] === '10');
    const Oct = this.sum(arrOct, 'total');

    const arrNov = data.filter(elt => elt?.date?.split('-')[1] === '11');
    const Nov = this.sum(arrNov, 'total');

    const arrDec = data.filter(elt => elt?.date?.split('-')[1] === '12');
    const Dec = this.sum(arrDec, 'total');

    return {
      labels: ['Jan', 'Fev', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aou', 'Sep', 'Oct', 'Nov', 'Dec'],
      data: [Jan, Fev, Mar, Avr, Mai, Jun, Jul, Aou, Sep, Oct, Nov, Dec]
    };
  }

  static generateChartMonthData(dataChart: any, query: any) {
    const labels = [];
    const data = [];

    const currentMonth = query?.startDate?.split('-')[1] ?? (new Date).getMonth() + 1;
    dataChart = dataChart.filter(elt => elt?.date?.split('-')[1] === `${currentMonth}`);

    for (let index = 1; index <= 31; index++) {
      const arr = dataChart.filter(elt => elt?.date?.split('-')[2].replace(/^0+/, '') === `${index}`);
      const total = this.sum(arr, 'total')
      labels.push(`${index}`);
      data.push(total);
    }

    return { labels, data };
  }

  static avg(arr: unknown[], key: string): number {
    const result = (this.sum(arr, key) / arr.length) / 100 || 0;
    return Math.ceil(result);
  }

  static sum(arr: unknown[], key: string): any {
    let result = 0;
    arr.forEach(elt => result += elt[key]);
    return result;
  }


}