import { MongoDatabase } from "@la-pasta/database";
import { setLoader, stopLoader } from "scripts/common";
import { Company } from "@la-pasta-module/companies";
import { User } from "@la-pasta-module/users";

const database = MongoDatabase.getInstance();
const collectionName = 'companies';
const collectionNameUsers = 'users';

export async function InsertCompanyCommercial() {
    try {
        setLoader('Insertion des Commerciaux dans les Compagnies\n');

        const db = await database.getDatabase();

        const companiesInDB = await db.collection(collectionName).find({ associatedCommercial: { $exists: false } }).toArray() as unknown as Company[];

        for (const companyDoc of companiesInDB) {

            let commercial = await db.collection(collectionName).findOne({ category: 200 }) as unknown as User;

            const companyCommercial = {
                _id: commercial?._id.toString(),
                firstName: commercial?.firstName,
                lastName: commercial?.lastName,
                tel: commercial?.tel,
                email: commercial?.email,
                enable: commercial?.enable
            };

            // await db.collection(collectionName).updateOne({ _id: companyDoc?._id }, { $set: { associatedCommercial: companyCommercial } });

        }

        stopLoader(true);
    } catch (error) {
        console.error(error);
    }
}


