import { UnitService } from '../unit';
import { t } from '@la-pasta/common';
import { CreatePackagingDto } from './dto';
import { Injectable } from '@nestjs/common';
import { BaseService } from 'src/common/base';
import { NotFoundException } from '@nestjs/common';
import { PackagingRepository } from './repository';

@Injectable()
export class PackagingsService extends BaseService {
  constructor(
    private readonly packagingRepository: PackagingRepository,
    private readonly unitService: UnitService
  ) {
    super(packagingRepository);
  }

  async createPackage(createPackageDto: CreatePackagingDto) {
    this.verifyIfUnitExist(createPackageDto.unit);

    createPackageDto.unit = await this.getPackageUnit(createPackageDto.unit) as any;

    return this.create(createPackageDto);
  }

  async findPackage(query: QueryOptions) {
    return await this.findOne(query);
  }

  private async getPackageUnit(unitId: string) {
    return await this.unitService.findOne({ filter: { _id: unitId }, projection: { created_at: 0, enable: 0, _id: 0 } })
  }

  private async verifyIfUnitExist(unitId: string) {
    const unit = await this.getPackageUnit(unitId);
    if (!unit) throw new NotFoundException(t('error.UNIT_NOT_FOUND'))
  }
}
