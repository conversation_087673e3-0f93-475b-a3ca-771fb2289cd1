import { Injectable, Inject, OnModuleInit, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { promises as fs } from 'fs';
import * as path from 'path';

@Injectable()
export class ManualOrderService implements OnModuleInit {
  protected logger: Logger;
  private readonly MANUAL_ORDER_CACHE_KEY = 'ACTIVE_MANUAL_ORDER';
  private readonly CONFIG_FILE_PATH = path.join(process.cwd(), '/src/common/cache/manual-order.ts');

  constructor(@Inject(CACHE_MANAGER) private readonly cacheManager: Cache) {
    this.logger = new Logger(this.constructor.name)
  }

  async onModuleInit() {
    await this.preloadCache();
  }

  private async preloadCache() {
    const value = await this.loadFromFile();
    if (value !== undefined) {
      await this.cacheManager.set(this.MANUAL_ORDER_CACHE_KEY, value);
    } else {
      this.logger.warn('Unable to preload cache, value undefined');
    }
  }


  async getStateManualOrderSetting(): Promise<boolean> {
    try {
      let cachedValue = await this.cacheManager.get<boolean>(this.MANUAL_ORDER_CACHE_KEY);

      if (!cachedValue || (typeof cachedValue === "object" && Object.keys(cachedValue).length === 0)) {
        cachedValue = await this.loadFromFile();
        if (cachedValue !== undefined) {
          await this.cacheManager.set(this.MANUAL_ORDER_CACHE_KEY, cachedValue);
        } else {
          return false;
        }
        await this.cacheManager.set(this.MANUAL_ORDER_CACHE_KEY, cachedValue);
      }
      return cachedValue;
    } catch (error) {
      this.logger.error(error);
    }
  }
  async updateStateManualOrderSetting(enabled: boolean): Promise<void> {
    try {
      await this.cacheManager.set(this.MANUAL_ORDER_CACHE_KEY, enabled);
      await this.saveToFile(enabled);
    } catch (error) {
      this.logger.error(error);
    }
  }
  private async loadFromFile(): Promise<boolean> {
    try {
      const data = await fs.readFile(this.CONFIG_FILE_PATH, 'utf-8');
      const match = data.match(/manual_order:\s*(true|false)/);
      if (match) {
        return match[1] === 'true';
      }
      return false;
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  private async saveToFile(enabled: boolean): Promise<void> {
    try {
      const data = `export const manual_order = {\n  manual_order: ${enabled}\n};\n`;
      await fs.writeFile(this.CONFIG_FILE_PATH, data);
    } catch (error) {
      this.logger.error(error);
    }
  }
}