import { Test, TestingModule } from '@nestjs/testing';
import { ScannerDataController } from './scanner-data.controller';
import { ScannerDataService } from './scanner-data.service';

describe('ScannerDataController', () => {
  let controller: ScannerDataController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ScannerDataController],
      providers: [ScannerDataService],
    }).compile();

    controller = module.get<ScannerDataController>(ScannerDataController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
