import { Test, TestingModule } from '@nestjs/testing';
import { ErpItemIdController } from './erp-item-id.controller';
import { ErpItemIdService } from './erp-item-id.service';

describe('ErpItemIdController', () => {
  let controller: ErpItemIdController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ErpItemIdController],
      providers: [ErpItemIdService],
    }).compile();

    controller = module.get<ErpItemIdController>(ErpItemIdController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
