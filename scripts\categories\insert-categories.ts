import readline from 'readline';
import { insertCategories } from './categories.script';

(async () => {
    const prompt = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    console.log(`* Vous êtes sur le point inséré les catégories de produit, dans la base de données. \n`);

    prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {
        if (answer == 'y') {
            await insertCategories();
        }

        prompt.close();
    })

    prompt.on('close', () => {
        process.exit()
    })
})();