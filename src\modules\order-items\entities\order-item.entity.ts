import { Category } from "@la-pasta-module/category/entities";
import { OrderStatus } from "@la-pasta-module/order/entities";
import { User } from "@la-pasta-module/users/entities";


export class OrderItem {
    _id?: string;
    cart?: MarketPlaceCart;
    user: User;
    status?: OrderStatus;
    appReference?: string;
    validation?: {
        user: Partial<User>;
        date: number;
        reason?: string;
    }
    created_at?: number;
    isMobile?:boolean

}

export class MarketPlaceCart {
    items: Items;
    quantity: number;
    amount?: MarketOrderPrice;
    optionsDiscount?: { points?: boolean };
}

export class Items {
    _id: string
    name: string;
    image: string;
    sku: string;
    price: number;
    description: string;
    category: Category;
}

export declare type MarketOrderPrice = {
    HT: number;
    VAT: number;
    TTC: number;
    discount?: Discount;
};

export class Point {
    validate: number;
    unValidated: number;
    status: FidelityStatus
}

enum FidelityStatus {
    AMIGO = 1,
    COLOMBE = 2,
    PELICAN = 3,
}
