import { User } from "@la-pasta-module/users";

export class Invitation {
    _id: string;
    referrerId: string;
    referrer?: Partial<User>;
    prospectTel: number;
    status: InvitationStatus;
    created_at: Date;
    updated_at?: Date;
    accepted_at?: Date;
    referredUserId?: string;
}

export enum InvitationStatus {
    PENDING = 200,
    VALIDATED = 300,
    COMPLETED = 400,
    EXPIRED = 500,
}