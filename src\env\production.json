{"env": "production", "port": 3001, "host": "api", "db": {"host": "mongodb", "name": "la-pasta"}, "db_images": {"host": "mongodb", "name": "mycimencam-images", "auth": {"user": "", "password": ""}}, "jwt": {"secret": "n1l894y7c0br3k7", "expiration": "168h"}, "baseUrl": "https://lapasta.com/", "basePath": "api/v3", "queue": {"port": 3000, "host": "queue-api"}, "payment": {"port": 3000, "host": "payment-api"}, "jde": {"port": 3000, "host": "jde-api"}, "cron": {"port": 3000, "host": "localhost"}, "eexTransaction": {"login": "express_exchange_mcm_prod", "password": "mBLIq7+FY9(kyH#S6$bnvRzM%K&QJC)w"}, "rabbitmq": {"hostname": "mycimencam.com", "port": 7689, "protocol": "amqps", "username": "londo", "password": "@cWqmjYisoSQZjuHu5wwdfvvNvE8a1hNrJ2kIatyVN02ZBnUMEtfANhiqTmexbzahzTJ05y1"}, "fluentdHost": "**************", "minimalMobileBuildVersion": {"android": "1.4.21", "ios": "1.5.1"}, "isDisableOrderProcess": false, "isSendingDataViaEndpoint": true, "base_url_logistic_server": "https://cadyst-logistic.londo-tech.com/api/v1"}