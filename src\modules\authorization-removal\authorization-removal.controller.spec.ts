import { Test, TestingModule } from '@nestjs/testing';
import { AuthorizationRemovalController } from './authorization-removal.controller';
import { AuthorizationRemovalService } from './authorization-removal.service';

describe('AuthorizationRemovalController', () => {
  let controller: AuthorizationRemovalController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthorizationRemovalController],
      providers: [AuthorizationRemovalService],
    }).compile();

    controller = module.get<AuthorizationRemovalController>(AuthorizationRemovalController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
