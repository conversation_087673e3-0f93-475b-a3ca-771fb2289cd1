import { IsBoolean, IsInt, <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON>ptional, IsString} from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateLocationDto {
    @IsOptional()
    _id: string;
  
    @IsOptional()
    @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
    enable: boolean;
  
    @IsOptional()
    @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
    created_at:number;


    @IsNotEmpty()
    @IsString({ message: 'la region doit etre une chaine de caractére' })
    region:string;

    @IsNotEmpty()
    category:any;

    @IsNotEmpty()
    cities:any;

    @IsNotEmpty()
    defautLocation:boolean;


}

