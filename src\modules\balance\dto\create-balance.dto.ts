import { Company } from "@la-pasta-module/companies";
import { IsNotEmpty } from "class-validator";

export class CreateBalanceDto {
  @IsNotEmpty()
  company: Partial<Company>;

  @IsNotEmpty()
  othersAmount: string;

  @IsNotEmpty()
  date: string;

  @IsNotEmpty()
  creditLimit: string;

  @IsNotEmpty()
  paymentTerms: string;

  @IsNotEmpty()
  Payments: string;

  @IsNotEmpty()
  invoiceInProgress: string;

  @IsNotEmpty()
  invoicedDelayed: string;

  @IsNotEmpty()
  openOrderAmount: string;

}
