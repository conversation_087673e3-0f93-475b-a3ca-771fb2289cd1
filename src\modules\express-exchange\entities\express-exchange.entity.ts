import { Company } from "@la-pasta-module/companies";
import { User } from "@la-pasta-module/users";

export class ExpressExchangeTransaction {
    _id?: string
    user?: Partial<User>
    transactionId?: string
    amount?: number
    cimmencamAccount?: string
    company?: Partial<Company>
    status?: string
    transactionReason?: string
    referenceKey?: string
    transactionDate?: string;
    address?: Partial<Address>;
}

export enum StatusTransaction {
    CANCEL = 'CANCEL',
    PROCESSING = 'PROCESSING',
    AWAIT_RELOAD = 'PENDING',
    RELOADED = 'RECHARGE'
}

export enum ReferenceKey {
    EEXPAY = 'EEXPAY' //Express exchange payment process
}