import { HttpStatus, Injectable } from '@nestjs/common';
import { BaseService, convertParams, setResponse } from '@la-pasta/common';
import { LocationsRepository } from './repository';
import { CreateLocationDto } from './dto';
import moment from 'moment';

@Injectable()
export class LocationsService extends BaseService {

  constructor(
    private readonly locationsRepository: LocationsRepository,
  ) {
    super(locationsRepository)
  }


  async createLocation(location: CreateLocationDto) {
    location.created_at = moment().valueOf();

    const { region } = location;
    const document = await this.locationsRepository.findOne({ filter: { region: `${region}` } });
    if (document) {
      return setResponse(
        HttpStatus.NON_AUTHORITATIVE_INFORMATION,
        'Existing Locations',
        document?._id);
    }

    const locationCreated = await this.create(location);
    return setResponse(
      HttpStatus.CREATED,
      'SUCCEFULY CREATED LOCATION',
      locationCreated.data);
  }

  // async getLocations(query: QueryOptions) {

  //   return await this.findAll(query);

  // }

  async getLocations(query: QueryOptions) {
    const region = query?.filter?.region ?? null;
    const locations: getAllResult = (region)
      ? { data: await this.findAllAggregate(this.generateFindAggregation(query, region)), count: null }
      : await this.findAll(query);

    locations.count = (region) ? locations.data.length : locations.count;

    return locations;
  }

  private generateFindAggregation(query: QueryOptions, region: string): any[] {
    query = convertParams(query);
    delete query?.filter?.region;
    return [
      { $project: { _id: 1, cities: 1, category: 1, enable: 1, defautLocation: 1, locationParticular: 1, locationEmployee: 1, region: { $toUpper: "$region" } } },
      { $match: { region: region?.toUpperCase(), ...query?.filter } }
    ];
  }

}
