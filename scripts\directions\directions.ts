import { MongoDatabase } from "@la-pasta/database";
import { ObjectId } from "mongodb";
import moment from "moment";
import { dropCollection, setLoader, stopLoader } from "../common";

const directions: any[] = [{
  "_id": "621dcda5aa44e43d68e7731d",
  "DIRECTIONS": "DUB",
  "LIBELLE": "DIRECTION D'USINE BONABERI"
}, {
  "_id": "621dcda5aa44e43d68e77315",
  "DIRECTIONS": "BPE",
  "LIBELLE": "BETON PRÊT A L'EMPLOI"
}, {
  "_id": "621dcda5aa44e43d68e77317",
  "DIRECTIONS": "DFC",
  "LIBELLE": "DIRECTION FINANCIERE ET COMPTABLE"
}, {
  "_id": "621dcda5aa44e43d68e77318",
  "DIRECTIONS": "DG",
  "LIBELLE": "DIRECTION GENERALE"
}, {
  "_id": "621dcda5aa44e43d68e7731a",
  "DIRECTIONS": "DL",
  "LIBELLE": "DIRECTION LOGISTIQUE"
}, {
  "_id": "621dcda5aa44e43d68e7731b",
  "DIRECTIONS": "DM",
  "LIBELLE": "DIRECTION MARKETING"
}, {
  "_id": "621dcda5aa44e43d68e7731f",
  "DIRECTIONS": "DUN",
  "LIBELLE": "DIRECTION D'USINE NOMAYOS"
}, {
  "_id": "621dcda5aa44e43d68e77320",
  "DIRECTIONS": "DV",
  "LIBELLE": "DIRECTION DES VENTES"
}, {
  "_id": "621dcda5aa44e43d68e7731c",
  "DIRECTIONS": "DRH",
  "LIBELLE": "DIRECTION DES RESSOURCES HUMAINES"
}];

const database = MongoDatabase.getInstance();
const collectionName = 'directions';

export async function insertDirections() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des directions\n');

    directions.forEach(direction => { direction._id = new ObjectId(direction._id), direction.enable = true; direction.create_at = moment().valueOf() });
    const insertedDirections = await (await database.getDatabase()).collection(collectionName).insertMany(directions);
    stopLoader(insertedDirections);
  } catch (error) {
    console.error(error)
  }
}