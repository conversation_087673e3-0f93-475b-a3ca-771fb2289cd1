import { Module } from '@nestjs/common';
import { TechnicalSheetService } from './technical-sheet.service';
import { TechnicalSheetController } from './technical-sheet.controller';
import { TechnicalSheetRepository } from './repository/technical-sheet.repository';
import { FirebaseCloud } from '@la-pasta/infrastructures/image';

@Module({
  controllers: [TechnicalSheetController],
  providers: [TechnicalSheetService, FirebaseCloud, TechnicalSheetRepository]
})
export class TechnicalSheetModule { }
