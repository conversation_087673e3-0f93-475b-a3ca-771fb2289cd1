import { Module } from '@nestjs/common';
import { config } from 'convict-config';
import { JdeModule } from '@la-pasta-module/jde/jde.module';
import { CompaniesModule } from '@la-pasta-module/companies';
import { ReloadBalanceService } from './reload-balance.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ReloadBalanceController } from './reload-balance.controller';
import { PaymentsRepository } from '@la-pasta-module/payments/repository';
import { ReloadBalanceRepository } from './repository/reload-balance.repository';

@Module({
  controllers: [ReloadBalanceController],
  imports: [
    ClientsModule.register([
      { name: "JDE", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
      { name: "PAYMENT", transport: Transport.TCP, options: { host: config.get('payment.host'), port: config.get('payment.port') } },
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
      { name: "CRON", transport: Transport.TCP, options: { host: config.get('cron.host'), port: config.get('cron.port') } },

    ]),
    // UsersModule,
    // forwardRef(() => CompaniesModule), 
    JdeModule,
    CompaniesModule
  ],
  providers: [
    ReloadBalanceService,
    ReloadBalanceRepository, PaymentsRepository
  ],

})
export class ReloadBalanceModule { }
