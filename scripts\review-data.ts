import readline from 'readline';
import { formatLocation } from "./shippings/format-location";
import { changePriceRegionCase, updateAddressStorePrice } from './price-store';
import { updateCompaniesCategoriesInReloadBalance } from './reload-balances/insert-company-category.scripts';

(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'executer un script pour la revue des données.')

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {
    if (answer == 'y') {

      // await formatLocation({ defautLocation: { $exists: false }, enable: true });
      await changePriceRegionCase()
      await updateAddressStorePrice()
      await updateCompaniesCategoriesInReloadBalance();
    }

    prompt.close();
  })

  prompt.on('close', () => {
    process.exit()
  })
})()