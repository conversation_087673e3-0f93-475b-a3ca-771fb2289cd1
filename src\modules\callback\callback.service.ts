import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { PaymentMode } from '@la-pasta-module/order';
import { config } from 'convict-config';
import moment from 'moment';
import { UsersService } from '@la-pasta-module/users';
import { ObjectId } from 'mongodb';

@Injectable()
export class CallbackService {
  constructor(
    @Inject('PAYMENT') private readonly paymentClient: ClientProxy,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,
    private readonly userService: UsersService,
  ) { }

  sendOrangeCallBackResponse(requestBody: unknown) {
    return this.paymentClient.send(
      { cmd: `${PaymentMode.ORANGE_MONEY}_callback` },
      requestBody,
    );
  }

  sendAfrilandOTP(requestBody: unknown) {
    return this.paymentClient.send(
      { cmd: `${PaymentMode.AFRILAND}_OTP` },
      requestBody,
    );
  }

  async generateActivationKey(authorization, email) {
    const login = config.get('londo.login');
    const password = config.get('londo.password');
    const encodedCredentials = Buffer.from(`${login}:${password}`).toString(
      'base64',
    );
    const expectedAuthorization = `Basic ${encodedCredentials}`;

    if (authorization !== expectedAuthorization)
      throw new Error('Bad Authorization credentials');

    if (!email) throw new Error('Bad request');

    const value = this.getRandomString(7);
    const expirationDate = moment().add(2, 'day').toDate().getTime();

    try {
      const user = await this.userService.findOne({ filter: { email } });

      await this.userService.update(
        { _id: user?._id.toString() },
        { afrilandActivationKey: { value, expirationDate } },
      );
      this.queueClient.emit('validation_key', {
        email: user?.email,
        key: value,
        username: `${user?.firstName} ${user?.lastName}`,
        expire_date: moment(expirationDate).locale('fr').format("DD MMMM YYYY, HH:mm")
      });
      return {
        status: 201,
        activation_key: value,
        expiration_date: new Date(expirationDate).toISOString(),
      };
    } catch (err) {
      return err;
    }
  }

  async verifyValidationKey(authorization, validationKey) {
    const afrilandLogin = config.get('afriland.login');
    const afrilandPassword = config.get('afriland.password');
    const afrilandAuthorization = `Basic ${Buffer.from(
      `${afrilandLogin}:${afrilandPassword}`,
    ).toString('base64')}`;

    const londoLogin = config.get('londo.login');
    const londoPassword = config.get('londo.password');
    const londoAuthorization = `Basic ${Buffer.from(
      `${londoLogin}:${londoPassword}`,
    ).toString('base64')}`;

    if (![londoAuthorization, afrilandAuthorization].includes(authorization)) {
      throw new Error('Bad Authorization credentials');
    }

    if (!validationKey) {
      throw new Error('ActivationCodeNotProvided');
    }

    try {
      const user = await this.userService.findOne({
        filter: { 'afrilandActivationKey.value': validationKey },
      });

      if (user['afrilandActivationKey.expirationDate'] <= new Date().getTime()) {
        throw new Error('Code de validation expirée');
      }

      return {
        status: 200,
        client: `${user.firstName}  ${user.lastName}`,
        phone: user.tel,
        key: user['afrilandActivationKey']['value'],
        expiration_date: new Date(
          user['afrilandActivationKey']['expirationDate'],
        ).toISOString(),
      };
    } catch (err) {
      return err;
    }
  }

  async setAfrilandPaymentCode(authorization, body) {
    const afrilandLogin = config.get('afriland.login');
    const afrilandPassword = config.get('afriland.password');
    const afrilandAuthorization = `Basic ${Buffer.from(
      `${afrilandLogin}:${afrilandPassword}`,
    ).toString('base64')}`;

    const londoLogin = config.get('londo.login');
    const londoPassword = config.get('londo.password');
    const londoAuthorization = `Basic ${Buffer.from(
      `${londoLogin}:${londoPassword}`,
    ).toString('base64')}`;

    if (![londoAuthorization, afrilandAuthorization].includes(authorization)) {
      throw new Error('Bad Authorization credentials');
    }

    const activationKey = body.activation_key;
    const paymentKey = body.payment_key;

    try {

      const user = await this.userService.findOne({
        filter: { 'afrilandActivationKey.value': activationKey },
      });

      await this.userService.update(
        { _id: user?._id },
        { afrilandKey: paymentKey },
      );

      return {
        status: 200,
        client: `${user.firstName}  ${user.lastName}`,
        message: 'Afriland payment key successfully saved.',
      };
    } catch (err) {
      return err;
    }
  }

  private getRandomString(size) {
    size = size || 10;
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZ';
    let randomstring = '';
    for (let i = 0; i < size; i++) {
      const rnum = Math.floor(Math.random() * chars.length);
      randomstring += chars.substring(rnum, rnum + 1);
    }
    return randomstring;
  }
}
