import { Modu<PERSON>, forwardRef, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { OrderController } from './order.controller';
import { config } from 'convict-config';
import { UsersModule } from '@la-pasta-module/users';
import { OrderService } from './order.service';
import { OrderRepository } from './repository/order.repository';
import { CompaniesModule } from '@la-pasta-module/companies';
import { LogoModule } from '@la-pasta-module/logo/logo.module';
import { SnitchsModule } from '@la-pasta-module/snitchs/snitchs.module';
import { NotificationsModule } from '@la-pasta-module/notifications/notifications.module';
import { LoyaltyProgramModule } from '@la-pasta-module/loyalty-program/loyalty-program.module';
import { AuthorizationRemovalModule } from '@la-pasta-module/authorization-removal/authorization-removal.module';
import { OrderValidationMiddleware } from './middleware/order-validation.middleware';

@Module({
  imports: [
    ClientsModule.register([
      { name: "J<PERSON>", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
      { name: "PAYMENT", transport: Transport.TCP, options: { host: config.get('payment.host'), port: config.get('payment.port') } },
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),

    forwardRef(() => UsersModule),
    forwardRef(() => CompaniesModule),
    forwardRef(() => LogoModule),
    forwardRef(() => SnitchsModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => LoyaltyProgramModule),
    forwardRef(() => AuthorizationRemovalModule),
  ],
  controllers: [OrderController],
  providers: [OrderService, OrderRepository],
  exports: [OrderService, OrderRepository],
})
export class OrderModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(OrderValidationMiddleware)
      .forRoutes(
        { path: '/orders-data', method: RequestMethod.POST }
      );
  }
}
