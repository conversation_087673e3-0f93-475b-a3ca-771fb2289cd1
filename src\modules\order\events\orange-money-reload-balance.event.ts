import { User } from '@la-pasta-module/users';
import { Order, Payment } from '../entities';
import { PaymentReloadBalance, PaymentReloadBalancePayload, ReloadBalance } from '@la-pasta-module/reload-balance/entities/reload-balance.entity';

export class OrangeMoneyReloadBalanceEvent {
    _id: string;
    payment: PaymentReloadBalance;

    constructor(reloadBalance: ReloadBalance) {
        this._id = reloadBalance?._id.toString();
        this.payment = reloadBalance?.payment;
    }
}


