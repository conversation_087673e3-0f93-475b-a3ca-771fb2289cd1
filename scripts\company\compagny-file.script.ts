import moment from 'moment';
import { getDataInExcelFile } from '@la-pasta/common';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { MongoDatabase } from '@la-pasta/database';
import { Company, CompanyCategory } from '@la-pasta-module/companies';
import { User } from '@la-pasta-module/users';

const database = MongoDatabase.getInstance();
const collectionName = 'companies';

export async function insertCompagniesDataFile() {
  try {
    setLoader('Chargement des compagnies depuis le fichier Excel\n');
    const companiesCollection = (await database.getDatabase()).collection(collectionName)

    const datas = getDataInExcelFile('la-pasta.company') as any[];
    const datasUsers = (getDataInExcelFile('la-pasta.users') as any[]).filter(user => user.category === 'COMMERCIAL') as User[];

    let compagnies: Company[] = [];

    for (const data of datas) {
      const company: Company = {
        name: data?.name,
        category: CompanyCategory[data?.category] as any,
        defaultStore: data?.defaultStore,
        nui: data?.nui,
        rccm: data?.rccm,
        tel: data?.tel,
        erpShipToDesc: data?.erpShipToDesc,
        erpSoldToId: data?.erpSoldToId,
        erpShipToId: data?.erpShipToId,
        precompteRate: +data?.precompteRate_value,
        address: {
          commercialRegion: data?.address_commercialRegion,
          region: data?.address_region,
          city: data?.address_city,
          district: data?.address_district,
        },
        enable: true,
        created_at: moment().valueOf(),
      };

      const query = {
        erpSoldToId: data?.erpSoldToId,
      };

      const res = await companiesCollection.updateOne(query, { $set: { ...company } }, { upsert: true });
      if (res?.matchedCount == 0 && !res?.upsertedCount)
        compagnies.push(company);
    }

    if (compagnies?.length) await companiesCollection.insertMany(compagnies as unknown[]);

    stopLoader(true);
    console.log('Compagnies insérées');

  } catch (error) {
    console.error('Erreur lors de l\'insertion des compagnies :', error);
  }
}

