import { Module } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductsController } from './products.controller';
import { ProductRepository } from './repository';
import { FirebaseCloud } from 'src/infrastructures/image';

@Module({
  controllers: [ProductsController],
  providers: [ProductsService, ProductRepository, FirebaseCloud],
  exports: [ProductsService, ProductRepository]
})
export class ProductsModule {}
