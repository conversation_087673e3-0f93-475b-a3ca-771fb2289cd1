import { Controller, Get, Post, Body, Query, Put, HttpCode, UseGuards, Req, Param } from '@nestjs/common';
import { ExpressExchangeService } from './express-exchange.service';
import { CreateExpressExchangeDto } from './dto/create-express-exchange.dto';
import { Api<PERSON>ody, ApiOkResponse } from '@nestjs/swagger';
import { NewToken, RefreshTokenDto } from '@la-pasta-module/auth/dtos';
import { ExxNewToken } from './dto/express-exchange-newToken.dto';
import { JwtGuard } from '@la-pasta-module/auth';
import { Request } from 'express';

@Controller()
export class ExpressExchangeController {
  constructor(private readonly expressExchangeService: ExpressExchangeService,
  ) { }

  @Post('eex-transaction')
  insertExpressExchangeTransaction(@Body() createExpressExchangeDto: CreateExpressExchangeDto) {
    return this.expressExchangeService.addEexTransaction(createExpressExchangeDto);
  }

  @Get('eex-transactions')
  getAllEexTransaction(@Query() query: QueryFilter) {
    return this.expressExchangeService.findAll({ filter: query });
  }

  @Get('eex-transaction')
  checkStatus(@Query() query: QueryFilter) {
    return this.expressExchangeService.checkStatus(query);
  }

  @Put('eex-transaction/:transactionId')
  update(@Param('transactionId') transactionId: string, @Body() updateExpressExchangeDto: any) {
    return this.expressExchangeService.updateEexTransaction({ transactionId }, updateExpressExchangeDto);
  }

  @ApiOkResponse({ description: 'New access token generated', type: NewToken })
  @ApiBody({ type: RefreshTokenDto })
  @Post('eex-transaction/refresh-token')
  @HttpCode(200)
  async refreshToken(@Body('refresh_token') refresh_token: string) {
    return await this.expressExchangeService.refeshToken({ token: refresh_token });
  }

  @ApiOkResponse({ description: 'New access token generated', type: NewToken })
  @ApiBody({ type: ExxNewToken })
  @Get('eex-transaction/get-token')
  @HttpCode(200)
  async newToken(@Req() request: Request) {
    return await this.expressExchangeService.generateToken(request);
  }
}


