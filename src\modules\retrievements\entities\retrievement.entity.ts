export class Retrievement {
    _id: string;
    driverName: string;
    registrationLicenseNumber: string;
    primaryVehicleId: string;
    shipTo: number;
    soldTo: number;
    salesOrderNumber: number;
    shipmentNumber: number;
    loadNumber: number;
    actualShipDate: number;
    actualShipTime: string
    lifeTicketNumber: number;
    details: {
        qtyShipped: string
        qtyOrdered: string
        item: {
            label: string
            img: string
        }
    }
    companyName: string;
    quantityReceive:{
        data:number
    }

}
