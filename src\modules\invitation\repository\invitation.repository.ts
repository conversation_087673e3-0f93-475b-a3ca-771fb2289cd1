import { BaseRepository } from "@la-pasta/common";
import { Injectable } from "@nestjs/common";

@Injectable()
export class InvitationRepository extends BaseRepository {
    constructor() {
        super();
    }

    async getLoockUpData(query: QueryFilter) {
        const aggregations = [
            {
                $match: {
                    ...query
                },
                $lookup: {
                    from: "users",
                    localField: "referrerId",
                    foreignField: "_id",
                    as: "users"
                }
            }
        ];

        return await this.findAllAggregate(aggregations);
    }
}