import { MongoDatabase } from '@la-pasta/database';
import { Store } from '@la-pasta-module/cart';
import moment from 'moment';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';

const database = MongoDatabase.getInstance();
const collectionName = 'stores';

const stores: Omit<Store, '_id'>[] = [
  {
    "storeRef": "PAS10000",
    "label": "USINE BONABÉRI",
    "address": {
      "region": "LITTORAL",
      "city": "Douala"
    }
  }, {
    "storeRef": "PAS10010",
    "label": "USINE DE KRIBI",
    "address": {
      "region": "SUD",
      "city": "Kribi"
    }
  }];

export async function insertStores() {
  try {
    await dropCollection(collectionName);

    setLoader('insertion des points de ventes\n');
    stores.forEach(store => { store.enable = true; store.create_at = moment().valueOf() });
    const insertedStores = await (await database.getDatabase()).collection(collectionName).insertMany(stores);
    stopLoader(insertedStores);
  } catch (error) {
    console.error(error);
  }
}

export async function getStores() {
  try {
    return (await database.getDatabase()).collection(collectionName).find().toArray() as unknown as Store[];
  } catch (error) {
    console.error(error);
  }
}