import { PriceAction } from '@la-pasta-module/price';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class PriceAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      PriceAction.CREATE,
      PriceAction.DELETE,
      PriceAction.UPDATE,
      PriceAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    return user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const PriceAuthorizationInstance = new PriceAuthorization();