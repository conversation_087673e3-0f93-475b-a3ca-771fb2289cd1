import { PartialType } from '@nestjs/swagger';
import { CreateItemDto, PartialCategoryDTO } from './create-item.dto';
import { IsString, IsOptional, ValidateNested, IsNumber, IsBoolean, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { i18nValidationMessage } from 'nestjs-i18n';

export class UpdateItemDto extends PartialType(CreateItemDto) {
    @IsString()
    @IsOptional()
    name?: string;

    @IsString()
    @IsOptional()
    image?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsString()
    @IsOptional()
    sku?: string;

    @IsOptional()
    @IsNumber()
    @Min(0)
    price?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    stock?: number;

    @IsOptional()
    @ValidateNested()
    @Type(() => PartialCategoryDTO)
    category?: PartialCategoryDTO;

    @IsOptional()
    @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
    enable: boolean;

    @IsOptional()
    openingTime: number | null;

    @IsOptional()
    closingTime: number | null;
    
    @IsOptional()
    @IsBoolean()
    isActive: boolean;
}