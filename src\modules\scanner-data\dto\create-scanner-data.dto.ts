import { User } from "@la-pasta-module/users";
import { OrderStatus} from "@la-pasta-module/order";
import { Company } from "@la-pasta-module/companies";
import { IsArray, IsNotEmpty, IsOptional} from "class-validator";
import { Cart } from "@la-pasta-module/price/compute_price";
import { QrCodeData } from "@la-pasta-module/qr-code-management/entities/qr-code.entity";


export class CreateScannerDataDto {
    
  @IsNotEmpty()
  supplier: Partial<Company>;

  @IsNotEmpty()
  cart: Cart;

  @IsNotEmpty()
  user: Partial<User>;

  @IsNotEmpty()
  @IsArray()
  qrCodeData: Partial<QrCodeData>[];

  @IsOptional()
  status?: OrderStatus;

  @IsOptional()
  appReference?: string;

  @IsOptional()
  validation?: {
    user: Partial<User>;
    date: number;
    raison: string;
  }

  @IsOptional()
  dates?: {
    created?: number;
    paid?: number;
    validated?: number;
  }
}
