import { PaymentAction } from '@la-pasta-module/order';
import { User, UserCategory } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class PaymentAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      PaymentAction.MY_ACCOUNT,
      PaymentAction.AFRILAND,
      PaymentAction.ORANGE_MONEY,
      PaymentAction.MOBILE_MONEY,
      PaymentAction.VISA,
      PaymentAction.EXPRESS_EXCHANGE,
      PaymentAction.CREDIT,
      PaymentAction.LOW_BALANCE,
      PaymentAction.M2U
    ] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string, subject?: any): boolean {
    if (permission == PaymentAction.AFRILAND) return user.afrilandKey != null;

    if (permission == PaymentAction.CREDIT) return user.category === UserCategory.EmployeeEntity;

    // if (permission == PaymentAction.MY_ACCOUNT) return ('company' in user);

    if (permission == PaymentAction.LOW_BALANCE) return ('company' in user);

    if (permission == PaymentAction.EXPRESS_EXCHANGE) return ('company' in user);

    return user.enable === true;
  }
}

export const PaymentAuthorizationInstance = new PaymentAuthorization();