import { PartialType } from '@nestjs/swagger';
import { CreateCategoryDto } from './create-category.dto';
import { IsBoolean, IsInt, IsOptional } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class UpdateCategoryDto extends PartialType(CreateCategoryDto) {
    @IsOptional()
    _id: string;

    @IsOptional()
    @IsBoolean({ message: i18nValidationMessage('validation.NOT_BOOLEAN') })
    enable: boolean;

    @IsOptional()
    @IsInt({ message: i18nValidationMessage('validation.NOT_INT') })
    created_at: number;

}
