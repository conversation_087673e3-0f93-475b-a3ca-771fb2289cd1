import { MongoDatabase } from '@la-pasta/database';
import { Profile } from '@la-pasta-module/authorizations';
import { UserCategory } from '@la-pasta-module/users/entities';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { CompanyCategory } from '@la-pasta-module/companies';
import { getCompaniesCategories } from 'scripts/company';

const database = MongoDatabase.getInstance();
const collectionName = 'profiles';

const profiles: Omit<Profile, '_id'>[] = [
  {
    label: 'particular',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'retailer',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'Group',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'Industry',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'Export',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'employeeentity',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'commercial',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'donutanimator',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'administrator',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'baker',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'wholesaler',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
  {
    label: 'gms',
    authorizations: [
      "update_user",
      "create_user"
    ]
  },
];

export async function insertProfiles() {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des profils d\'autorisation\n');
    const insertedProfiles = await (await database.getDatabase()).collection(collectionName).insertMany(profiles);
    stopLoader(insertedProfiles);
  } catch (error) {
    console.error(error);
  }
}

export async function getProfileAuthorization(category: number) {
  try {
    const categoryCompanies = getCompaniesCategories();
    const labelCategory = `${(categoryCompanies.includes(category) ? CompanyCategory : UserCategory)[category]?.toLowerCase()}`
    const profile = await (await database.getDatabase()).collection(collectionName).findOne({
      label: labelCategory
    }) as unknown as Profile;

    if (profile) {
      return profile.authorizations;
    }
  } catch (error) {
    console.error(error);
  }
}
