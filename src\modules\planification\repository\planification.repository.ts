import { BaseRepository } from "@la-pasta/common";

export class PlanificationRepository extends BaseRepository {
  constructor() {
    super();
  }

  async getCustomPlanifications(storeId: string, today: number) {
    return (await this.getCollection()).aggregate([
      {
        '$match': {
          'data.store._id': `${storeId}`,
          'data.end': {
            '$gte': today
          },
          'enable': true
        }
      }, {
        '$project': {
          'data': 1
        }
      }
    ]).toArray();
  }
}