import { Feedback } from "@la-pasta-module/feedbacks/entities";
import { User } from "@la-pasta-module/users";
import { IsNotEmpty, IsOptional } from "class-validator";


export class CreateNotificationDto {

    @IsOptional()
    _id?: object | string;

    @IsOptional()
    feedback?: Feedback;

    @IsOptional()
    title?: string;

    @IsOptional()
    isGeneralNotif?: boolean;

    @IsOptional()
    emailAdmin?: string;

    @IsOptional()
    userId?: string;

    @IsOptional()
    img?: string;

    @IsNotEmpty()
    message: string;

    @IsOptional()
    status?: number;

    @IsOptional()
    dates?: {
        created: number;
        read?: number;
    };
}
