#!/bin/bash

# Get the current branch name
branch_name=$(git rev-parse --abbrev-ref HEAD)

# Get the current date
current_year=$(date +"%Y")

# Regex patterns for different branch types
feature_bugfix_pattern="^(feature|bugfix)\/([a-z0-9-]+)_$current_year(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])$"
release_pattern="^release\/([0-9]+\.[0-9]+(\.[0-9]+)?)$" # matches semantic versions like 1.0 or 1.0.0
hotfix_pattern="^hotfix\/([a-z0-9-]+)_[0-9]+\.[0-9]+(\.[0-9]+)?$"

# Check if the branch name matches any of the patterns
if [[ $branch_name =~ $feature_bugfix_pattern ]]; then
    exit 0
elif [[ $branch_name =~ $release_pattern ]]; then
    exit 0
elif [[ $branch_name =~ $hotfix_pattern ]]; then
    exit 0
elif [[ $branch_name =~ "develop" ]]; then
    exit 0
elif [[ $branch_name =~ "master" ]]; then
    exit 0
else
    echo "Error: Branch name '$branch_name' does not match the required naming convention."
    echo "Expected formats are:"
    echo "  - feature/<name>_<YYYYMMDD>"
    echo "  - bugfix/<name>_<YYYYMMDD>"
    echo "  - release/<release version>"
    echo "  - hotfix/<name>_<affected version>"
    exit 1
fi