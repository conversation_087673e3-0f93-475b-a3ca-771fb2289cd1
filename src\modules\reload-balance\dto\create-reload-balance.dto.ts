import { Company } from "@la-pasta-module/companies";
import { PaymentMode } from "@la-pasta-module/order";
import { User } from "@la-pasta-module/users";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, ValidateIf } from "class-validator";
import { i18nValidationMessage } from "nestjs-i18n";

export class CreateReloadBalanceDto {

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    user: Partial<User>;

    @ApiPropertyOptional()
    @IsOptional()
    status: TransactionStatus;

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    payment?: {
        mode?: {
            id: PaymentMode;
            label: string;
            icon: string;
            txt: string;
            bank: string;
            authoriseLabel: string;

        };
        reference: string;
        amount: number;
        tel?: string;
        transactionId?: string;
        jdeTransactionId?: string;

    }

    @ApiProperty()
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    company: Partial<Company>;

    @ApiPropertyOptional()
    @IsOptional()
    @ValidateIf(o => o.payment.mode.id == PaymentMode.M2U)
    paymentInfo?: QueryFilter;

    @ApiPropertyOptional()
    @IsOptional()
    created_at?: number;

}

export enum TransactionStatus {
    PENDING = 'PENDING',
    SUCCESSFUL = 'SUCCESSFUL',
    INITIATED = 'INITIATED',
    FAILED = 'FAILED',
    EXPIRED = 'EXPIRED',
}
