import { query } from 'express';
import { HttpStatus, Injectable } from '@nestjs/common';
import { CreateItemDto } from './dto/create-item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { BaseService, converDateFilter, setResponse, t } from '@la-pasta/common';
import { ItemRepository } from './repository';
import { FirebaseCloud } from '@la-pasta/infrastructures/image';

@Injectable()
export class ItemsService extends BaseService {

  constructor(private readonly itemsRepository: ItemRepository,
    private firebaseCloude: FirebaseCloud
  ) {
    super(itemsRepository)
  }

  private MILLISECONDS_IN_DAY = 1000 * 60 * 60 * 24;
  private MILLISECONDS_IN_HOUR = 1000 * 60 * 60;
  private MILLISECONDS_IN_MINUTE = 1000 * 60;
  private MILLISECONDS_IN_SECOND = 1000;

  async createItems(createItemDto: CreateItemDto) {

    createItemDto.image = await this.firebaseCloude.getImageUrl(createItemDto.image, createItemDto.name);
    const res = await this.create(createItemDto)

    return setResponse(HttpStatus.CREATED, t('CREATED'), await res.data);
  }


  async updateItems(id: string, item: UpdateItemDto) {
    const dataUriRegExp = /^data:image\/(.*);base64/gm;

    if (item.image && item.image.match(dataUriRegExp) != null)
      item.image = await this.firebaseCloude.getImageUrl(item.image, item.name);

    return await this.update({ _id: id }, item);
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    query = converDateFilter({ filter: query });
    // query['status'] = OrderStatus.PAID;

    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }

  async getItems(query: QueryOptions) {
    const items = await this.findAll(query);
    const res = items.data.map(product => ({
      ...product,
      timeRemaining: this.calculateTimeRemaining(product.marketplaceStatus?.closingTime)
    }));
    return { ...items, data: res };
  }

  private calculateTimeRemaining(endDate?: number | null): string {
    if (endDate === null) return '';
    const now = Date.now();
    const diff = endDate - now;
    if (diff <= 0) return 'Expiré';
    const days = Math.floor(diff / this.MILLISECONDS_IN_DAY);
    const hours = Math.floor((diff % this.MILLISECONDS_IN_DAY) / this.MILLISECONDS_IN_HOUR);
    const minutes = Math.floor((diff % this.MILLISECONDS_IN_HOUR) / this.MILLISECONDS_IN_MINUTE);
    const seconds = Math.floor((diff % this.MILLISECONDS_IN_MINUTE) / this.MILLISECONDS_IN_SECOND);
    return `${days}j ${hours}h ${minutes}m ${seconds}s`;
  }

  async toggleMarketplaceStatus(item: QueryFilter) {
    const { closingTime, openingTime, isActive } = item || {};
    const rewardProducts = await this.findAll({ filter: { enable: true } });
    const results = await Promise.allSettled(
      rewardProducts.data.map(product =>
        this.update({ _id: product._id }, { marketplaceStatus: { closingTime, openingTime, isActive } })
      )
    );
    const hasError = results.some(result => result.status === "rejected");

    if (hasError) {
      return { status: HttpStatus.INTERNAL_SERVER_ERROR, message: "Échec de la mise à jour. Aucune modification appliquée.", errors: results };
    }

    return { status: HttpStatus.OK, message: "Tous les produits ont été mis à jour avec succès." };
  }

}
