import { Injectable } from '@nestjs/common';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { BaseService } from '@la-pasta/common';
import { PaymentsRepository } from './repository';

@Injectable()
export class PaymentsService extends BaseService {
  constructor(
    private readonly paymentsRepository: PaymentsRepository,
  ) {
    super(paymentsRepository)
  }
}
