import { PackagingAction } from '@la-pasta-module/cart';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class PackagingAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      PackagingAction.CREATE,
      PackagingAction.DELETE,
      PackagingAction.UPDATE,
      PackagingAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const PackagingAuthorizationInstance = new PackagingAuthorization();