{"CREATED": "Data created successfully", "UPDATED": "Update successfully", "SAVED": "Data saved successfully", "SUCCESS_OPERATION": "Operation completed successfully", "USER_CREATED": "User created successfully", "PASSWORD_RESET": "Password reset successfully", "PAYMENT_INIT": "Your order has been sent for validation", "NOTIF_CREATED": "Notification created successfully", "NOTIF_REMOVED_FROM_USER": "Notification removed successfully", "NOTIF_DELETED": "Notification deleted successfully", "NOTIFS_DELETED": "Notifications deleted successfully", "PAYMENT_RETAIL_INIT": "Your order has been sent for validation", "VALIDATE_ORDER_RETAILER_AND_CREATE_AE": "Order successfully validated and AE successfully created", "VALIDATE_ORDER_RETAILER": "Order successfully transmitted to logistics", "REJECT_ORDER": "Order rejected", "CANCEL_ORDER": "Order Canceled", "VALIDATE_ORDER": "Order validated", "IMAGE_CREATED": "Image created successfully", "TRANSACTION_CREATED": "Your transaction has been recorded with the status", "IMAGE_UPDATED": "Image updated sucessfully", "EXECEDE_CAPACITY_ANNUAL": "You have exceeded your annual product quota", "error": {"NO_ACCOUNT": "No account is associated with this address", "NO_COMPANY": "Company not found in system", "NO_PROMO_CODE": "Promo code not found in sytem", "PROMO_CODE_DATE_NOT_VALID": "The promo code has expire.", "PROMO_CODE_NOT_VALID": "The promo code is not valid", "INVALID_TOKEN": "Invalid reset token", "NO_RESET_REQUEST": "No password reset request found for this account", "BAD_CREDENTIAL": "Incorrect Credentials", "ON_CREATION": "An error occurred while creating", "ON_UPDATE": "An error occurred while updating", "NOT_FOUND": "No data found", "USER_NOT_FOUND": "No data found found for this client", "UNIT_NOT_FOUND": "No unit found for the sent value", "NO_CATEGORY_AUTHORIZATION": "There are no permissions associated with this category", "PRICE_EXISTE": "This price offer already exists", "CATEGORY_EXIST": "This category already exists", "SHIPPING_EXIST": "This shipping already exists", "ACCOUNT_EXISTE": "This account already exists", "NOT_AUTHORIZE": "You do not have permission to perform this action", "EXPIRE_TIME": "Expired code validity period", "NOT_ACCEPTABLE": "This data is not acceptable", "INSUFFICIENT_BALANCE": "Your account balance is insufficient to place this order", "PROMO_CODE_EXIST": "The promo code already exist", "CURRENT_BALANCE_NOT_FOUND": "The current balance does not exist", "M2U_VERIFY_WALLET": "An error occurred while verifying the wallet", "M2U_PAY_ORDER": "An error occurred while paying for your order", "OCCURRED_ERROR": "An error occurred", "NO_AMOUNT": "The TTC amount should not be less than or equal to zero", "NO_USER_FOR_LOYALTY_PROGRAM": "No user found for loyalty program", "NO_ORDER_FOR_LOYALTY_PROGRAM": "No order found for loyalty program", "NO_ENOUGH_POINT_TO_VALIDATE": "The client doesn't not have enough points to validate this order", "COMPANY_ALREADY_EXISTS": "This company already exists", "WHOLE_SALE_EXIST": "This wholesale already exists"}, "mail": {"RESET_MAIL_SENT": "Password reset email sent", "OTP_CODE_SEND": "Your otp code has been generated and sent successfully"}, "JDE_NUMBER": "Order has been send to JDE", "REWARD_REQUEST_NOT_FOUND": "Reward request not found", "REWARD_REQUEST_ALREADY_VALIDATED": "Reward request already validated", "REWARD_REQUEST_VALIDATED": "Reward request validated", "ERROR_REJECT_ORDER_FAILED": "Failed to reject order", "QR_CODE_NOT_FOUND": "QR code not found"}