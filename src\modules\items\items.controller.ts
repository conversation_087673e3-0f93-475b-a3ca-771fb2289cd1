import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ItemsService } from './items.service';
import { CreateItemDto } from './dto/create-item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { GetUser, JwtGuard } from '@la-pasta-module/auth';
import { getUser } from '@la-pasta/common';
import { ItemsAction } from './actions';
import { Permission } from '@la-pasta/infrastructures/security';
import { BaseUser } from '@la-pasta-module/users';

@Controller('items')
export class ItemsController {
  constructor(private readonly itemsService: ItemsService) { }

  @Post()
  @UseGuards(JwtGuard)
  async createItem(@Body() createItemsDto: CreateItemDto) {
    Permission.itemsAuthorization(getUser(), ItemsAction.CREATE);
    return await this.itemsService.createItems(createItemsDto);
  }

  @Get()
  @UseGuards(JwtGuard)
  async findAllItem(@GetUser() user: BaseUser, @Query() query: QueryFilter) {
    // Permission.itemsAuthorization(user, ItemsAction.VIEW);
    return await this.itemsService.getItems({ filter: query });
  }

  @Get(':id')
  @UseGuards(JwtGuard)
  async findOneItem(@Param('id') id: string, @GetUser() user: BaseUser) {
    Permission.itemsAuthorization(user, ItemsAction.VIEW);
    return await this.itemsService.findOne({ filter: { _id: id } });
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  async updateItem(@Param('id') id: string, @Body() updateItemDto: UpdateItemDto) {
    Permission.itemsAuthorization(getUser(), ItemsAction.UPDATE);
    return await this.itemsService.updateItems(id, updateItemDto)
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    Permission.itemsAuthorization(getUser(), ItemsAction.VIEW);
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.itemsService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }


  @UseGuards(JwtGuard)
  @Patch('/toggle-marketplace/market')
  async toggleMarketplace(@Body() updateItemDto: UpdateItemDto) {
    Permission.itemsAuthorization(getUser(), ItemsAction.UPDATE);
    const res = await this.itemsService.toggleMarketplaceStatus(updateItemDto);
    return res;
  }


}



