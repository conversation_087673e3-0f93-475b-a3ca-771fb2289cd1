import readline from 'readline';
import { insertProductsDataFile } from 'scripts/products/product-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données des produits de Click CADYST, ce processus effacera la collection products.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertProductsDataFile();
      console.log('Données des produits insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();