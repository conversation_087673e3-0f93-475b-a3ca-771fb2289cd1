import { Injectable, NotFoundException } from '@nestjs/common';
import { LogoRepository } from './repository';
import { BaseService } from '@la-pasta/common';
import { CreateLogoDto } from './dto/create-logo.dto';
import { Logo } from './entities/logo.entity';

@Injectable()
export class LogoService extends BaseService {

  constructor(private logoRepo: LogoRepository) {
    super(logoRepo);
  }

  async createLogo(createLogoDto) {
    const logo = await this.verifyIfLogoExists(createLogoDto);
    if (logo instanceof NotFoundException) return await this.create(createLogoDto);

    return await this.update({ _id: logo['_id'] }, createLogoDto);
  }

  private async verifyIfLogoExists(createLogoDto: Logo): Promise<boolean | Logo> {
    const logo = await this.findOne({ filter: { 'company._id': createLogoDto?.company?._id, logoType: createLogoDto?.logoType } }) as Logo;
    if (logo) return logo;

    return false;
  }
}
