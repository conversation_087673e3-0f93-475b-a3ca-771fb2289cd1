import { ErpItemIdAction } from '@la-pasta-module/cart';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

export class ErpItemIdAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return (
      (Object.values(ErpItemIdAction) as string[]).includes(permission) &&
      user.authorizations.includes(permission)
    );
  }

  authorise(user: User, permission: string): boolean {
    return user.enable && user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const ErpItemIdAuthorizationInstance = new ErpItemIdAuthorization();
