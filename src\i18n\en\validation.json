{"NOT_EMPTY": "The field {property} is required", "INVALID_EMAIL": "Invalid email", "INVALID_STRING": "the field {property} should be a string", "INVALID_JWT": "Invalid reset token", "BAD_DATA_URI": "The image is not in base64 format", "NOT_BOOLEAN": "{property} should be a boolean", "NOT_INT": "{property} must be a number", "NOT_NUMBER": "{property} must be a number", "NOT_ARRAY": "{property} must be an array", "MIN_COMPANY_USER": "The company must contain at least one user.", "BAD_PHONE": "Incorrect phone number"}