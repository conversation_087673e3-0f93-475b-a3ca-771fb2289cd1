import { <PERSON>, Get, Post, Body, Patch, Param, Query } from '@nestjs/common';
import { LogoService } from './logo.service';
import { CreateLogoDto } from './dto/create-logo.dto';
import { UpdateLogoDto } from './dto/update-logo.dto';

@Controller('logo')
export class LogoController {
  constructor(private readonly logoService: LogoService) { }

  @Post()
  async create(@Body() createLogoDto: CreateLogoDto) {
    // Permission.logoAuthorization(getUser(), LogoAction.CREATE);

    return await this.logoService.createLogo(createLogoDto);
  }

  @Get()
  async findAll(@Query() query: QueryOptions) {
    // Permission.logoAuthorization(getUser(), LogoAction.VIEW);

    return await this.logoService.findAll({ filter: query });
  }

  @Get(':id')
  findOne(@Param('id') _id: string) {
    // Permission.logoAuthorization(getUser(), LogoAction.VIEW);

    return this.logoService.findOne({ filter: { _id } });
  }

  @Patch(':id')
  update(@Param('id') _id: string, @Body() updateLogoDto: UpdateLogoDto) {
    // Permission.logoAuthorization(getUser(), LogoAction.UPDATE);

    return this.logoService.update({ _id }, updateLogoDto);
  }

}
