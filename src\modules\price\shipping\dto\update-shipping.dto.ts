import { PartialType } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsOptional } from 'class-validator';
import { CreateShippingDto } from './create-shipping.dto';

export class UpdateShippingDto extends PartialType(CreateShippingDto) {
    @IsOptional()
    _id: string;

    @IsOptional()
    @IsBoolean()
    enable: boolean;

    @IsOptional()
    @IsInt()
    created_at:number;

    @IsOptional()
    @IsInt()
    updated_at: number;
}
