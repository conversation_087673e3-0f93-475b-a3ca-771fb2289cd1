###################
# BUILD FOR PRODUCTION
###################

FROM node:16-alpine

ENV NODE_ENV build

COPY package*.json /tmp/

# Install dependecies
# RUN cd /tmp && npm ci && npm cache clean --force && npm i typescript
RUN cd /tmp && npm install && npm i typescript && npm i -g @nestjs/cli

COPY nest-cli.json tsconfig*.json convict-config.ts /tmp/

COPY src /tmp/src

COPY public /tmp/public

COPY views /tmp/views

COPY scripts /tmp/scripts

RUN cd /tmp && npm run build

COPY tsconfig*.json /tmp/dist/



FROM node:16-alpine

COPY package*.json /tmp/

RUN cd /tmp && npm install --omit=dev



FROM node:16-alpine

RUN mkdir -p /usr/src/lapasta

COPY --from=1 /tmp/package*.json /usr/src/lapasta/

COPY --from=1 /tmp/node_modules /usr/src/lapasta/node_modules

COPY --from=0 /tmp/dist /usr/src/lapasta/

WORKDIR /usr/src/lapasta

EXPOSE 3000

CMD npm run start:staging