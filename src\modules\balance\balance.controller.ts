import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { BalanceService } from './balance.service';
import { CreateBalanceDto } from './dto/create-balance.dto';
import { UpdateBalanceDto } from './dto/update-balance.dto';
import { ClientBalance } from './entities/balance.entity';

@Controller('balance')
export class BalanceController {
  constructor(private readonly balanceService: BalanceService) { }

  @Post()
  create(@Body() createBalanceDto: CreateBalanceDto) {
    return this.balanceService.create(createBalanceDto);
  }

  @Get(':_id')
  async findOne(@Param('_id') _id: string) {
    return this.balanceService.findOne({ filter: { _id } });
  }

  @Patch()
  savesData(@Body() createBalanceDto: CreateBalanceDto[]) {
    return this.balanceService.saveDataForBalanceClient(createBalanceDto as unknown as ClientBalance[]);
  }

}
