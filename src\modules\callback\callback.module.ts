import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { CallbackController } from './callback.controller';
import { CallbackService } from './callback.service';
import { config } from 'convict-config';
import { UsersModule } from '@la-pasta-module/users';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'PAYMENT',
        transport: Transport.TCP,
        options: {
          host: config.get('payment.host'),
          port: config.get('payment.port'),
        },
      },
      {
        name: 'QUEUE',
        transport: Transport.TCP,
        options: {
          host: config.get('queue.host'),
          port: config.get('queue.port'),
        },
      },
    ]),
    UsersModule,
  ],
  controllers: [CallbackController],
  providers: [CallbackService],
})
export class CallbackModule { }
