import moment from 'moment';
import fetch from 'node-fetch';
import { faker } from '@faker-js/faker';
import { config } from 'convict-config';
import { Order } from '@la-pasta-module/order';
import { RenderType } from '@la-pasta-module/cart';
import { HttpStatus, Injectable } from '@nestjs/common';
import { BpRepository } from './repository/bp.repository';
import { BaseService, convertFilter } from '@la-pasta/common';
import { AuthorizationRemovalRepository } from './repository/autorization-removal.repository';
import { AuthorizationRemoval, AuthorizationRemovalStatus, RenderType as RenderTypeBP, StatusAES, StatusRemovals } from './entities';
import { OrderStatus } from '@la-pasta-module/order';


@Injectable()
export class AuthorizationRemovalService extends BaseService {
  constructor(
    private readonly authorizationRemovalsRepository: AuthorizationRemovalRepository,
    private readonly bpRepository: BpRepository
  ) {
    super(authorizationRemovalsRepository);
  }

  async getFilterElement(filter: QueryFilter) {
    filter = convertFilter(filter);
    filter['dates.created'] = filter.created_at;
    const { keyForFilters } = filter;

    delete filter.created_at;
    delete filter.keyForFilters

    return await this.authorizationRemovalsRepository.getFilterAuthorization(filter, keyForFilters.split(',') ?? []);
  }

  async getAllAutorizations(filter: QueryFilter) {
    filter = convertFilter(filter);
    filter['dates.created'] = filter.created_at;
    delete filter.created_at;

    return await this.findAll({ filter: filter });
  }
  async getUserRemoval(filter: QueryFilter) {

    if ('startDate' in filter || 'endDate' in filter) {

      this.setDateFilter(filter);

    } else {
      filter = convertFilter(filter, ['status']);
      filter['dates.created'] = filter.created_at;
      delete filter.created_at;
    }
    if ('SoldTo' in filter) {
      const SoldTo = `${filter.SoldTo}`;
      delete filter.SoldTo;
      filter['SoldTo'] = SoldTo;
    }

    if ('loadNumber' in filter) {
      const loadNumber = filter['loadNumber'];
      delete filter['loadNumber'];
      filter['LoadNumber'] = loadNumber
    }



    return await this.authorizationRemovalsRepository.getUserRemovals({ filter: filter });
  }


  private setDateFilter(query: QueryFilter) {
    query['dates.created'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }

  async transmitOrderToCadystLogistic(order: Order) {
    if (order.status !== OrderStatus.VALIDATED) {
      return {
        status: HttpStatus.BAD_REQUEST,
        message: "La commande doit être validée avant d'être transmise à Cadyst Logistic",
      };
    }

    try {
      const res = config.get('isSendingDataViaEndpoint') 
        ? await this.sendAuthorizationRemovalEndpoind(order) 
        : await this.bpRepository.create(order as unknown as Document);

      if (!res) {
        this.logger.error(`Échec de la transmission de la commande ${order._id} à Cadyst Logistic`);
        return {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: "Échec de la transmission de la commande à Cadyst Logistic",
        };
      }

      this.logger.log(`Commande ${order._id} transmise avec succès à Cadyst Logistic`);
      return {
        status: HttpStatus.CREATED,
        message: "Commande transmise avec succès à Cadyst Logistic",
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la transmission de la commande ${order._id} à Cadyst Logistic: ${error.message}`);
      return {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Erreur lors de la transmission: ${error.message}`,
      };
    }
  }

  async sendAuthorizationRemovalEndpoind(data: Order) {
    const res = await fetch(`${config.get('base_url_logistic_server')}/orders`, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.logger.log(`sendAuthorizationRemovalEndpoind: ${res.status} - ${res.statusText} \n error: ${res?.text}`);
    return res?.json()
  }
}
