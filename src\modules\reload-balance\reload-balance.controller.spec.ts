import { Test, TestingModule } from '@nestjs/testing';
import { ReloadBalanceController } from './reload-balance.controller';
import { ReloadBalanceService } from './reload-balance.service';

describe('ReloadBalanceController', () => {
  let controller: ReloadBalanceController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReloadBalanceController],
      providers: [ReloadBalanceService],
    }).compile();

    controller = module.get<ReloadBalanceController>(ReloadBalanceController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
