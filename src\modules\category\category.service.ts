import { ObjectId } from 'mongodb';
import { HttpException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryRepository } from './repository';
import { BaseService, t } from '@la-pasta/common';
import { Category } from './entities';

@Injectable()
export class CategoryService extends BaseService {

  constructor(private readonly categoryRepository: CategoryRepository) {
    super(categoryRepository)
  }

  async createCategory(newCategory: CreateCategoryDto) {
    const categoryExist = await this.checkIfCategoryExist(newCategory);

    if (categoryExist) throw new HttpException(t('error.CATEGORY_EXIST'), 400);

    return await this.create(newCategory);
  }

  async getAllCategories(query: QueryOptions) {
    return await this.findAll(query);
  }

  async updateCategory(filter: QueryFilter, category: UpdateCategoryDto) {
    const categoryExist = await this.checkIfCategoryExist(category as Category);

    if (categoryExist) throw new HttpException(t('error.CATEGORY_EXIST'), 400);

    return await this.update(filter, category);
  }

  private async checkIfCategoryExist(category: Category) {
    let categoryExist: any;

    if (category?._id) {
      categoryExist = await this.findOne({
        filter: {
          $and: [
            { _id: { $ne: new ObjectId(category?._id) } },
            { $or: [{ code: category.code }, { label: category.label }] }
          ]
        }
      })
    } else {
      categoryExist = await this.findOne({
        filter: { $or: [{ code: category.code }, { label: category.label }] }
      });
    }

    if (categoryExist instanceof NotFoundException) return false

    return true;
  }

}
