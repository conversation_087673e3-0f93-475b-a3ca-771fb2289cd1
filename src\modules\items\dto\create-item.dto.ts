import { IsString, IsNumber, IsNotEmpty, ValidateNested, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { MarketplaceStatus } from '../entities/item.entity';

export class PartialCategoryDTO {
    @IsOptional()
    @IsString()
    _id?: string;
  
    @IsOptional()
    @IsString()
    code?: string;
  
    @IsOptional()
    @IsString()
    label?: string;
  
    @IsOptional()
    @IsString()
    description?: string;
  }

  export class MarketplaceStatusDto {
    @IsNotEmpty()
    openingTime: number | null;

    @IsNotEmpty()
    closingTime: number | null;

    @IsNotEmpty()
    @IsBoolean()
    isActive: boolean;
}
  
export class CreateItemDto {

    @IsString()
    @IsNotEmpty()
    name: string;

    @IsString()
    @IsNotEmpty()
    image:string
    
    @IsString()
    description: string;

    
    @IsString()
    @IsNotEmpty()
    sku: string;
    
    @IsNumber()
    price: number;
    
    
    @ValidateNested()
    @Type(() => PartialCategoryDTO)
    category: PartialCategoryDTO;

    @IsNotEmpty()
    marketplaceStatus: MarketplaceStatus;
}