export class WholeSale {
    _id?: string;
    name: string;
    address: AddressAssociatedDonutAnimator;
    associatedDonutAnimator: AssociatedDonutAnimator;
    tel?: number;
}

export class AssociatedDonutAnimator {
    id: string;
    lastName: string;
    email: string;
    tel: number
}

export class AddressAssociatedDonutAnimator {
    region: string;
    city: string;
    district: string;
    commercialRegion: string;
}