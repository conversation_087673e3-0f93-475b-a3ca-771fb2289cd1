import { Modu<PERSON> } from '@nestjs/common';
import { AuthorizationRemovalService } from './authorization-removal.service';
import { AuthorizationRemovalController } from './authorization-removal.controller';
import { AuthorizationRemovalRepository } from './repository';
import { RemovalReportingController } from '../reporting/removal-reporting/removal-reporting.controller';
import { RemovalReportingService } from '../reporting/removal-reporting/removal-reporting.service';
import { BpRepository } from './repository/bp.repository';


@Module({
  controllers: [AuthorizationRemovalController,RemovalReportingController ],
  providers: [AuthorizationRemovalService, AuthorizationRemovalRepository, RemovalReportingService,BpRepository],
  exports: [AuthorizationRemovalService, AuthorizationRemovalRepository],

})
export class AuthorizationRemovalModule {}
