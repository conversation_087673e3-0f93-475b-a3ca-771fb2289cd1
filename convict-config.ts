import convict from 'convict';

export const config = convict({
  env: {
    doc: 'The application environment.',
    format: ['production', 'development', 'staging'],
    default: 'development',
    env: 'NODE_ENV',
  },
  ip: {
    doc: 'The IP address to bind.',
    format: String,
    default: '127.0.0.1',
    env: 'IP_ADDRESS',
  },
  port: {
    doc: 'The port to bind.',
    format: Number,
    default: 3000,
    env: 'PORT',
    arg: 'port',
  },
  host: {
    doc: 'Application host.',
    format: String,
    default: 'localhost',
    env: 'HOST',
  },
  db: {
    host: {
      doc: 'Database host name/IP',
      format: String,
      default: '127.0.0.1:27017',
      env: 'DB_MONGO_HOST',
    },
    name: {
      doc: 'Database name',
      format: String,
      default: '',
      env: 'DB_MONGO_NAME',
    },
    auth: {
      user: {
        doc: 'Database user if any',
        format: String,
        default: '',
        env: 'DB_MONGO_USERNAME',
      },
      password: {
        doc: 'Database password if any',
        format: String,
        default: '',
        env: 'DB_MONGO_PASSWORD',
      },
    },
  },
  dbV2: {
    host: {
      doc: 'Database host name/IP',
      format: String,
      default: '127.0.0.1:27017',
      env: 'DB_V2_MONGO_HOST',
    },
    name: {
      doc: 'Database name',
      format: String,
      default: '',
      env: 'DB_V2_MONGO_NAME',
    },
    auth: {
      user: {
        doc: 'Database user if any',
        format: String,
        default: '',
        env: 'DB_V2_MONGO_USERNAME',
      },
      password: {
        doc: 'Database password if any',
        format: String,
        default: '',
        env: 'DB_V2_MONGO_PASSWORD',
      },
    },
    mongoUrl: {
      doc: 'Database name',
      format: String,
      default: '',
      env: 'DB_V2_MONGO_URL',
    },
  },
  db_images: {
    host: {
      doc: 'Database host name/IP',
      format: '*',
      default: '127.0.0.1:27017',
      env: 'DB_LAPASTA_IMAGES_HOST',
    },
    name: {
      doc: 'Database name',
      format: String,
      default: '',
      env: 'DB_LAPASTA_IMAGES_NAME',
    },
    auth: {
      user: {
        doc: 'Database user if any',
        format: String,
        default: '',
        env: 'DB_LAPASTA_IMAGES_USERNAME',
      },
      password: {
        doc: 'Database password if any',
        format: String,
        default: '',
        env: 'DB_LAPASTA_IMAGES_PASSWORD',
      },
    },
  },
  baseUrl: {
    doc: 'API base url.',
    format: String,
    default: '',
    env: 'BASE_URL',
    arg: 'base-url',
  },
  basePath: {
    doc: 'API base path.',
    format: String,
    default: '',
  },
  jwt: {
    secret: {
      doc: 'JWT Secret',
      format: String,
      default: 'secret-string',
    },
    expiration: {
      doc: 'JWT Token expiration',
      format: String,
      default: '5m',
      env: 'JWT_EXPIRATION',
    },
  },
  email: {
    support: {
      doc: 'Support email address',
      format: String,
      default: '<EMAIL>',
    },
    noreply: {
      doc: 'noreplay email address',
      format: String,
      default: '',
    },
    cc: {
      doc: 'Carbon Copy email address',
      format: String,
      default: '',
    },
  },
  company: {
    name: {
      doc: 'Name of the company',
      format: String,
      default: 'LAPASTA',
    },
    address: {
      doc: 'Company address',
      format: String,
      default: 'Douala ZI Bonaberi',
    },
  },
  exportTTL: {
    doc: "Export time to live.",
    format: Number, // in seconds
    default: 1900,
    env: "EXPORT_TTL",
    arg: "export-ttl",
  },
  exportSalt: {
    doc: "Export salt.",
    format: String,
    default: "SzxWhsPnj!k@#7yIe7Wa^PA4t0W3MLue6j&bPe@Vtg#8gE0UyI",
    env: "EXPORT_LINK_SALT",
    arg: "export-salt",
  },
  gotenbergUrl: {
    doc: 'GOTENBERG PDF GENERATOR URL',
    format: String,
    default: 'http://localhost:3000/',
    env: 'GOTENBERG_URL',
  },
  product: {
    name: {
      doc: 'Product name',
      format: String,
      default: 'LAPASTA',
    },
    frontendUrl: {
      doc: 'Product frontend url',
      format: String,
      default: 'https://www.dev-lapasata.londo-tech.com',
    },
  },
  queue: {
    port: {
      doc: 'The queue port to bind.',
      format: Number,
      default: 3000,
      env: 'QUEUE_PORT',
      arg: 'queue_port',
    },
    host: {
      doc: 'The queue host name or ip to bind.',
      format: String,
      default: 'localhost',
      env: 'QUEUE_HOST',
    },
  },
  payment: {
    port: {
      doc: 'The payment port to bind.',
      format: Number,
      default: 3000,
      env: 'PAYMENT_PORT',
      arg: 'payment_port',
    },
    host: {
      doc: 'The payment host name or ip to bind.',
      format: String,
      default: 'localhost',
      env: 'PAYMENT_HOST',
    },
  },
  cyberSource: {
    targetOrigins: {
      doc: "Target Origins",
      format: Array,
      default: [],
      env: "TARGET_ORIGINS",
    },
  },
  jde: {
    port: {
      doc: 'The jde port to bind.',
      format: Number,
      default: 3000,
      env: 'JDE_PORT',
      arg: 'jde_port',
    },
    host: {
      doc: 'The jde host name or ip to bind.',
      format: String,
      default: 'localhost',
      env: 'jde_HOST',
    },
  },
  cron: {
    port: {
      doc: 'The cron port to bind.',
      format: Number,
      default: 3005,
      env: 'CRON_PORT',
      arg: 'cron_port',
    },
    host: {
      doc: 'The cron host name or ip to bind.',
      format: String,
      default: 'localhost',
      env: 'cron_HOST',
    },
  },
  tonnage: {
    active: {
      capacity: {
        doc: 'Active employee tonnage capacity',
        format: Number,
        default: 40,
      },
      capacityPerYear: {
        doc: 'Tonnage capacity per year for active employee',
        format: Number,
        default: 20,
      },
    },
    retired: {
      capacity: {
        doc: 'Active employee tonnage capacity',
        format: Number,
        default: 20,
      },
      capacityPerYear: {
        doc: 'Tonnage capacity per year for active employee',
        format: Number,
        default: 5,
      },
    },
  },
  rabbitmq: {
    hostname: {
      doc: 'rabbitmq hostname',
      format: String,
      default: '',
      env: 'RABBITMQ_HOSTNAME'
    },
    port: {
      doc: 'rabbitmq port',
      format: Number,
      default: null,
      env: 'RABBITMQ_PORT'
    },
    protocol: {
      doc: 'rabbitmq protocol',
      format: String,
      default: 'amqp',
      env: 'RABBITMQ_PROTOCOL'
    },
    username: {
      doc: 'rabbitmq auth username',
      format: String,
      default: '',
      env: 'RABBITMQ_USERNAME'
    },
    password: {
      doc: 'rabbitmq auth password',
      format: String,
      default: '',
      env: 'RABBITMQ_PASSWORD'
    },
    activeRabbitmq: {
      doc: 'rabbitmq state',
      format: Boolean,
      default: true,
      env: 'ACTIVE_RABBITMQ'
    }
  },
  time_otp_expire: {
    value: {
      doc: 'time for the otp code to expire',
      format: Number,
      default: 7,
      env: 'VALUE_TIME_OTP'
    },
    format: {
      doc: 'format for the otp code to expire',
      default: 'minutes',
      env: 'FORMAT_TIME_OTP'

    },
  },
  londo: {
    login: {
      doc: 'Afriland basic auth login.',
      format: String,
      default: '',
    },
    password: {
      doc: 'Afriland basic auth password.',
      format: String,
      default: '',
    },
  },
  afriland: {
    login: {
      doc: 'Afriland basic auth login.',
      format: String,
      default: '',
    },
    password: {
      doc: 'Afriland basic auth password.',
      format: String,
      default: '',
    },
  },
  eexTransaction: {
    login: {
      doc: 'Express Exchange basic auth login.',
      format: String,
      default: '',
    },
    password: {
      doc: 'Express Exchange basic auth password.',
      format: String,
      default: '',
    },
  },
  oauthSalt: {
    doc: 'Salt to encrypt Oauth tokens.',
    format: String,
    default: '',
    env: 'OAUTH_SALT',
    arg: 'oauth-salt'
  },
  oauthTTL: {
    doc: 'Token time to live.',
    format: Number,
    default: '',
    env: 'OAUTH_TTL',
    arg: 'oauth-ttl'
  },
  fluentdHost: {
    doc: "Fluentd log server host.",
    format: String,
    default: "localhost",
    env: "FLUENTD_HOST",
  },
  minimalMobileBuildVersion: {
    android: {
      doc: 'Android minimal build version supported.',
      format: String,
      default: '',
      env: "ANDROID_MINIMAL_BUILD_VERSION",

    },
    ios: {
      doc: 'IoS minimal build version supported.',
      format: String,
      default: '',
      env: "IOS_MINIMAL_BUILD_VERSION"
    },
  },
  activeSnitch: {
    doc: 'Variable to active snitch.',
    format: Boolean,
    default: true,
    env: "ACTIVE_SNITCH"
  },
  switchFilterPrice: {
    doc: 'Variable to active snitch.',
    format: Boolean,
    default: false,
    env: "SWITCH_FILTER_PRICE"
  },
  activeMock: {
    doc: 'Variable to active mock.',
    format: Boolean,
    default: false,
    env: "ACTIVE_MOCK"
  },
  activeForEnv: {
    doc: 'Variable to active process for ENV.',
    format: Array,
    default: ['development'],
    env: "ACTIVE_FOR_ENV"
  },
  appUrl: {
    backoffice: {
      doc: 'Variable to active set url for mobile app.',
      format: String,
      default: 'https://backoffice-lapasta.londo-tech.com',
      env: "APP_URL_BACKOFFICE"
    },
    website: {
      doc: 'Variable to active set url for mobile app.',
      format: String,
      default: 'https://backoffice-lapasta.londo-tech.com',
      env: "APP_URL_WEBSITE"
    },
    mobile: {
      doc: 'Variable to active set url for mobile app.',
      format: String,
      default: 'https://shorturl.at/q3mkw',
      env: "APP_URL_Mobile"
    },
  },
  telStore: {
    doc: 'Variable to mock num for store set url for mobile app.',
    format: String,
    default: '*********',
    env: "TEL_STORE"
  },
  telIndirectClient: {
    doc: 'Variable to mock num for indirect client set url for mobile app.',
    format: String,
    default: '*********',
    env: "TEL_STORE"
  },
  companyName: {
    doc: 'Variable to set owner of app.',
    format: String,
    default: 'CADYST',
    env: "COMPANY_NAME"
  },
  detailOrderBaseUrl: {
    doc: 'Variable to redirect for notification in mobile platform.',
    format: String,
    default: 'order/detail/',
    env: "DETAIL_ORDER_BASEURL"
  },
  filluelMinThresholdPoints: {
    doc: 'Variable to increment to the points validate of referral in loyalty program.',
    format: Number,
    default: 5,
    env: "FILLEUL_MIN_THRESHOLD_POINTS"
  },
  sponsorshipPoints: {
    doc: 'Variable to increment to the points validate of referral in loyalty program.',
    format: Number,
    default: 300,
    env: "SPONSORSHIP_POINTS"
  },
  ratioOfPointsByVolume: {
    doc: 'Variable to calculate points validate.',
    format: Number,
    default: 0.2,
    env: "RATIO_OF_POINTS_BY_VOLUME"
  },
  fidelity: {
    statusThresholds: {
      doc: 'Seuils pour les statuts de fidélité',
      format: Array,
      default: [
        { min: 0, max: 2000, status: 'AMIGO' },
        { min: 2001, max: 5000, status: 'COLOMBE' },
        { min: 5001, max: Infinity, status: 'PELICAN' }
      ],
      env: 'FIDELITY_STATUS_THRESHOLDS'
    }
  },
  isMockOtp: {
    doc: 'Variable to active mock otp.',
    format: Boolean,
    default: false,
    env: "ACTIVE_MOCK_OTP"
  },
  isManualOrder: {
    doc: 'Variable to active manual order.',
    format: Boolean,
    default: false,
    env: "ACTIVE_MANUAL_ORDER"
  },
  isDisableOrderProcess: {
    doc: 'Variable to disable order process.',
    format: Boolean,
    default: false,
    env: "DISABLE_ORDER_PROCESS"
  },
  db_cadyst_logistic: {
    host: {
      doc: 'Database host name/IP',
      format: String,
      default: '127.0.0.1:27017',
      env: 'DB_CADYST_LOGISTIC_MONGO_HOST',
    },
    name: {
      doc: 'Database name',
      format: String,
      default: 'cadyst-logistic',
      env: 'DB_CADYST_LOGISTIC_MONGO_NAME',
    },
    auth: {
      user: {
        doc: 'Database user if any',
        format: String,
        default: '',
        env: 'DB_CADYST_LOGISTIC_MONGO_USERNAME',
      },
      password: {
        doc: 'Database password if any',
        format: String,
        default: '',
        env: 'DB_CADYST_LOGISTIC_MONGO_PASSWORD',
      },
    },
    mongoUrl: {
      doc: 'Database name',
      format: String,
      default: '',
      env: 'DB_CADYST_LOGISTIC_MONGO_URL',
    },
    registerCollections: {
      doc: 'name of collections to register',
      format: String,
      default: 'orders',
      env: 'DB_CADYST_LOGISTIC_REGISTER_COLLECTIONS',
    }
  },
  base_url_logistic_server: {
    doc: 'Base url of logistic server',
    format: String,
    default: 'http://localhost:3008/api/v1',
    env: 'BASE_URL_LOGISTIC_SERVER'
  },
  isSendingDataViaEndpoint: {
    doc: 'Variable to send data to via endpoint.',
    format: Boolean,
    default: false,
    env: "SENDING_DATA_VIA_ENDPOINT"
  },
});

const env = config.get('env');
config.loadFile('./src/env/' + env + '.json');

config.validate({ allowed: 'strict' });