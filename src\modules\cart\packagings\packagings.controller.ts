
import { JwtGuard } from '@la-pasta-module/auth';
import { Controller, Get, Post, Body, Patch, Param, HttpCode, Query, UseGuards } from '@nestjs/common';
import { PackagingsService } from './packagings.service';
import { CreatePackagingDto } from './dto/create-packaging.dto';
import { UpdatePackagingDto } from './dto/update-packaging.dto';
import { getUser, setResponseController } from '@la-pasta/common';
import { ApiTags } from '@nestjs/swagger';
import { Permission } from '@la-pasta/infrastructures/security';
import { PackagingAction } from './actions';

@ApiTags('Packaging')
@Controller('packagings')
export class PackagingsController {
  constructor(private readonly packagingsService: PackagingsService) { }

  @HttpCode(201)
  @UseGuards(JwtGuard)
  @Post()
  async create(@Body() createPackagingDto: CreatePackagingDto) {
    Permission.packagingAuthorization(getUser(), PackagingAction.CREATE);
    const data = await this.packagingsService.createPackage(createPackagingDto);
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.packagingAuthorization(getUser(), PackagingAction.VIEW);
    const data = await this.packagingsService.findAll({ filter: query });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.packagingAuthorization(getUser(), PackagingAction.VIEW);
    const data = await this.packagingsService.findPackage({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updatePackagingDto: UpdatePackagingDto) {
    Permission.packagingAuthorization(getUser(), PackagingAction.UPDATE);
    const data = await this.packagingsService.update({ _id: id }, updatePackagingDto);
    return setResponseController(data);
  }
}
