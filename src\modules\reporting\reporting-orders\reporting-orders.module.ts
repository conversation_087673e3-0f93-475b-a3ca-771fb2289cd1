import { OrderRepository } from '@la-pasta-module/order';
import { UserRepository } from '@la-pasta-module/users';
import { Module } from '@nestjs/common';
import { OrderRetailRepository } from '@la-pasta-module/order-retaill/repository';
import { ReportingHelpers } from '@la-pasta/common';
import { ReportingOrdersService } from './reporting-orders.service';
import { ReportingOrdersController } from './reporting-orders.controller';
import { OrderSupplierRepository } from '@la-pasta-module/order-supplier/repository';
import { ScannerDataRepository } from '@la-pasta-module/scanner-data/repository';

@Module({
  controllers: [ReportingOrdersController],
  providers: [ReportingOrdersService, OrderRepository, UserRepository, OrderRetailRepository, ReportingHelpers, OrderSupplierRepository, ScannerDataRepository]
})
export class ReportingOrdersModule { }
