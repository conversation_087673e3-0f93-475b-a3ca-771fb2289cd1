import readline from 'readline';
import { insertStoreDataFile } from 'scripts/stores/store-file.script';


(async () => {
  const prompt = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('* Vous êtes sur le point d\'insérer les données des usines de Click CADYST, ce processus effacera la collection stores.');

  prompt.question('* Voulez-vous continuer? (y/n): ', async (answer) => {


    if (answer === 'y') {

      await insertStoreDataFile();
      console.log('Données des usines insérées avec succès.');
    }

    prompt.close();

  });

  prompt.on('close', () => {
    process.exit()
  })
})();