import { StoreAction } from '@la-pasta-module/cart';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class StoreAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      StoreAction.CREATE,
      StoreAction.DELETE,
      StoreAction.UPDATE,
      StoreAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    return user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const StoreAuthorizationInstance = new StoreAuthorization();