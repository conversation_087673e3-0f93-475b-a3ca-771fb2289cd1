import { Injectable } from '@nestjs/common';
import { BaseService, converDateFilter } from '@la-pasta/common';
import { RetrievementRepository } from './repository/retrievements.repository';
import moment from 'moment';
import { UpdateRetrievementDto } from './dto/update-retrievement.dto';

@Injectable()
export class RetrievementsService extends BaseService {


  constructor(
    private readonly retrievementsRepository: RetrievementRepository,

  ) {
    super(retrievementsRepository)
  }

  async getRetrievements(query: QueryOptions) {
    if ('startDate' in query.filter || 'endDate' in query.filter) {
      this.setDateFilter(query.filter);
    }
    return await this.findAll(query);
  }


  async updateRetrievement(id: string, body: UpdateRetrievementDto) {
    const quantityReceive = {
      data: body.quantityReceive,
    }
    return await this.update(
      { _id: id },
      { quantityReceive },
    );

  }

  private setDateFilter(query: QueryFilter) {
    query['actualShipDate'] = {
      $gte: this.formatStartDate(query.startDate),
      $lte: this.formatEndDate(query.endDate),
    };
    delete query.startDate;
    delete query.endDate;
  }

  private formatStartDate(date: string) {
    return moment(date, 'YYYY-MM-DD').startOf('day').valueOf();
  }

  private formatEndDate(date: string) {
    return moment(date, 'YYYY-MM-DD').endOf('day').valueOf();
  }

  async getFilterElementsByKeys(query: QueryFilter, keyForFilters: string[]) {
    query = converDateFilter({ filter: query });
    const data = {};
    for (const idKey of keyForFilters) {
      const result = await this.repository.baseAggregation({ filter: query }, idKey);
      data[`data${idKey}`] = result ?? [];
    }

    return data;
  }
}
