import { Injectable } from '@nestjs/common';
import { BaseService, ReportingHelpers, convertFilter } from '@la-pasta/common';
import { AuthorizationRemovalRepository } from '@la-pasta-module/authorization-removal/repository';
import { RenderTypeLibrary, StatusAES, StatusAESLibrary } from '@la-pasta-module/authorization-removal/entities';

@Injectable()
export class RemovalReportingService extends BaseService {
    constructor(public aeRepository: AuthorizationRemovalRepository) {
        super(aeRepository);
    }

    async getAeRepartition(query: QueryFilter) {
        try {
            query = convertFilter(query, ['status']);
            query['dates.created'] = query?.created_at;
            delete query?.created_at;

            const totalAesByStatusAndRenderTypes = {}
            const totalAesPerRenderType = await this.aeRepository.baseAggregation({ filter: query }, 'FreightHandlingCode');
            const totalAesPerStatus = await this.aeRepository.baseAggregation({ filter: query }, 'LoadStatus');

            for (const repartition of totalAesPerStatus) {
                Object.assign(totalAesByStatusAndRenderTypes, { [StatusAESLibrary[+repartition.label]]: repartition.total });
            }

            for (const renderType of totalAesPerRenderType) {
                Object.assign(totalAesByStatusAndRenderTypes, { [RenderTypeLibrary[renderType.label || 'Non spécifier'] ?? 'Non spécifier']: renderType.total })
            }

            return totalAesByStatusAndRenderTypes;
        } catch (error) {
            return error;
        }
    }

    async getProductInAe(query: QueryFilter) {

        try {
            query = convertFilter(query, ['status']);
            query['dates.created'] = query?.created_at;
            query['LoadStatus'] = { $ne: StatusAES.REJECTED };
            delete query?.created_at;
            const data = await this.aeRepository.baseAggregation({ filter: query }, 'ItemNumber');
            return data;
        } catch (error) {
            return error;
        }
    }

    async getEvolution(query: QueryFilter) {
        try {
            let dataP = [];
            let dataR = [];

            query = convertFilter(query, ['status']);
            query['dates.created'] = query?.created_at;
            delete query?.created_at;
            const dataChart = await this.aeRepository.getEvolutionAes(query);
            const data2 = dataChart.map((elt) => { return elt.render });
            const data1 = dataChart.map((elt) => { return elt.pickup });
            dataP = dataP.concat(...data1);
            dataR = dataR.concat(...data2);

            const dataYearPickup = ReportingHelpers.generateChartYearDataForRetails(dataP);
            const dataYearRender = ReportingHelpers.generateChartYearDataForRetails(dataR);

            return { dataYearPickup, dataYearRender };
        } catch (error) {
            throw error;
        }

    }
}
