import { BaseRepository } from "@la-pasta/common";
import { OrderStatus } from "../entities";


export class OrderRepository extends BaseRepository {

  monthOfYear = {
    $dateToString: {
      format: '%m',
      date: { $toDate: { $toLong: '$created_at' } }
    }
  };

  constructor() {
    super();
  }

  async findModePayment(query) {
    const matchExpression = {
      $match: {
        'payment.mode.id': { $exists: true, $ne: null },
        ...query,
      }
    }
    const groupExpression = {
      $group: {
        '_id': '$payment.mode.id',
        'numberOforders': {
          '$sum': 1
        },
        'total': {
          '$sum': '$cart.amount.TTC'
        }
      }
    };


    return await this.findAllAggregate([matchExpression, groupExpression]);
  }

  async findModeCimencam(query) {
    const matchExpression = {
      $match: {
        'payment.mode.id': 4,
        'payment.clientOption.id': { $exists: true },
        ...query
      }
    };
    const groupExpression = {
      $group: {
        '_id': '$payment.clientOption.id',
        'numberOfOrders': {
          '$sum': 1
        },
        'total': {
          '$sum': '$cart.amount.TTC'
        }
      }

    }
    return await this.findAllAggregate([matchExpression, groupExpression]);
  }

  async getExportRecapOrder(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [];

    aggregateExpressions.push({
      $unwind: { path: "$cart.items" }
    });

    aggregateExpressions.push({
      $match: { ...query }
    });

    aggregateExpressions.push({
      $group: {
        _id: {
          month: { $month: { $toDate: "$created_at" } },
          year: { $year: { $toDate: "$created_at" } }
        },
        totalSalesAmount: { $sum: "$cart.amount.TTC" },
        cementVolumeSold: {
          $sum: {
            $cond: {
              if: { $gt: [{ $ifNull: ["$cart.items.quantity", 0] }, 0] },
              then: {
                $divide: [
                  "$cart.items.quantity",
                  { $ifNull: ["$cart.items.packaging.unit.ratioToTone", 1] }
                ]
              },
              else: 0
            }
          }
        }
      }
    });

    aggregateExpressions.push({
      $project: {
        _id: 0,
        totalSalesAmount: 1,
        cementVolumeSold: 1,
        monthYear: {
          $concat: [
            {
              $arrayElemAt: [
                ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
                { $subtract: ["$_id.month", 1] }
              ]
            },
            " ",
            { $toString: "$_id.year" }
          ]
        }
      }
    });

    aggregateExpressions.push({
      $sort: { "monthYear": 1 }
    });

    return await this.findAllAggregate(aggregateExpressions);
  }


  async getEvolutionOrders(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [];
    const matchExpression = {
      $match: {
        ...query,
      }
    };
    aggregateExpressions.push(matchExpression);

    const addFelidsExpression = {
      $addFields: { category: "$company.category" }
    };
    aggregateExpressions.push(addFelidsExpression);

    const projectExpression = {
      $project: {
        monthOfYear: this.monthOfYear,
        category: 1,
        _id: 0
      }
    };
    aggregateExpressions.push(projectExpression);

    return await this.findAllAggregate(aggregateExpressions);
  }


  async getVolumeOrderByUsers(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [];

    const unwindExpressions = {
      $unwind: { path: "$cart.items" }
    }
    aggregateExpressions.push(unwindExpressions);
    const matchExpression = {
      $match: {
        ...query,
      }
    };
    aggregateExpressions.push(matchExpression);

    const groupExpressions = {
      $group: {
        _id: '$company.category',
        total: {
          $sum: {
            $divide: [
              "$cart.items.quantity",
              "$cart.items.packaging.unit.ratioToTone",
            ],
          },
        }

      }
    };
    aggregateExpressions.push(groupExpressions);


    return await this.findAllAggregate(aggregateExpressions);
  }

  async getEvolutionProduct(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [];
    const unwindExpressions = {
      $unwind: { path: "$cart.items" }
    }
    aggregateExpressions.push(unwindExpressions);

    const matchExpression = {
      $match: { ...query }
    };
    aggregateExpressions.push(matchExpression);

    const addFieldsExpression = {
      $addFields: { label: "$cart.items.product.label", quantity: "$cart.items.quantity" }
    };
    aggregateExpressions.push(addFieldsExpression);

    const projectExpression = {
      $project: {
        monthOfYear: this.monthOfYear,
        label: 1,
        quantity: 1,
        _id: 0
      }
    };
    aggregateExpressions.push(projectExpression);
    return await this.findAllAggregate(aggregateExpressions);
  }

  async getRepartitionProduct(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [];
    const unwindExpressions = {
      $unwind: { path: "$cart.items" }
    }
    aggregateExpressions.push(unwindExpressions);

    const matchExpression = {
      $match: { ...query }
    };
    aggregateExpressions.push(matchExpression);

    const groupExpression = {
      $group: {
        _id: '$cart.items.product.label',
        total: {
          $sum: {
            $divide: [
              "$cart.items.quantity",
              "$cart.items.packaging.unit.ratioToTone",
            ],
          },
        },
        // totalTone: {
        //   $sum: '$cart.items.quantity'
        // }
      }
    };
    aggregateExpressions.push(groupExpression);

    const projectExpression = {
      $project: {
        "label": "$_id",
        "total": '$total',
        // "totalTone": '$totalTone',
        _id: 0
      }
    };

    aggregateExpressions.push(projectExpression);


    return await this.findAllAggregate(aggregateExpressions);
  }

  async getAvgTimeValidation(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [
      {
        $match: {
          status: OrderStatus?.VALIDATED,
          ...query
        }
      },
      {
        $project: {
          delayInMilliseconds: {
            $subtract: ["$dates.validated", "$dates.paid"]
          },
          delayInMinutes: {
            $divide: [
              { $subtract: ["$dates.validated", "$dates.paid"] },
              60000 // Convert milliseconds to minutes
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          totalDelayInMilliseconds: { $sum: "$delayInMilliseconds" },
          totalDelayInMinutes: { $sum: "$delayInMinutes" },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          averageDelayInMilliseconds: {
            $divide: ["$totalDelayInMilliseconds", "$count"]
          },
          averageDelayInMinutes: {
            $divide: ["$totalDelayInMinutes", "$count"]
          }
        }
      }
    ];

    return await this.findAllAggregate(aggregateExpressions);
  }

  async getEvolutionSalePayment(query: QueryFilter): Promise<unknown[]> {
    const aggregateExpressions = [
      { $match: { ...query } },
      {
        $project: {
          monthOfYear: {
            $dateToString: {
              format: '%m',
              date: { $toDate: { $toLong: '$created_at' } }
            }
          },
          amount: "$cart.amount.TTC"
        }
      },
      {
        $group: {
          _id: "$monthOfYear",
          monthOfYear: { $sum: '$monthOfYear' },
          totalValue: { $sum: '$amount' },
        }
      },
      {
        $project: {
          monthOfYear: "$_id",
          _id: 0,
          totalValue: 1
        }
      },
      { $sort: { totalValue: -1 } }
    ];

    return await this.findAllAggregate(aggregateExpressions);
  }

  async getEvolutionRegion(query: QueryFilter) {
    const aggregateExpressions = [];

    const matchExpression = {
      $match: { ...query }
    };
    aggregateExpressions.push(matchExpression);

    const projectExpression = {
      $project: {
        monthOfYear: this.monthOfYear,
        'cart': 1,
      }
    };

    aggregateExpressions.push(projectExpression);

    return await this.findAllAggregate(aggregateExpressions);

  }

  async getRepartitionRegion(query: QueryFilter) {
    const aggregateExpressions = [];


    const unwindExpressions = {
      $unwind: { path: "$cart.items" }
    }
    aggregateExpressions.push(unwindExpressions);

    const matchExpression = {
      $match: {
        'cart.store.address.commercialRegion': { $exists: true, $ne: null },

        ...query
      }
    };
    aggregateExpressions.push(matchExpression);

    const groupExpression = {
      $group: {
        _id: '$cart.store.address.commercialRegion',
        total: {
          $sum: {
            $divide: [
              "$cart.items.quantity",
              "$cart.items.packaging.unit.ratioToTone",
            ],
          },
        },

      }
    };
    aggregateExpressions.push(groupExpression);

    const projectExpression = {
      $project: {
        "label": "$_id",
        "total": '$total',
        _id: 0
      }
    };

    aggregateExpressions.push(projectExpression);

    return await this.findAllAggregate(aggregateExpressions);
  }

  async getEvolutionPayement(query: QueryFilter) {
    const aggregateExpressions = [];

    const unwindExpressions = {
      $unwind: { path: "$cart.items" }
    }
    aggregateExpressions.push(unwindExpressions);

    const matchExpression = {
      $match: { ...query }
    };
    aggregateExpressions.push(matchExpression);

    const projectExpression = {
      $project: {
        monthOfYear: this.monthOfYear,
        payment: 1
      }
    };
    aggregateExpressions.push(projectExpression);
    return await this.findAllAggregate(aggregateExpressions);
  }

  async getTotalSales(query: QueryFilter) {
    const aggregateExpressions = [];
    const matchExpression = {
      $match: { ...query }
    };
    aggregateExpressions.push(matchExpression);

    const groupExpression = {
      $group: {
        _id: '$status',
        totalSale: { $sum: '$cart.amount.TTC' },
        NumberOfOrders: { $sum: 1 }
      }
    };
    aggregateExpressions.push(groupExpression);
    return await this.findAllAggregate(aggregateExpressions);
  }

  async getRankingsByValue(query: QueryFilter) {
    const aggregateExpressions = []
    const matchExpression = {
      $match: {
        ...query,
        company: { $exists: 1 }
      }
    };
    aggregateExpressions.push(matchExpression);

    const unwindExpressions = {
      $unwind: { path: "$cart.items" }
    };
    aggregateExpressions.push(unwindExpressions);


    const groupExpressions = {
      $group: {
        _id: "$company._id",
        totalValue: { $sum: { $divide: ["$cart.items.quantity", "$cart.items.packaging.unit.ratioToTone"] } },
        label: { $first: "$company.name" },
      }
    };


    aggregateExpressions.push(groupExpressions);

    const sortExpressions = {
      $sort: { totalValue: -1 }
    };
    aggregateExpressions.push(sortExpressions);

    return await this.findAllAggregate(aggregateExpressions);
  }

  async getCementSalesVolumes(query: QueryFilter) {
    const aggregateExpressions = [];
    const unwindExpressions = {
      $unwind: { path: "$cart.items" }
    };
    aggregateExpressions.push(unwindExpressions);

    const matchExpression = {
      $match: { ...query }
    };
    aggregateExpressions.push(matchExpression);

    const groupExpressions = {
      $group: {
        _id: "$cart.items.product._id",
        totalValue: { $sum: { $divide: ["$cart.items.quantity", "$cart.items.packaging.unit.ratioToTone"] } },
        totalQuantity: { $sum: "$cart.items.quantity" },
        label: { $first: "$cart.items.product.label" },
        packaging: { $first: "$cart.items.packaging.label" }
      }
    };
    aggregateExpressions.push(groupExpressions);

    const sortExpressions = {
      $sort: { totalValue: -1 }
    }

    aggregateExpressions.push(sortExpressions);

    return await this.findAllAggregate(aggregateExpressions);
  }

  async getRecapInfosForOrderList(query: QueryFilter) {
    const aggregateExpressions = [];
    const matchExpression = {
      $match: { ...query }
    }
    aggregateExpressions.push(matchExpression);

    const unwindExpressions = {
      $unwind: { path: '$cart.items' }
    }
    aggregateExpressions.push(unwindExpressions);

    const groupExpression1 = {
      $group: {
        _id: '$_id',
        totalTonnes: { $sum: { $divide: ["$cart.items.quantity", "$cart.items.packaging.unit.ratioToTone"] } },
        totalAmount: { $addToSet: "$cart.amount.TTC" },
      }
    }
    aggregateExpressions.push(groupExpression1);

    const projectExpression1 = {
      $project: {
        totalTonnes: '$totalTonnes',
        totalAmount: { $sum: '$totalAmount' }
      }
    }
    aggregateExpressions.push(projectExpression1);

    const groupExpression2 = {
      $group: {
        _id: null,
        totalTonnes: { '$sum': '$totalTonnes' },
        totalAmount: { '$sum': '$totalAmount' },
        numberOfOrders: { '$sum': 1 }
      }
    }
    aggregateExpressions.push(groupExpression2);

    const projectExpression2 = {
      $project: {
        _id: 0,
        totalAmount: { $sum: '$totalAmount' },
        totalTonnes: 1,
        numberOfOrders: 1
      }
    }
    aggregateExpressions.push(projectExpression2);

    return await this.findAllAggregate(aggregateExpressions);

  }

}

