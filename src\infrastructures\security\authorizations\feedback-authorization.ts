
import { FeedbackAction } from '@la-pasta-module/feedbacks/actions';
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';

class FeedbackAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      FeedbackAction.DELETE,
      FeedbackAction.CREATE,
      FeedbackAction.UPDATE,
      FeedbackAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == FeedbackAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const FeedbackAuthorizationInstance = new FeedbackAuthorization();