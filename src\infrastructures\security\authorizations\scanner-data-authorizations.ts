
import { ScannerDataAction } from '@la-pasta-module/scanner-data/actions';
import { AuthorizationInterface } from './authorization.interface';
import { User, UserRole } from '@la-pasta-module/users';


class ScannerDataAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      ScannerDataAction.DELETE,
      ScannerDataAction.CREATE,
      ScannerDataAction.UPDATE,
      ScannerDataAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    if (permission == ScannerDataAction.VIEW) return user.enable === true;

    return user.roles.includes(UserRole.BACKOFFICE) || user.roles.includes(UserRole.CLIENT);
  }
}

export const ScannerDataAuthorizationInstance = new ScannerDataAuthorization();