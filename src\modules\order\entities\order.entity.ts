import { Cart } from "@la-pasta-module/price/compute_price";
import { BaseUser, User } from "@la-pasta-module/users";
import { Company } from "@la-pasta-module/companies";
import { Removal } from "@la-pasta-module/planification/entities";
export class Order {
  _id?: string;
  appReference: string;
  erpReference: string;
  payment: Payment;
  preparationReference:number
  cart: Cart;
  customerDeliveryDestination?: string;
  status: OrderStatus;
  statusDelivery?: OrderStatusDelivery;
  customerReference?: string;
  user: Partial<BaseUser>;
  company?: Partial<Company>;
  dates?: {
    created: number;
    paid?: number;
    validated?: number;
  };
  removals: Removal[];
  validation: OrderValidation;
  created_at: number;
  rejectReason: string;
  nberModif: number;
  messageCancellation?: string;
  cancellationStatus?: CancellationStatus;
  reference?: string;
  comments?: CommercialComment[];
  carrier?: Carrier;
}

export enum CancellationStatus {
  ISSUE = 11,
  ACCEPTED = 12,
  REFUSED = 13
}

export enum OrderStatus {
  CREATED = 100,
  PAID = 200,
  VALIDATED = 300,
  FAILD = 400,
  CREDIT_REJECTED = 99,
  CREDIT_IN_VALIDATION = 101,
  REJECTED = 99 ,
  CREDIT_IN_AWAIT_VALIDATION = 102,
  CANCELLED = 500,
  RETREIVED = 600,
}

export enum OrderStatusDelivery {
  WAITING = 100,
  PROCESS = 300,
  COMPLETED = 400,
}

export enum OrderValidation {
  CORDO_RH = 1,
  DRH = 2,
  COMMERCIAL = 3,
}

export class Payment {
  mode: {
    id: number;
    label: string;
  };
  clientOption?: {
    id: number;
    label: string;
    icon: string;
    destination: string;
  };
  data?: {
    reference: string;
    amount: number;
    bank: {
      id: number;
      label: string;
    }
  };
  monthlyPayment?: number;
  startDate?: string;
  tel?: string;
  processingNumber?: string | number;
  transactionId?: string;
  isQ1?: boolean;
  paymentId?: string;
}

export enum PaymentMode {
  ORANGE_MONEY = 0,
  MOBILE_MONEY = 1,
  M2U = 2,
  AFRILAND = 3,
  MY_ACCOUNT = 4,
  EXPRESS_EXCHANGE = 5,
  CREDIT = 6,
  VISA = 7,
  LOW_BALANCE = 8,
  EU = 9

}

export const PaymentModeLabel = {
  [PaymentMode.AFRILAND]: "Afriland",
  [PaymentMode.ORANGE_MONEY]: "ORANGE MONEY",
  [PaymentMode.MOBILE_MONEY]: "MOBILE MONEY",
  [PaymentMode.EXPRESS_EXCHANGE]: "EXPRESS EXCHANGE",
  [PaymentMode.VISA]: "VISA",
  [PaymentMode.M2U]: "M2U",
  [PaymentMode.EU]: "EXPRESS UNION"
}

export interface CommercialComment {
  text: string;
  userCommercial: Partial<User>;
  created_at: number;
}

export interface Carrier{
  _id: string;
  name: string;
  phone: string;
  idCard?: string; 
  vehiclePlate?: string;
  vehicleCategory?: string;
  driverLicense?: string;
}