import { Balance } from "@la-pasta-module/balance/entities/balance.entity";
import { Company } from "@la-pasta-module/companies";
import { PaymentMode } from "@la-pasta-module/order";
import { User } from "@la-pasta-module/users";
import { ObjectId } from "mongodb";

export class ReloadBalance {
    _id: string | ObjectId;
    user: Partial<User>;
    nbrTries: number;
    company: Partial<Company>;
    status: TransactionStatus;
    currentBalance?: Balance;
    payment: PaymentReloadBalance;
    uniqueReference?: string;
    paymentInfo?: PaymentInfo<'status'>;
    created_at?: number;
    paymentResponseJDE?: any;
    statusPaymentResponseJDE?: any;
}

export interface PaymentReloadBalancePayload {
    _id: string;
    payment: PaymentReloadBalance;
}

export enum TransactionStatus {
    PENDING = 200,
    SUCCESSFUL = 300,
    INITIATED = 100,
    FAILED = 99,
    EXPIRED = 400,
}

export enum OperatorTransactionStatus {
    PENDING = 'PENDING',
    INITIATED = 'INITIATED',
    FAILED = 'FAILED',
    EXPIRED = 'EXPIRED',
    SUCCESSFUL = 'SUCCESSFUL',
}

export class PaymentReloadBalance {
    mode?: {
        id: PaymentMode;
        label: string;
        icon: string;
        txt: string;
        bank: string;
        class?: string;
        authoriseLabel?: string
    };
    reference: string;
    amount: number;
    tel?: string;
    transactionId?: string;
    jdeTransactionId?: string;
}
