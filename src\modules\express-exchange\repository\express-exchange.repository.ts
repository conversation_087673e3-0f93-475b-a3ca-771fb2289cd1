import { BaseRepository } from "@la-pasta/common";
import { InsertOneResult, ObjectId, UpdateResult, Document } from "mongodb";

export class ExpressExchangeRepository extends BaseRepository {
  constructor() {
    super()
  }

  async insertDataEexTransaction(document: Document): Promise<InsertOneResult<Document>> {
    return (await this.getCollection()).insertOne(document);
  }

  async getEexTransaction(query?: QueryOptions): Promise<Document[]> {
    return await (await this.getCollection())
      .find(query?.filter ?? {})
      .project(query?.projection ?? {})
      .sort(query?.sort ?? '_id', query?.way ?? -1)
      .skip(query?.offset ?? 0)
      .limit(query?.limit ?? 0)
      .toArray();
  }

  async getEexTransactionBy(query: QueryFilter): Promise<Document> {

    return (await this.getCollection()).findOne({ ...query });
  }

  async getEexTransactionById(id) {
    id = new ObjectId(id);
    return (await this.getCollection()).findOne(id);
  }

  async updateEexTransaction(query: QueryFilter, data: any): Promise<UpdateResult> {

    return (await this.getCollection()).updateOne({ ...query },
      {
        $set: {
          ...data,
        },
      });
  }

  async deleteEexTransaction(id) {
    return (await this.getCollection()).deleteOne({
      _id: new ObjectId(id)
    });
  }


}