import { OptionAction } from './actions';
import { JwtGuard } from '../auth';
import { OptionsService } from './options.service';
import { CreateOptionDto } from './dto/create-option.dto';
import { UpdateOptionDto } from './dto/update-option.dto';
import { Permission } from '@la-pasta/infrastructures/security';
import { getUser, setResponseController } from '@la-pasta/common';
import { Controller, Get, Post, Body, Patch, Param, UseGuards, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('options')
@Controller('options')
export class OptionsController {
  constructor(private readonly optionsService: OptionsService) { }

  @UseGuards(JwtGuard)
  @Post()
  create(@Body() createOptionDto: CreateOptionDto) {
    Permission.optionAuthorization(getUser(), OptionAction.CREATE);

    return this.optionsService.createOption(createOptionDto);
  }

  @UseGuards(JwtGuard)
  @Get()
  async findAll(@Query() query: QueryFilter) {
    Permission.optionAuthorization(getUser(), OptionAction.VIEW);

    const data = await this.optionsService.findAll({ filter: query });
    return setResponseController(data);
  }

  @Get('filters-elements')
  @UseGuards(JwtGuard)
  getElementForFilter(@Query() query: QueryFilter) {
    const { keyForFilters } = query;
    delete query.keyForFilters;
    return this.optionsService.getFilterElementsByKeys(query, keyForFilters?.split(',') ?? []);
  }

  @UseGuards(JwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    Permission.optionAuthorization(getUser(), OptionAction.VIEW);

    const data = await this.optionsService.findOne({ filter: { _id: id } });
    return setResponseController(data);
  }

  @UseGuards(JwtGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateOptionDto: UpdateOptionDto) {
    Permission.optionAuthorization(getUser(), OptionAction.UPDATE);

    return this.optionsService.updateProduct(id, updateOptionDto);
  }
}
