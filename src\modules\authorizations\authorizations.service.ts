import { Injectable } from '@nestjs/common';
import { BaseService } from '@la-pasta/common';
import { AuthorizationRepository } from './repository';

@Injectable()
export class AuthorizationsService extends BaseService {

  constructor(
    private readonly authorizationRepository: AuthorizationRepository
  ) {
    super(authorizationRepository);
  }

  findAutorizations(query: QueryOptions) {
    return this.authorizationRepository.findAll(query);
  }

}
