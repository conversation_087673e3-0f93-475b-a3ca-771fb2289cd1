import { Order } from '@la-pasta-module/order';
import { BaseService, setResponse, t } from '@la-pasta/common';
import { ClientProxy } from '@nestjs/microservices';
import { keyEventJDE } from '@la-pasta-module/order/events';
import { HttpException, HttpStatus, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { CompaniesService, Company } from '@la-pasta-module/companies';
import { lastValueFrom } from 'rxjs';
import { Balance } from '@la-pasta-module/balance/entities/balance.entity';

@Injectable()
export class JdeService extends BaseService {

    constructor(
        private companyService: CompaniesService,
        @Inject('JDE') private readonly JDEService: ClientProxy,
    ) { super(); }

    async reCreateOrderInJde(partialOrder: Partial<Order>) {
        const { appReference } = partialOrder;
        await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.CREATE_ORDER }, { appReference: appReference }));

        return setResponse(HttpStatus.OK, t('JDE_NUMBER'))
    }

    async getAccountBalance(companyId: string): Promise<Balance> {
        const company = await this.companyService.findOne({ filter: { _id: companyId } }) as unknown as Company;

        if (!company?.erpSoldToId) throw new NotFoundException(t('error.NO_COMPANY'))

        const balance = await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.GET_BALANCE }, { ...company }));

        if (!balance) throw new HttpException(t("CURRENT_BALANCE_NOT_FOUND"), HttpStatus.NOT_FOUND);

        return balance;
    }

    async paymentRequestToJde(ReloadBalnceId: string) {
        return await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.CREATE_RELOAD_BALANCE }, ReloadBalnceId));
    }

    async checkPaymentRequestToJde(ReloadBalnceId: string) {
        return await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.CHECK_STATUS_PAYMENT_RELOAD_BALNCE }, ReloadBalnceId));
    }

}
