import { MongoDatabase } from "@la-pasta/database";

export async function formatLocation(baseQuery: QueryFilter) {
    const database = MongoDatabase.getInstance();
    const collectionName = 'location';

    const datas = await (await database.getDatabase()).collection(collectionName).aggregate([
        { $match: baseQuery },
        {
            $group:
            {
                _id: "$region",
                cities: {
                    $push: "$cities",
                },
            },
        },
    ]).toArray();

    for (const data of datas) {
        let allCities = [];
        data?.cities.forEach(arr => allCities = allCities.concat(arr));

        const citiesToFormat = allCities.filter(elt => typeof elt == 'string');
        allCities = allCities.filter(elt => typeof elt == 'object');
        citiesToFormat.forEach(city => {
            if (!citiesToFormat.find(city => city?.name === city))
                allCities.push({ name: city })
        });

        const query = { ...baseQuery, region: data?._id };

        const locations = await (await database.getDatabase()).collection(collectionName).find(query).toArray();

        if (locations.length >= 2) {
            const currentLocation = locations[locations.length - 1];

            await (await database.getDatabase()).collection(collectionName).updateMany(
                { ...query }, { $set: { enable: false } });

            const res = await (await database.getDatabase()).collection(collectionName).updateOne(
                { _id: currentLocation?._id, ...query, enable: false }, { $set: { cities: allCities, enable: true } });
        }


    }
}