import { Modu<PERSON> } from '@nestjs/common';
import { config } from 'convict-config';
import { JwtService } from '@nestjs/jwt';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ExpressExchangeService } from './express-exchange.service';
import { ExpressExchangeController } from './express-exchange.controller';
import { ExpressExchangeRepository } from './repository/express-exchange.repository';

@Module({
  controllers: [ExpressExchangeController],
  providers: [ExpressExchangeService, ExpressExchangeRepository, JwtService],
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ])]
})
export class ExpressExchangeModule { }
