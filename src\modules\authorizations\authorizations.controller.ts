import { Body, Controller, Get, Param, <PERSON>, Post } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { AuthorizationsService } from './authorizations.service';
import { CreateProfileDto, ProfileService, UpdateProfileDto } from './profile';

@ApiTags('Authorizations')
@Controller('authorizations')
export class AuthorizationsController {
  constructor(private readonly authorizationsService: AuthorizationsService, private readonly profileSerivice: ProfileService) {}

  @ApiOkResponse({ description: 'Application authorizations' })
  @Get()
  findAll() {
    return this.authorizationsService.findAutorizations({ projection: { _id: 0 } });
  }

  @Get('profiles')
  findProfiles() {
    return this.profileSerivice.findProfiles({ projection: { enable: 0, created_at: 0 } });
  }

  @Post('profiles')
  createProfile(@Body() newProfile: CreateProfileDto) {
    return this.profileSerivice.create(newProfile);
  }

  @Patch('profiles/:id')
  updateProfile(@Param('id') id: string, @Body() profile: UpdateProfileDto) {
    return this.profileSerivice.update({ _id: id }, profile);
  }

}
