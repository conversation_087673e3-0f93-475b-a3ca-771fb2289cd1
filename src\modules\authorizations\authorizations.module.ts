import { Module } from '@nestjs/common';
import { AuthorizationsService } from './authorizations.service';
import { AuthorizationsController } from './authorizations.controller';
import { AuthorizationRepository } from './repository';
import { ProfileRepository, ProfileService } from './profile';

@Module({
  controllers: [AuthorizationsController],
  providers: [AuthorizationsService, AuthorizationRepository, ProfileService, ProfileRepository],
  exports: [AuthorizationsService, AuthorizationRepository, ProfileService, ProfileRepository]
})
export class AuthorizationsModule { }
