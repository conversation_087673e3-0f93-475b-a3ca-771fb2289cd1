import { <PERSON><PERSON> } from "@nestjs/common";
import { config } from "../../convict-config";
import { Db, MongoClient, MongoClientOptions } from "mongodb";

export class DatabaseMongoDB {

    private static instance: DatabaseMongoDB;

    private db!: Db;
    private options!: MongoClientOptions;

    private constructor(private readonly logger?: Logger) { }

    public static getInstance(): DatabaseMongoDB {
        return DatabaseMongoDB.instance ??= new DatabaseMongoDB();
    }

    async getDatabase(): Promise<Db> {
        return this.db ??= await this.setDatabase();
    }

    private async setDatabase(): Promise<Db> {
        if (config.get('db_images.auth.user') && config.get('db_images.auth.password')) {
            this.options.auth = {
                username: config.get('db_images.auth.user'),
                password: config.get('db_images.auth.password')
            }
        }

        try {
            const connection = await MongoClient.connect(this.getMongoDbURL(), this.options);
            return connection.db();
        } catch (error: unknown) {
            console.log('error', error);

            this.logger?.error(error);

        }
    }

    private getMongoDbURL(): string {
        return (config.get('db_images.auth.user') && config.get('db_images.auth.password'))
            ? `mongodb://${config.get('db_images.auth.user')}:${config.get('db_images.auth.password')}@${config.get('db_images.host')}/${config.get('db_images.name')}?retryWrites=true&w=majority`
            : `mongodb://${config.get('db_images.host')}/${config.get('db_images.name')}?retryWrites=true&w=majority`;
    }
}