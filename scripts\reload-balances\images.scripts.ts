import { ReloadBalance } from '@la-pasta-module/reload-balance/entities/reload-balance.entity';
import { MongoDatabase } from '@la-pasta/database';
import { ObjectId } from 'bson';
import { setLoader, stopLoader } from 'scripts/common';

const database = MongoDatabase.getInstance();
const collectionName = 'reload_balances';

export async function updateImagesPath() {
    try {

        setLoader('Insertion des chemin\n');

        const db = await database.getDatabase();

        const reloadBalance = await db.collection(collectionName).find({ 'payment.mode.icon': { $exists: true } }).toArray() as unknown as ReloadBalance[];

        const promises = reloadBalance.map(async reloadBalance => {
            if (!reloadBalance.payment.mode.icon.startsWith('.')) {
                reloadBalance.payment.mode.icon = `.${reloadBalance.payment.mode.icon}`;
            }
            if (reloadBalance.payment.mode.icon.includes('/images/payments-methodes/')) {
                reloadBalance.payment.mode.icon = reloadBalance.payment.mode.icon.replace('/images/payments-methodes/', '/icons/');
            }
            await db.collection(collectionName).updateOne(
                { _id: new ObjectId(reloadBalance._id) },
                { $set: { 'payment.mode.icon': reloadBalance.payment.mode.icon } }
            );

        })

        await Promise.all(promises);

        stopLoader(true);

    } catch (error) {
        console.error(error);
    }
}