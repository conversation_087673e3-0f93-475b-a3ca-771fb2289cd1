import { UnitAction } from '@la-pasta-module/cart';
import { PlanificationAction } from '@la-pasta-module/planification';
import { User, UserRole } from '@la-pasta-module/users';
import { AuthorizationInterface } from './authorization.interface';

class UnitAuthorization implements AuthorizationInterface {
  support(user: User, permission: string): boolean {
    return ([
      UnitAction.CREATE,
      UnitAction.DELETE,
      UnitAction.UPDATE,
      UnitAction.VIEW] as string[]).includes(permission) && user.authorizations.includes(permission);
  }

  authorise(user: User, permission: string): boolean {
    return user.roles.includes(UserRole.BACKOFFICE);
  }
}

export const UnitAuthorizationInstance = new UnitAuthorization();