import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Company } from "@la-pasta-module/companies";
import { UserRole } from "../roles";
import { i18nValidationMessage } from "nestjs-i18n";
import { UserExists } from "../validations";
import { Store } from "@la-pasta-module/cart";

export class CreateUserDto {
    @ApiProperty({ type: String })
    @IsEmail({}, { message: i18nValidationMessage('validation.INVALID_EMAIL') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    @UserExists()
    email: string;

    @ApiProperty({ type: Number })
    @IsNumber({}, { message: i18nValidationMessage('validation.BAD_PHONE') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    tel: number;

    @ApiProperty({ type: String })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    password: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    firstName: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    lastName: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: Address;

    @ApiProperty({ type: Number })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    category: number;

    @ApiPropertyOptional({ type: Number })
    @IsOptional()
    employeeType: number;

    @ApiPropertyOptional({ enum: UserRole })
    @IsOptional()
    roles: UserRole[];

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    authorizations: string[];

    @ApiPropertyOptional({ type: Number })
    @IsOptional()
    createAt?: number;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    updateAt?: number;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    afrilandKey?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    cni?: string;


    @IsOptional()
    profilePicture: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    nui?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    isRetired?: boolean;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    direction?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    service?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    position: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    matricule: string;

    @ApiPropertyOptional({ type: Boolean })
    @IsOptional()
    isCordoRH?: boolean;

    @ApiPropertyOptional({ type: Boolean })
    @IsOptional()
    isDRH?: boolean;

    @ApiPropertyOptional({ type: Number })
    @IsOptional()
    tonnage: Tonnage;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    socialReason: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    profession: string;

    @ApiPropertyOptional()
    @IsOptional()
    points: Point;

    @ApiPropertyOptional()
    @IsOptional()
    store: { _id: string, label: string };

    @ApiPropertyOptional()
    @IsOptional()
    company?: Company;

    @ApiPropertyOptional()
    @IsOptional()
    associatedCompanies: Partial<Company[]>;

    @ApiPropertyOptional()
    @IsOptional()
    defaultStore: Partial<Store>;


    @ApiPropertyOptional({ type: String })
    @IsOptional()
    parrainId?: string;

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    filleuls?: string[];

    @ApiPropertyOptional({ type: Number })
    @IsOptional()
    invitationStatus?: 100 | 200 | 300;
}