import { IsEmpty, <PERSON>NotEmpty, IsOptional } from "class-validator";
import { Attachment, FeedbackCategory, statusFeetback } from "../entities";
import { CompanyEmployee, User } from "@la-pasta-module/users";
import { Company } from "@la-pasta-module/companies";

export class CreateFeedbackDto {

  @IsEmpty()
  ref?: string;

  @IsNotEmpty()
  category: FeedbackCategory;

  @IsNotEmpty()
  subCategory: FeedbackCategory;

  @IsNotEmpty()
  message: string;

  @IsOptional()
  user: Partial<CompanyEmployee>;

  @IsEmpty()
  status?: statusFeetback;

  @IsEmpty()
  dates?: {
    resolved?: number;
    created?: number
  };

  @IsOptional()
  attachment?: Attachment;

  @IsOptional()
  company?: Company;
}
