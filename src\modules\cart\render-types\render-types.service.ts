import { Injectable } from '@nestjs/common';
import { BaseService } from 'src/common/base';
import { CreateRenderTypeDto } from './dto/create-render-type.dto';
import { UpdateRenderTypeDto } from './dto/update-render-type.dto';
import { RenderTypeRepository } from './repository';

@Injectable()
export class RenderTypesService extends BaseService {
  constructor(
    private readonly renderTypeRepository: RenderTypeRepository
  ) {
    super(renderTypeRepository);
  }
}
