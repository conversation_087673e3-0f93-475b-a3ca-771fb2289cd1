import { faker } from '@faker-js/faker';
import { MongoDatabase } from '@la-pasta/database';
import { BaseUser, UserCategory, UserRole } from '@la-pasta-module/users';
import * as bcrypt from 'bcrypt';
import moment from 'moment';
import { dropCollection, setLoader, stopLoader } from 'scripts/common';
import { getProfileAuthorization } from 'scripts/profiles';
import { getCompany } from 'scripts/company';

const database = MongoDatabase.getInstance();
const collectionName = 'users';
const collectionNameOrder = 'unpaid_orders';

const basicUsers: any = [
    
];

export function setRole(category: number) {
  if (category < 10) return [UserRole.CLIENT];

  if (category === UserCategory.Commercial) return [UserRole.BACKOFFICE, UserRole.CLIENT];

  return [UserRole.BACKOFFICE];
}

export function getUsesrCategories() {
  return Object.values(UserCategory).filter(
    (value) => typeof value != 'string',
  ) as number[];
}

let users: Omit<BaseUser, '_id'>[] = [];

export async function insertUsers(...userCategories: number[]) {
  try {
    await dropCollection(collectionName);

    setLoader('Insertion des utilisateurs\n');

    if (!userCategories.length) userCategories = getUsesrCategories();

    for await (const category of userCategories) {
      for (let i = 0; i <= 5; i++) {
        const gender = faker.name.sex() as 'female' | 'male';
        const firstName = faker.name.firstName(gender);
        const lastName = faker.name.lastName(gender);

        const user = {
          firstName,
          lastName,
          email: faker.internet.email(firstName, lastName),
          password: await bcrypt.hash('123456', 10),
          cni:
            faker.helpers.arrayElement(['LT', 'YD']) + faker.random.numeric(6),
          nui: faker.random.numeric(12),
          category,
          roles: setRole(category),
          authorizations: (await getProfileAuthorization(category)) ?? [
            'unauthorize',
          ],
          enable: true,
          address: {
            region: '',
            city: '',
            district: '',
          },
        }

        if (category === UserCategory.CompanyUser) {
          const company = await getCompany({ name: `Distributeur ${i + 1}` });
          user['company'] = { name: company?.name, category: company?.category, _id: company?._id.toString() };
          user['authorizations'] = await getProfileAuthorization(company?.category,);
        }
        users.push(user);
      }
    }

    users = users.concat(basicUsers);

    users.forEach((user) => {
      user.enable = true;
      user.created_at = moment().valueOf();
    });
    const insertedUsers = await (await database.getDatabase())
      .collection(collectionName)
      .insertMany(users);
    stopLoader(insertedUsers);
  } catch (error) {
    console.error(error);
  }
}

