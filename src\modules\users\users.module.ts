import { forwardRef, Module } from '@nestjs/common';
import { Permission } from '@la-pasta/infrastructures/security';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { UserRepository } from './repository/user.repository';
import { AuthorizationsModule, ProfileService } from '../authorizations';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { UserExistsRule } from './validations';
import { NotificationsModule } from '@la-pasta-module/notifications/notifications.module';
import { FirebaseCloud } from '@la-pasta/infrastructures/image';
import { CompaniesModule } from '@la-pasta-module/companies';

@Module({
  imports: [
    AuthorizationsModule,
    forwardRef(() => CompaniesModule),
    ClientsModule.register([
      { name: 'QUEUE', transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    UserRepository,
    Permission,
    UserExistsRule,
    FirebaseCloud,
  ],
  exports: [UsersService, UserRepository, UserExistsRule],
})
export class UsersModule {}