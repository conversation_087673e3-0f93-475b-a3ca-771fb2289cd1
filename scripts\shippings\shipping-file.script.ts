import { MongoDatabase } from '@la-pasta/database';
import { ParticularShipping } from '@la-pasta-module/price/shipping';
import { getStores } from 'scripts/stores';
import { setLoader, stopLoader } from 'scripts/common';
import moment from 'moment';
import { getDataInExcelFile } from '@la-pasta/common';

const database = MongoDatabase.getInstance();
const collectionName = 'shippings';

export async function insertShippingsDataFile() {
    try {
        setLoader('chargement des adresses depuis le fichier excel\n');
        const shippingsCollection = (await database.getDatabase()).collection(collectionName)

        const datas = getDataInExcelFile('la-pasta.shippings') as any;

        const stores = await getStores();

        let particularShippings: ParticularShipping[] = [];

        for (const data of datas) {
            const startRef = stores.find(store => store.storeRef == data.startRef_storeRef)
            const particularShipping: any = {
                startRef,
                label: data?.label,
                endRef: data?.endRef,
                amount: data?.amount,
                address: {
                    region: data?.address_region,
                    commercialRegion: data?.address_commercialRegion,
                    city: data?.address_city,
                    district: data?.address_district,
                },
                enable: true,
                create_at: moment().valueOf(),
            }
            const query = {
                'startRef.storeRef': startRef?.storeRef,
                endRef: data?.endRef,
            }
            const res = await shippingsCollection.updateOne(query, { $set: { ...particularShipping } }, { upsert: true });
            if (res?.matchedCount == 0 && !res?.upsertedCount)
                particularShippings.push(particularShipping);
        }

        if (particularShippings?.length) await shippingsCollection.insertMany(particularShippings as unknown[]);

        stopLoader(true);
        console.log('adresses insérés')

    } catch (error) {
        console.error('Erreur de lors l\'insertion des adresses');
        return error;
    }

}