import { <PERSON><PERSON>mail, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, IsString } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Company } from "@la-pasta-module/companies";
import { i18nValidationMessage } from "nestjs-i18n";
import { EmployeeEntity, UserRole } from "@la-pasta-module/users";

export class RegisterUserParticularDto {
    @ApiProperty({ type: String })
    @IsOptional({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    email: string;

    @ApiProperty({ type: String })
    @IsNumber({}, { message: i18nValidationMessage('validation.BAD_PHONE') })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    tel: number;

    @ApiProperty({ type: String })
    @IsString({ message: i18nValidationMessage('validation.INVALID_STRING') })
    @IsOptional({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    password: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    firstName: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    lastName: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    categoryType: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    localisation: string;

    @ApiPropertyOptional()
    @IsOptional()
    associatedCompanies: Partial<Company[]>

    @ApiPropertyOptional()
    @IsOptional()
    associatedSuppliers: Partial<Company[]>

    // Represents the commercial entity associated with the user, if any.
    @ApiPropertyOptional()
    @IsOptional()
    associatedCommercial: Partial<EmployeeEntity> = null;
    @IsOptional()
    profilePicture: string;

    @ApiProperty()
    @IsOptional()
    address: Address;

    @ApiProperty({ type: Number })
    @IsNotEmpty({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    category: number;

    @ApiProperty({ type: Number })
    @IsOptional({ message: i18nValidationMessage('validation.NOT_EMPTY') })
    employeeType: number;

    @ApiPropertyOptional({ enum: UserRole })
    @IsOptional()
    roles: UserRole[];

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    authorizations: string[];

    @ApiPropertyOptional({ type: Number })
    @IsOptional()
    createAt?: number;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    updateAt?: number;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    afrilandKey?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    cni?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    nui?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    isRetired?: boolean;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    direction?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    service?: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    position: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    matricule: string;

    @ApiPropertyOptional({ type: Boolean })
    @IsOptional()
    isCordoRH?: boolean;

    @ApiPropertyOptional({ type: Boolean })
    @IsOptional()
    isDRH?: boolean;

    @ApiPropertyOptional({ type: Number })
    @IsOptional()
    capacityTonnage: number;

    @ApiPropertyOptional({ type: Number })
    @IsOptional()
    capacityTonnageYear: number;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    socialReason: string;

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    profession: string;

    @ApiPropertyOptional()
    @IsOptional()
    points: Point;

    @ApiPropertyOptional()
    @IsOptional()
    store: { _id: string, label: string };

    company?: Company;
}